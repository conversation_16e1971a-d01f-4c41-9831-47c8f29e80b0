
using MediatR;
using Shared.Application;
using Tasks.Domain;

namespace Tasks.Application.Tasks.TaskReports;

public class DashboardTaskReportDto
{
    public int TotalTaskCount { get; set; }
    public int OpenTaskCount { get; set; }
    public int AssignedTaskCount { get; set; }
    public int UnassignedTaskCount { get; set; }
    public int ClosedTaskCount { get; set; }

    public List<TaskDto> CriticalTasks { get; set; } = [];
    public List<PersonTaskCountDto> TopUsersByTaskCount { get; set; } = [];

    public Dictionary<string, int> TaskCountByPriority { get; set; } = [];
    public Dictionary<string, int> TaskCountByStatus { get; set; } = [];
    public Dictionary<string, int> TaskCountByDepartment { get; set; } = [];
}

public class PersonTaskCountDto
{
    public Guid? UserId { get; set; }
    public string? FullName { get; set; }
    public int TaskCount { get; set; }
}
