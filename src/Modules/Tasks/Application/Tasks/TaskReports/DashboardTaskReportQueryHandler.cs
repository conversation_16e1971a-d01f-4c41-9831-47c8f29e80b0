using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Tasks.Application.Abstractions;
using Tasks.Domain;

namespace Tasks.Application.Tasks.TaskReports;

public class DashboardTaskReportQueryHandler : IRequestHandler<DashboardTaskReportQuery, Result<DashboardTaskReportDto>>
{
    private readonly ITaskDbContext _context;
    private readonly ISharedUserService _userService;
    private readonly ISharedDepartmentService _departmentService;

    public DashboardTaskReportQueryHandler(
        ITaskDbContext context,
        ISharedUserService userService,
        ISharedDepartmentService departmentService)
    {
        _context = context;
        _userService = userService;
        _departmentService = departmentService;
    }

    public async Task<Result<DashboardTaskReportDto>> Handle(DashboardTaskReportQuery request, CancellationToken cancellationToken)
    {
        var startDate = request.StartDate ?? DateTime.MinValue;
        var endDate = request.EndDate ?? DateTime.MaxValue;

        var query = _context.Tasks
        .Include(t => t.Status)
        .Include(t => t.TaskDepartments)
        .Include(t => t.Watchlist)
        .Where(t => t.InsertDate >= startDate && t.InsertDate <= endDate);

        var totalTaskCount = await query.CountAsync(cancellationToken);

        var assignedTaskCount = await query
            .Where(t => t.UserId != null && t.UserId != Guid.Empty)
            .CountAsync(cancellationToken);

        var unassignedTaskCount = await query
            .Where(t => t.UserId == null || t.UserId == Guid.Empty)
            .CountAsync(cancellationToken);

        var closedTaskCount = await query
            .Where(t => t.Status != null && t.Status.IsClosed)
            .CountAsync(cancellationToken);

        var openTaskCount = await query
            .Where(t => t.Status != null && !t.Status.IsClosed)
            .CountAsync(cancellationToken);

        var taskCountByStatus = await query
            .GroupBy(t => t.Status!.Name)
            .Select(g => new
            {
                Status = g.Key,
                Count = g.Count()
            })
            .ToDictionaryAsync(x => x.Status, x => x.Count, cancellationToken);

        var taskCountByDepartmentIds = await query
            .SelectMany(t => t.TaskDepartments)
            .GroupBy(td => td.DepartmentId)
            .Select(g => new
            {
                DepartmentId = g.Key,
                Count = g.Count()
            })
            .ToDictionaryAsync(x => x.DepartmentId, x => x.Count, cancellationToken);

        var departmentIds = taskCountByDepartmentIds.Keys.ToList();
        var departments = await _departmentService.GetDepartmentsByIdsAsync(departmentIds);
        var departmentDict = departments.ToDictionary(d => d.Id, d => d.Name);

        var taskCountByDepartment = taskCountByDepartmentIds
            .ToDictionary(
                x => departmentDict.TryGetValue(x.Key, out string? value) ? value : "Bilinmeyen Departman",
                x => x.Value);

        var criticalTasks = await query
            .Where(t => t.Priority == Priority.Critical)
            .Select(t => new TaskDto(
                t.Id,
                t.Title,
                t.Description,
                t.UserId,
                t.ReporterUserId,
                t.Priority,
                new StatusDto(t.Status.Id, t.Status.Name, t.Status.ColorCode, t.Status.Order, t.Status.IsClosed),
                t.EndDate,
                t.InsertDate,
                t.UpdateDate
            )
            {
                NotificationWayId = t.NotificationWayId,
                Departments = t.TaskDepartments.Select(d => new TaskDepartmentDto(d.DepartmentId, d.DepartmentName)).ToList(),
                Watchlist = t.Watchlist.Select(d => new WatchlistDto(d.UserId, "")).ToList()
            })
            .ToListAsync(cancellationToken);

        var topUsers = await query
            .GroupBy(t => t.UserId)
            .Select(g => new
            {
                UserId = g.Key,
                TaskCount = g.Count()
            })
            .OrderByDescending(x => x.TaskCount)
            .Take(10)
            .ToListAsync(cancellationToken);

        var userIds = topUsers.Select(x => x.UserId ?? Guid.Empty).Distinct().ToList();

        var users = await _userService.GetUsersByIdsAsync(userIds);

        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}".Trim());
        var topUserDtos = topUsers.Select(u => new PersonTaskCountDto
        {
            UserId = u.UserId,
            FullName = u.UserId.HasValue && userDict.ContainsKey(u.UserId.Value)
                ? userDict[u.UserId.Value]
                : "Bilinmiyor",
            TaskCount = u.TaskCount
        }).ToList();


        var taskCountByPriority = await query
            .GroupBy(t => t.Priority)
            .Select(g => new
            {
                Priority = g.Key.ToString(),
                Count = g.Count()
            })
            .ToDictionaryAsync(x => x.Priority, x => x.Count, cancellationToken);

        var dto = new DashboardTaskReportDto
        {
            TotalTaskCount = totalTaskCount,
            OpenTaskCount = openTaskCount,
            AssignedTaskCount = assignedTaskCount,
            UnassignedTaskCount = unassignedTaskCount,
            ClosedTaskCount = closedTaskCount,
            CriticalTasks = criticalTasks,
            TopUsersByTaskCount = topUserDtos,
            TaskCountByPriority = taskCountByPriority,
            TaskCountByStatus = taskCountByStatus,
            TaskCountByDepartment = taskCountByDepartment
        };

        return Result.Success(dto);
    }
}
