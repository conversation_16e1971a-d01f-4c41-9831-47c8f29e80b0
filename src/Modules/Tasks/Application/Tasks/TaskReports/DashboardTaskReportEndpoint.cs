using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Tasks.TaskReports;

public class DashboardTaskReportEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/tasks/reports/dashboard", async (
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new DashboardTaskReportQuery
            {
                StartDate = startDate,
                EndDate = endDate
            }, cancellationToken);

            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Tasks.Reports")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Reports")
        .Produces<DashboardTaskReportDto>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest);
    }
}
