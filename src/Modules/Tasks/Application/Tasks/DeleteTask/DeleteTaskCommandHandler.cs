using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Tasks.DeleteTask;

public class DeleteTaskCommandHandler(
    ITaskDbContext dbContext
) : IRequestHandler<DeleteTaskCommand, Result>
{
    private readonly ITaskDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteTaskCommand request, CancellationToken cancellationToken)
    {
        var task = await _dbContext.Tasks
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (task == null)
        {
            return Result.Failure("404", "Görev bulunamadı.");
        }
        _dbContext.Tasks.Remove(task);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
