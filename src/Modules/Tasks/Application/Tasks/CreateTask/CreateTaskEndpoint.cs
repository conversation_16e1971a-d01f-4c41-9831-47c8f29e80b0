using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;
using Tasks.Domain;

namespace Tasks.Application.Tasks.CreateTask;

public class CreateTaskEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/tasks/tasks", async (
            CreateTaskCommand command,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);

            return result.Match(
                id => Results.Created($"/api/v1/tasks/tasks/{id}", new { id }),
                CustomResults.Problem
            );
        })
        .WithTags("Tasks.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Management");
    }
}
