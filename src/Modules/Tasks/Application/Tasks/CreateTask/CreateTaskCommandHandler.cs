using MediatR;
using Shared.Application;
using Shared.Contracts;
using Tasks.Application.Abstractions;
using Tasks.Domain;
using Task = Tasks.Domain.Task;

namespace Tasks.Application.Tasks.CreateTask;

public class CreateTaskCommandHandler(
    ITaskDbContext dbContext,
    ISharedDepartmentService departmentService
) : IRequestHandler<CreateTaskCommand, Result<Guid>>
{
    private readonly ITaskDbContext _dbContext = dbContext;
    private readonly ISharedDepartmentService _departmentService = departmentService;

    public async Task<Result<Guid>> Handle(CreateTaskCommand request, CancellationToken cancellationToken)
    {
        var task = new Task
        {
            Id = Guid.NewGuid(),
            Title = request.Title,
            Description = request.Description,
            NotificationWayId = request.NotificationWayId,
            UserId = request.UserId,
            ReporterUserId = request.ReporterUserId,
            Priority = request.Priority,
            StatusId = request.StatusId,
            EndDate = request.EndDate,
            InsertDate = DateTime.Now,
            InsertUserId = request.ReporterUserId
        };
        _dbContext.Tasks.Add(task);
        if (request.DepartmentIds.Any())
        {
            var departments = (await _departmentService.GetDepartmentsByIdsAsync(request.DepartmentIds)).ToDictionary(x => x.Id, x => x.Name);
            foreach (var departmentId in request.DepartmentIds)
            {
                _dbContext.TaskDepartments.Add(new TaskDepartment
                {
                    Id = Guid.NewGuid(),
                    TaskId = task.Id,
                    DepartmentId = departmentId,
                    DepartmentName = departments.TryGetValue(departmentId, out string? value) ? value : "Bilinmeyen Departman",
                });
            }
        }
        if (request.WatchlistUserIds.Any())
        {
            foreach (var userId in request.WatchlistUserIds)
            {
                _dbContext.Watchlists.Add(new Watchlist
                {
                    Id = Guid.NewGuid(),
                    TaskId = task.Id,
                    UserId = userId
                });
            }
        }
        task.Raise(new TaskCreatedEvent(task.Id));
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(task.Id);
    }
}
