using FluentValidation;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Tasks.CreateTask;

public class CreateTaskCommandValidator : AbstractValidator<CreateTaskCommand>
{
    private readonly ITaskDbContext _dbContext;

    public CreateTaskCommandValidator(ITaskDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Görev başlığı boş olamaz.")
            .MaximumLength(200).WithMessage("Görev başlığı en fazla 200 karakter olabilir.");

        RuleFor(x => x.Description)
            .MaximumLength(4000).WithMessage("Görev açıklaması en fazla 4000 karakter olabilir.");

        RuleFor(x => x.Priority)
            .IsInEnum().WithMessage("Geçersiz öncelik değeri.");

        RuleFor(x => x.StatusId)
            .NotEmpty().WithMessage("Durum ID'si boş olamaz.")
            .MustAsync(async (statusId, cancellation) =>
            {
                var exists = await _dbContext.Statuses.FindAsync(new object[] { statusId }, cancellation) != null;
                return exists;
            }).WithMessage("Belirtilen durum bulunamadı.");

        RuleFor(x => x.EndDate)
            .GreaterThan(DateTime.Now.AddDays(1).Date).When(x => x.EndDate.HasValue)
            .WithMessage("Bitiş tarihi şu anki tarihten ileri bir tarih olmalıdır.");
    }
}
