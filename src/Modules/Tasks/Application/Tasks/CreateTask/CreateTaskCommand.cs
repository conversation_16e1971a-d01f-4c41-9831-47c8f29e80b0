using MediatR;
using Shared.Application;
using Tasks.Domain;

namespace Tasks.Application.Tasks.CreateTask;

public record CreateTaskCommand : IRequest<Result<Guid>>
{
    public required string Title { get; init; }
    public string? Description { get; init; }
    public Guid? NotificationWayId { get; init; }
    public Guid? UserId { get; init; }
    public Guid ReporterUserId { get; init; }
    public List<Guid> DepartmentIds { get; init; } = [];
    public Priority Priority { get; init; }
    public Guid StatusId { get; init; }
    public DateTime? EndDate { get; init; }
    public List<Guid> WatchlistUserIds { get; init; } = [];
}
