using MediatR;
using Shared.Application;
using Tasks.Domain;

namespace Tasks.Application.Tasks.ListTasks;

public record ListTasksQuery : IRequest<PagedResult<TaskDto>>
{
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public string? SearchTerm { get; init; }
    public string? Title { get; init; }
    public Guid? UserId { get; init; }
    public Guid? ReporterUserId { get; init; }
    public Priority? Priority { get; init; }
    public Guid? StatusId { get; init; }
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
    public Guid? DepartmentId { get; init; }
}
