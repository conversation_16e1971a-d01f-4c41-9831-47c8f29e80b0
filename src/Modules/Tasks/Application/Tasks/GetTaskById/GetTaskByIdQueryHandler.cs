using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Tasks.GetTaskById;

public class GetTaskByIdQueryHandler(
    ITaskDbContext dbContext,
    ISharedUserService userService,
    ISharedDepartmentService departmentService,
    ISharedCustomerService customerService
) : IRequestHandler<GetTaskByIdQuery, Result<TaskDto>>
{
    private readonly ITaskDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedDepartmentService _departmentService = departmentService;
    private readonly ISharedCustomerService _customerService = customerService;

    public async Task<Result<TaskDto>> Handle(GetTaskByIdQuery request, CancellationToken cancellationToken)
    {
        var task = await _dbContext.Tasks
            .Include(t => t.Status)
            .Include(t => t.TaskDepartments)
            .Include(t => t.Watchlist)
            .Include(t => t.Comments)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);
        if (task == null)
        {
            return Result.Failure<TaskDto>("404", "Görev bulunamadı.");
        }
        var userIds = new List<Guid>();
        if (task.UserId.HasValue)
        {
            userIds.Add(task.UserId.Value);
        }
        userIds.Add(task.ReporterUserId);
        userIds.AddRange(task.Watchlist.Select(w => w.UserId));
        userIds.AddRange(task.Comments.Select(c => c.UserId));
        userIds = userIds.Distinct().ToList();
        var users = await _userService.GetUsersByIdsAsync(userIds);
        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}");
        var notificationWay = await _customerService.GetNotificationWaysAsync();
        return Result.Success(new TaskDto(
            task.Id,
            task.Title,
            task.Description,
            task.UserId,
            task.ReporterUserId,
            task.Priority,
            new StatusDto(task.Status!.Id, task.Status.Name, task.Status.ColorCode, task.Status.Order, task.Status.IsClosed),
            task.EndDate,
            task.InsertDate,
            task.UpdateDate
        )
        {
            NotificationWayId = task.NotificationWayId,
            NotificationWay = notificationWay.Value.FirstOrDefault(x => x.Id == task.NotificationWayId)?.Name,
            UserFullName = task.UserId.HasValue && userDict.ContainsKey(task.UserId.Value) ? userDict[task.UserId.Value] : null,
            ReporterUserFullName = userDict.ContainsKey(task.ReporterUserId) ? userDict[task.ReporterUserId] : "Bilinmeyen Kullanıcı",
            Departments = [.. task.TaskDepartments.Select(td => new TaskDepartmentDto(
                td.DepartmentId,
                td.DepartmentName
            ))],
            Watchlist = [.. task.Watchlist.Select(w => new WatchlistDto(
                w.UserId,
                userDict.TryGetValue(w.UserId, out string? value) ? value : null
            ))],
            Comments = [.. task.Comments.Select(c => new CommentDto(
                c.Id,
                c.TaskId,
                c.UserId,
                userDict.TryGetValue(c.UserId, out string? value) ? value : "Bilinmeyen Kullanıcı",
                c.Content,
                c.InsertDate
            )).OrderByDescending(c => c.InsertDate)]
        });
    }
}
