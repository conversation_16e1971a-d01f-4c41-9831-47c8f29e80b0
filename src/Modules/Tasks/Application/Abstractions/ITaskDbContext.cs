using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Data;
using Tasks.Domain;
using Task = Tasks.Domain.Task;

namespace Tasks.Application.Abstractions;

public interface ITaskDbContext : IBaseDbContext
{
    DbSet<Task> Tasks { get; }
    DbSet<Status> Statuses { get; }
    DbSet<TaskDepartment> TaskDepartments { get; }
    DbSet<Watchlist> Watchlists { get; }
    DbSet<Comment> Comments { get; }
}
