using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Comments.AddComment;

public class AddCommentCommandValidator : AbstractValidator<AddCommentCommand>
{
    private readonly ITaskDbContext _dbContext;

    public AddCommentCommandValidator(ITaskDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Görev ID'si boş olamaz.")
            .MustAsync(async (taskId, cancellation) =>
            {
                var exists = await _dbContext.Tasks.AnyAsync(t => t.Id == taskId, cancellation);
                return exists;
            }).WithMessage("Belirtilen görev bulunamadı.");

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("Kullanıcı ID'si boş olamaz.");

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("Yorum içeriği boş olamaz.")
            .MaximumLength(4000).WithMessage("Yorum içeriği en fazla 4000 karakter olabilir.");
    }
}
