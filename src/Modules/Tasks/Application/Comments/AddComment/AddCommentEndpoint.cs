using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Comments.AddComment;

public class AddCommentEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/tasks/tasks/{taskId}/comments", async (
            Guid taskId,
            AddCommentRequest request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new AddCommentCommand
            {
                TaskId = taskId,
                UserId = request.UserId,
                Content = request.Content
            };
            var result = await sender.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/tasks/tasks/{taskId}/comments/{id}", new { id }),
                CustomResults.Problem
            );
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Comments");
    }
}

public record AddCommentRequest(Guid UserId, string Content);
