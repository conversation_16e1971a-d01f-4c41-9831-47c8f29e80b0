using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Comments.UpdateComment;

public class UpdateCommentCommandHandler(
    ITaskDbContext dbContext
) : IRequestHandler<UpdateCommentCommand, Result>
{
    private readonly ITaskDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateCommentCommand request, CancellationToken cancellationToken)
    {
        var comment = await _dbContext.Comments
            .FirstOrDefaultAsync(c => c.Id == request.CommentId && c.TaskId == request.TaskId, cancellationToken);
        if (comment == null)
        {
            return Result.Failure("404", "Yorum bulunamadı.");
        }
        if (comment.UserId != request.UserId)
        {
            return Result.Failure("403", "Bu yorumu güncelleme yetkiniz yok.");
        }
        comment.Content = request.Content;
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
