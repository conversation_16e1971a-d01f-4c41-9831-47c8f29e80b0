using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Comments.UpdateComment;

public class UpdateCommentEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/tasks/tasks/{taskId}/comments/{commentId}", async (
            Guid taskId,
            Guid commentId,
            UpdateCommentRequest request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateCommentCommand
            {
                TaskId = taskId,
                CommentId = commentId,
                UserId = request.UserId,
                Content = request.Content
            };
            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Comments");
    }
}

public record UpdateCommentRequest(Guid UserId, string Content);
