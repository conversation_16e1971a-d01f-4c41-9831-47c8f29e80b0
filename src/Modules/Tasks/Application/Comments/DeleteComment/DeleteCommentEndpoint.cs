using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Comments.DeleteComment;

public class DeleteCommentEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("/api/v1/tasks/tasks/{taskId}/comments/{commentId}", async (
            Guid taskId,
            Guid commentId,
            Guid userId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new DeleteCommentCommand(taskId, commentId, userId);
            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Comments");
    }
}
