using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Comments.DeleteComment;

public class DeleteCommentCommandHandler(
    ITaskDbContext dbContext
) : IRequestHandler<DeleteCommentCommand, Result>
{
    private readonly ITaskDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteCommentCommand request, CancellationToken cancellationToken)
    {
        var task = await _dbContext.Tasks.FirstOrDefaultAsync(t => t.Id == request.TaskId, cancellationToken);
        if (task == null)
        {
            return Result.Failure("404", "Görev bulunamadı.");
        }
        var comment = await _dbContext.Comments
            .FirstOrDefaultAsync(c => c.Id == request.CommentId && c.TaskId == request.TaskId, cancellationToken);
        if (comment == null)
        {
            return Result.Failure("404", "Yorum bulunamadı.");
        }
        if (comment.UserId != request.UserId && task.ReporterUserId != request.UserId && task.UserId != request.UserId)
        {
            return Result.Failure("403", "Bu yorumu silme yetkiniz yok.");
        }
        _dbContext.Comments.Remove(comment);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
