using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Comments.ListComments;

public class ListCommentsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/tasks/tasks/{taskId}/comments", async (
            Guid taskId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListCommentsQuery(taskId);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Comments");
    }
}
