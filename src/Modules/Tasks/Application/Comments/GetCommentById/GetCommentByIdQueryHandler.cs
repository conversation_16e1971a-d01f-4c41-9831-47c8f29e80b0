using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Comments.GetCommentById;

public class GetCommentByIdQueryHandler(
    ITaskDbContext dbContext,
    ISharedUserService userService
) : IRequestHandler<GetCommentByIdQuery, Result<CommentDto>>
{
    private readonly ITaskDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<CommentDto>> Handle(GetCommentByIdQuery request, CancellationToken cancellationToken)
    {
        var comment = await _dbContext.Comments
            .FirstOrDefaultAsync(c => c.Id == request.CommentId && c.TaskId == request.TaskId, cancellationToken);
        if (comment == null)
        {
            return Result.Failure<CommentDto>("404", "Yorum bulunamadı.");
        }
        var userResult = await _userService.GetUserAsync(comment.UserId);
        var userFullName = userResult.IsSuccess
            ? $"{userResult.Value.Name} {userResult.Value.Surname}"
            : "Bilinmeyen Kullanıcı";
        var commentDto = new CommentDto(
            comment.Id,
            comment.TaskId,
            comment.UserId,
            userFullName,
            comment.Content,
            comment.InsertDate
        );
        return Result.Success(commentDto);
    }
}
