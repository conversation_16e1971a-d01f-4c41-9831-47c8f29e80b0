using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Comments.GetCommentById;

public class GetCommentByIdEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/tasks/tasks/{taskId}/comments/{commentId}", async (
            Guid taskId,
            Guid commentId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCommentByIdQuery(taskId, commentId);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .RequireAuthorization("Tasks.Comments");
    }
}
