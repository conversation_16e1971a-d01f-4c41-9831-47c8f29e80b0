using Tasks.Domain;

namespace Tasks.Application;

public record TaskDto(
    Guid Id,
    string Title,
    string? Description,
    Guid? UserId,
    Guid ReporterUserId,
    Priority Priority,
    StatusDto Status,
    DateTime? EndDate,
    DateTime InsertDate,
    DateTime? UpdateDate
)
{
    public string? NotificationWay { get; set; }
    public Guid? NotificationWayId { get; set; }
    public string? UserFullName { get; set; }
    public string? ReporterUserFullName { get; set; }
    public List<TaskDepartmentDto> Departments { get; set; }
    public List<WatchlistDto> Watchlist { get; set; }
    public List<CommentDto> Comments { get; set; }
}
