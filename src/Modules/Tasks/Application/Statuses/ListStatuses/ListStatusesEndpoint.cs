using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Tasks.Application.Statuses.ListStatuses;

public class ListStatusesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/tasks/statuses", async (
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListStatusesQuery();
            var result = await sender.Send(query, cancellationToken);
            return Results.Ok(result);
        })
        .WithTags("Tasks.Statuses")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
