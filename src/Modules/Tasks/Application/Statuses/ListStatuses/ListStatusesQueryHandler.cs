using MediatR;
using Microsoft.EntityFrameworkCore;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Statuses.ListStatuses;

public class ListStatusesQueryHandler(
    ITaskDbContext dbContext
) : IRequestHandler<ListStatusesQuery, List<StatusDto>>
{
    private readonly ITaskDbContext _dbContext = dbContext;

    public async Task<List<StatusDto>> Handle(ListStatusesQuery request, CancellationToken cancellationToken)
    {
        var statuses = await _dbContext.Statuses
            .OrderBy(s => s.Order)
            .ToListAsync(cancellationToken);

        return statuses.Select(s => new StatusDto(
            s.Id,
            s.Name,
            s.ColorCode,
            s.Order,
            s.IsClosed
        )).ToList();
    }
}
