using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tasks.Domain;

namespace Tasks.Infrastructure.Data.Configuration;

/// <summary>
/// Comment entity konfigürasyonu
/// </summary>
public class CommentConfiguration : IEntityTypeConfiguration<Comment>
{
    /// <summary>
    /// Comment entity konfigürasyonunu uygular
    /// </summary>
    public void Configure(EntityTypeBuilder<Comment> builder)
    {
        builder.ToTable("Comment", "Tasks");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.TaskId)
            .IsRequired();

        builder.Property(c => c.UserId)
            .IsRequired();

        builder.Property(c => c.Content)
            .IsRequired()
            .HasMaxLength(4000);
    }
}
