using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tasks.Domain;

namespace Tasks.Infrastructure.Data.Configuration;

public class WatchlistConfiguration : IEntityTypeConfiguration<Watchlist>
{
    public void Configure(EntityTypeBuilder<Watchlist> builder)
    {
        builder.ToTable("Watchlist", "Tasks");

        builder.HasKey(w => w.Id);

        builder.Property(w => w.TaskId)
            .IsRequired();

        builder.Property(w => w.UserId)
            .IsRequired();

        // Aynı görev-kullanıcı izleme ilişkisinin tekrarlanmaması için unique index
        builder.HasIndex(w => new { w.TaskId, w.UserId })
            .IsUnique();
    }
}
