using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tasks.Domain;

namespace Tasks.Infrastructure.Data.Configuration;

/// <summary>
/// Status entity konfigürasyonu
/// </summary>
public class StatusConfiguration : IEntityTypeConfiguration<Status>
{
    /// <summary>
    /// Status entity konfigürasyonunu uygular
    /// </summary>
    public void Configure(EntityTypeBuilder<Status> builder)
    {
        builder.ToTable("Status", "Tasks");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(s => s.ColorCode)
            .IsRequired()
            .HasMaxLength(7);

        builder.Property(s => s.Order)
            .IsRequired();

        // Varsayılan durumlar
        builder.HasData(
            new Status { Id = Guid.Parse("f8d5fa00-f81a-4d9e-9a0a-1e0f98ab2c5c"), Name = "Yeni", ColorCode = "#3498db", Order = 1 },
            new Status { Id = Guid.Parse("2f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"), Name = "Devam Ediyor", ColorCode = "#f39c12", Order = 2 },
            new Status { Id = Guid.Parse("3f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"), Name = "Tamamlandı", ColorCode = "#2ecc71", Order = 3 },
            new Status { Id = Guid.Parse("4f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"), Name = "İptal Edildi", ColorCode = "#e74c3c", Order = 4 }
        );
    }
}
