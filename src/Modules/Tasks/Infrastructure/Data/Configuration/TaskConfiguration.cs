using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

using Task = Tasks.Domain.Task;

namespace Tasks.Infrastructure.Data.Configuration;

/// <summary>
/// Task entity konfigürasyonu
/// </summary>
public class TaskConfiguration : IEntityTypeConfiguration<Task>
{
    /// <summary>
    /// Task entity konfigürasyonunu uygular
    /// </summary>
    public void Configure(EntityTypeBuilder<Task> builder)
    {
        builder.ToTable("Task", "Tasks");

        builder.HasKey(t => t.Id);

        builder.Property(t => t.Title)
            .HasMaxLength(200);

        builder.Property(t => t.Description)
            .HasMaxLength(4000);

        builder.Property(t => t.Priority)
            .IsRequired();

        builder.Property(t => t.InsertDate)
            .IsRequired()
            .HasDefaultValueSql("GETDATE()");

        builder.HasOne(t => t.Status)
            .WithMany(s => s.Tasks)
            .HasForeignKey(t => t.StatusId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(t => t.TaskDepartments)
            .WithOne(td => td.Task)
            .HasForeignKey(td => td.TaskId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Watchlist)
            .WithOne(w => w.Task)
            .HasForeignKey(w => w.TaskId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Comments)
            .WithOne(c => c.Task)
            .HasForeignKey(c => c.TaskId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
