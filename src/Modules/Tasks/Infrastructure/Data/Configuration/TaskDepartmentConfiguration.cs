using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tasks.Domain;

namespace Tasks.Infrastructure.Data.Configuration;

/// <summary>
/// TaskDepartment entity konfigürasyonu
/// </summary>
public class TaskDepartmentConfiguration : IEntityTypeConfiguration<TaskDepartment>
{
    /// <summary>
    /// TaskDepartment entity konfigürasyonunu uygular
    /// </summary>
    public void Configure(EntityTypeBuilder<TaskDepartment> builder)
    {
        builder.ToTable("TaskDepartment", "Tasks");

        builder.HasKey(td => td.Id);

        builder.Property(td => td.TaskId)
            .IsRequired();

        builder.Property(td => td.DepartmentId)
            .IsRequired();

        // Aynı görev-departman ilişkisinin tekrarlanmaması için unique index
        builder.HasIndex(td => new { td.TaskId, td.DepartmentId })
            .IsUnique();
    }
}
