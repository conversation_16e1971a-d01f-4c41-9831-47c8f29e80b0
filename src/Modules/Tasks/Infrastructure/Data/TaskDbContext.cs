using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;
using Tasks.Application.Abstractions;
using Tasks.Domain;
using Task = Tasks.Domain.Task;

namespace Tasks.Infrastructure.Data;

public class TaskDbContext(
    DbContextOptions<TaskDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), ITaskDbContext
{
    public DbSet<Task> Tasks { get; set; }
    public DbSet<Status> Statuses { get; set; }
    public DbSet<TaskDepartment> TaskDepartments { get; set; }
    public DbSet<Watchlist> Watchlists { get; set; }
    public DbSet<Comment> Comments { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("Tasks");
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
