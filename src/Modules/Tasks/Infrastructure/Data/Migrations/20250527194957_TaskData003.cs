﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tasks.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class TaskData003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NotificationWay",
                schema: "Tasks",
                table: "Task");

            migrationBuilder.AddColumn<Guid>(
                name: "NotificationWayId",
                schema: "Tasks",
                table: "Task",
                type: "uniqueidentifier",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NotificationWayId",
                schema: "Tasks",
                table: "Task");

            migrationBuilder.AddColumn<int>(
                name: "NotificationWay",
                schema: "Tasks",
                table: "Task",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
