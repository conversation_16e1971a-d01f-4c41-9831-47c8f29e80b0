﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tasks.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class TaskData002 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsClosed",
                schema: "Tasks",
                table: "Status",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                schema: "Tasks",
                table: "Status",
                keyColumn: "Id",
                keyValue: new Guid("2f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                column: "IsClosed",
                value: false);

            migrationBuilder.UpdateData(
                schema: "Tasks",
                table: "Status",
                keyColumn: "Id",
                keyValue: new Guid("3f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                column: "IsClosed",
                value: false);

            migrationBuilder.UpdateData(
                schema: "Tasks",
                table: "Status",
                keyColumn: "Id",
                keyValue: new Guid("4f6b6e3d-6b2f-4f0f-8f2f-2b2f6b2f6b2f"),
                column: "IsClosed",
                value: false);

            migrationBuilder.UpdateData(
                schema: "Tasks",
                table: "Status",
                keyColumn: "Id",
                keyValue: new Guid("f8d5fa00-f81a-4d9e-9a0a-1e0f98ab2c5c"),
                column: "IsClosed",
                value: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsClosed",
                schema: "Tasks",
                table: "Status");
        }
    }
}
