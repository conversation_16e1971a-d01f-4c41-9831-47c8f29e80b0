using Shared.Domain;

namespace Tasks.Domain;

public class Task : AuditableEntity
{
    public Guid Id { get; set; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public Guid? NotificationWayId { get; set; }
    public Guid? UserId { get; set; }
    public Guid ReporterUserId { get; set; }
    public Priority Priority { get; set; }
    public Guid StatusId { get; set; }
    public DateTime? EndDate { get; set; }
    public Status? Status { get; set; }
    public List<TaskDepartment> TaskDepartments { get; set; } = [];
    public List<Watchlist> Watchlist { get; set; } = [];
    public List<Comment> Comments { get; set; } = [];
}
