using Shared.Domain;

namespace General.Domain;

public class City : AuditableEntity
{
    private City(string name, Guid stateId, bool isActive)
    {
        Name = name;
        StateId = stateId;
        IsActive = isActive;
    }

    public Guid Id { get; set; }
    public string Name { get; private set; }
    public Guid StateId { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation property
    public virtual State State { get; private set; }

    public static City Create(string name, Guid stateId, bool isActive = true)
    {
        var city = new City(name, stateId, isActive);
        return city;
    }

    public void Update(string name, Guid stateId, bool isActive)
    {
        Name = name;
        StateId = stateId;
        IsActive = isActive;
    }
}
