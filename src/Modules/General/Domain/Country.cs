using Shared.Domain;

namespace General.Domain;

public class Country : AuditableEntity
{
    private Country(string name, string code, string phoneCode, bool isActive)
    {
        Name = name;
        Code = code;
        PhoneCode = phoneCode;
        IsActive = isActive;
        States = new List<State>();
    }

    public Guid Id { get; set; }
    public string Name { get; private set; }
    public string Code { get; private set; }  // ISO 3166-1 alpha-2 kodu (TR, US, DE vb.)
    public string PhoneCode { get; private set; }  // Ülke telefon kodu (+90, +1 vb.)
    public bool IsActive { get; private set; }

    // Navigation property
    public virtual ICollection<State> States { get; private set; }

    public static Country Create(string name, string code, string phoneCode, bool isActive = true)
    {
        var country = new Country(name, code, phoneCode, isActive);
        return country;
    }

    public void Update(string name, string code, string phoneCode, bool isActive)
    {
        Name = name;
        Code = code;
        PhoneCode = phoneCode;
        IsActive = isActive;
    }
}
