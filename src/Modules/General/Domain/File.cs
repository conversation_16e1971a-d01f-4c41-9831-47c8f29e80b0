using Shared.Domain;

namespace General.Domain;

public class File : AuditableEntity
{
    private File(
        string fileName,
        string originalFileName,
        string fileExtension,
        string mimeType,
        long fileSizeInBytes,
        Guid folderId,
        string storagePath)
    {
        FileName = fileName;
        OriginalFileName = originalFileName;
        FileExtension = fileExtension;
        MimeType = mimeType;
        FileSizeInBytes = fileSizeInBytes;
        FolderId = folderId;
        StoragePath = storagePath;
        Metadata = new Dictionary<string, object>();
    }

    public Guid Id { get; set; }
    public string FileName { get; private set; }
    public string OriginalFileName { get; private set; }
    public string FileExtension { get; private set; }
    public string MimeType { get; private set; }
    public long FileSizeInBytes { get; private set; }
    public Guid FolderId { get; private set; }
    public virtual Folder Folder { get; private set; }
    public string StoragePath { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public static File Create(
        string fileName,
        string originalFileName,
        string fileExtension,
        string mimeType,
        long fileSizeInBytes,
        Guid folderId,
        string storagePath)
    {
        var file = new File(
            fileName,
            originalFileName,
            fileExtension,
            mimeType,
            fileSizeInBytes,
            folderId,
            storagePath);
        
        return file;
    }

    public void UpdateMetadata(Dictionary<string, object> metadata)
    {
        Metadata = metadata;
    }

    public void MoveToFolder(Guid newFolderId, string newStoragePath)
    {
        FolderId = newFolderId;
        StoragePath = newStoragePath;
    }
}
