using Shared.Domain;

namespace General.Domain;

public class State : AuditableEntity
{
    private State(string name, string code, Guid countryId, bool isActive)
    {
        Name = name;
        Code = code;
        CountryId = countryId;
        IsActive = isActive;
        Cities = new List<City>();
    }

    public Guid Id { get; set; }
    public string Name { get; private set; }
    public string Code { get; private set; }  // Eyalet/Bölge kodu
    public Guid CountryId { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation properties
    public virtual Country Country { get; private set; }
    public virtual ICollection<City> Cities { get; private set; }

    public static State Create(string name, string code, Guid countryId, bool isActive = true)
    {
        var state = new State(name, code, countryId, isActive);
        return state;
    }

    public void Update(string name, string code, Guid countryId, bool isActive)
    {
        Name = name;
        Code = code;
        CountryId = countryId;
        IsActive = isActive;
    }
}
