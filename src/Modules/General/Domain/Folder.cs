using Shared.Domain;

namespace General.Domain;

public class Folder : AuditableEntity
{
    private Folder(
        string name,
        Guid? parentFolderId,
        string path)
    {
        Name = name;
        ParentFolderId = parentFolderId;
        Path = path;
        Attributes = [];
    }

    public Guid Id { get; set; }
    public string Name { get; private set; }
    public Guid? ParentFolderId { get; private set; }
    public Folder? ParentFolder { get; private set; }
    public string Path { get; private set; }
    public Dictionary<string, object> Attributes { get; private set; }
    public List<Folder> SubFolders { get; private set; } = [];
    public List<File> Files { get; private set; } = [];

    public static Folder Create(
        string name,
        Guid? parentFolderId,
        string path)
    {
        var folder = new Folder(
            name,
            parentFolderId,
            path);

        return folder;
    }

    public void Update(string name)
    {
        Name = name;
    }

    public void UpdatePath(string path)
    {
        Path = path;
    }

    public void MoveToParent(Guid? newParentFolderId, string newPath)
    {
        ParentFolderId = newParentFolderId;
        Path = newPath;
    }

    public void UpdateAttributes(Dictionary<string, object> attributes)
    {
        Attributes = attributes;
    }
}
