using Shared.Domain;

namespace General.Domain;

public class Language : AuditableEntity
{
    private Language(string code, string name, bool isActive, string? flagIcon)
    {
        Code = code;
        Name = name;
        IsActive = isActive;
        FlagIcon = flagIcon;
    }

    public Guid Id { get; set; }
    public string Code { get; private set; }  // ISO 639-1 kodu (tr, en, de, fr, vb.)
    public string Name { get; private set; }  // <PERSON><PERSON> adı (Türkçe, English, Deutsch, vb.)
    public bool IsActive { get; private set; }
    public string? FlagIcon { get; private set; }  // Dil bayrağı için ikon

    public static Language Create(string code, string name, bool isActive, string? flagIcon)
    {
        var language = new Language(code, name, isActive, flagIcon);
        return language;
    }

    public void Update(string code, string name, bool isActive, string? flagIcon)
    {
        Code = code;
        Name = name;
        IsActive = isActive;
        FlagIcon = flagIcon;
    }
}
