using Shared.Application;

namespace General.Domain;

public static class GeneralErrors
{
    // Country errors
    public static Error CountryNotFound(Guid id) => Error.NotFound(
        "General.Country.NotFound",
        $"Ülke bulunamadı. ID: {id}");

    public static Error CountryNotFound(string message) => Error.NotFound(
        "General.Country.NotFound",
        message);

    public static Error CountryCodeExists(string code) => Error.Validation(
        "General.Country.CodeExists",
        $"'{code}' kodlu ülke zaten mevcut.");

    // State errors
    public static Error StateNotFound(Guid id) => Error.NotFound(
        "General.State.NotFound",
        $"Eyalet/Bölge bulunamadı. ID: {id}");

    public static Error StateNotFound(string message) => Error.NotFound(
        "General.State.NotFound",
        message);

    public static Error StateCodeExists(string code, Guid countryId) => Error.Validation(
        "General.State.CodeExists",
        $"Bu ülkede '{code}' kodlu eyalet/bölge zaten mevcut.");

    // City errors
    public static Error CityNotFound(Guid id) => Error.NotFound(
        "General.City.NotFound",
        $"Şehir bulunamadı. ID: {id}");

    public static Error CityNotFound(string message) => Error.NotFound(
        "General.City.NotFound",
        message);

    public static Error CityNameExists(string name, Guid stateId) => Error.Validation(
        "General.City.NameExists",
        $"Bu eyalette/bölgede '{name}' adlı şehir zaten mevcut.");

    // Dependency errors
    public static Error HasDependentStates => Error.Validation(
        "General.Country.HasDependentStates",
        "Bu ülkeye bağlı eyaletler/bölgeler bulunmaktadır. Önce bunları silmelisiniz.");

    public static Error HasDependentCities => Error.Validation(
        "General.State.HasDependentCities",
        "Bu eyalete/bölgeye bağlı şehirler bulunmaktadır. Önce bunları silmelisiniz.");

    // File errors
    public static Error FileNotFound(Guid id) => Error.NotFound(
        "General.File.NotFound",
        $"Dosya bulunamadı. ID: {id}");

    public static Error FileNotFound(string message) => Error.NotFound(
        "General.File.NotFound",
        message);

    public static Error FileUploadFailed(string message) => Error.Failure(
        "General.File.UploadFailed",
        $"Dosya yükleme başarısız: {message}");

    public static Error FileDeleteFailed(string message) => Error.Failure(
        "General.File.DeleteFailed",
        $"Dosya silme başarısız: {message}");

    // Folder errors
    public static Error FolderNotFound(Guid id) => Error.NotFound(
        "General.Folder.NotFound",
        $"Klasör bulunamadı. ID: {id}");

    public static Error FolderNotFound(string message) => Error.NotFound(
        "General.Folder.NotFound",
        message);

    public static Error FolderNameExists(string name, Guid? parentFolderId) => Error.Validation(
        "General.Folder.NameExists",
        $"Bu konumda '{name}' adlı klasör zaten mevcut.");

    public static Error FolderCreateFailed(string message) => Error.Failure(
        "General.Folder.CreateFailed",
        $"Klasör oluşturma başarısız: {message}");

    public static Error FolderDeleteFailed(string message) => Error.Failure(
        "General.Folder.DeleteFailed",
        $"Klasör silme başarısız: {message}");

    public static Error HasDependentFiles => Error.Validation(
        "General.Folder.HasDependentFiles",
        "Bu klasörde dosyalar bulunmaktadır. Önce bunları silmelisiniz veya recursive silme seçeneğini kullanınız.");

    public static Error HasDependentFolders => Error.Validation(
        "General.Folder.HasDependentFolders",
        "Bu klasörde alt klasörler bulunmaktadır. Önce bunları silmelisiniz veya recursive silme seçeneğini kullanınız.");
}
