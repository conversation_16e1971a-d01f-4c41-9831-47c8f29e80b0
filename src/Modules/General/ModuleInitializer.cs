using System.Reflection;
using FluentValidation;
using General.Application.Abstractions;
using General.Infrastructure.Data;
using General.Infrastructure.Services;
using Microsoft.AspNetCore.OData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Endpoints;

namespace General;

public static class ModuleInitializer
{
    public static IServiceCollection AddGeneralModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("ApplicationConnection");
        var assembly = Assembly.GetExecutingAssembly();
        services.AddDbContext<GeneralDbContext>(opt =>
            opt.UseSqlServer(connectionString, b =>
            {
                b.MigrationsHistoryTable(HistoryRepository.DefaultTableName, "General");
                b.MigrationsAssembly(assembly.FullName);
                b.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            }));
        services.AddScoped<IGeneralDbContext>(sp => sp.GetRequiredService<GeneralDbContext>());
        services.AddScoped<IStorageService, StorageService>();
        services.AddEndpoints(assembly);
        services.AddMediatR(config => config.RegisterServicesFromAssembly(assembly));
        services.AddValidatorsFromAssembly(assembly, includeInternalTypes: true);
        services.AddControllers().AddOData(options => options.Select().Filter().OrderBy().Expand().Count().SetMaxTop(100));

        return services;
    }
}
