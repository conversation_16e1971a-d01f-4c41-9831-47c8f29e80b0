using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Cities.DeleteCity;

internal sealed class DeleteCityCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<DeleteCityCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteCityCommand request, CancellationToken cancellationToken)
    {
        var city = await _dbContext.City
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (city == null)
        {
            return Result.Failure(GeneralErrors.CityNotFound(request.Id));
        }

        _dbContext.City.Remove(city);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
