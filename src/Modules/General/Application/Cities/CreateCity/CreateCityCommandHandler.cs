using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Cities.CreateCity;

internal sealed class CreateCityCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<CreateCityCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateCityCommand request, CancellationToken cancellationToken)
    {
        // Eyaletin varlığını kontrol et
        var state = await _dbContext.State
            .FirstOrDefaultAsync(s => s.Id == request.StateId, cancellationToken);

        if (state == null)
        {
            return Result.Failure<Guid>(GeneralErrors.StateNotFound(request.StateId));
        }

        // İsim benzersizliğini kontrol et (aynı eyalet içinde)
        var existingCity = await _dbContext.City
            .FirstOrDefaultAsync(c => c.StateId == request.StateId && c.Name == request.Name, cancellationToken);

        if (existingCity != null)
        {
            return Result.Failure<Guid>(GeneralErrors.CityNameExists(request.Name, request.StateId));
        }

        var city = City.Create(
            request.Name,
            request.StateId,
            request.IsActive);

        _dbContext.City.Add(city);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(city.Id);
    }
}
