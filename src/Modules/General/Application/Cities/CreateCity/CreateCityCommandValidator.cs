using FluentValidation;

namespace General.Application.Cities.CreateCity;

internal sealed class CreateCityCommandValidator : AbstractValidator<CreateCityCommand>
{
    public CreateCityCommandValidator()
    {
        RuleFor(x => x.StateId)
            .NotEmpty().WithMessage("Eyalet/Bölge ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Şehir adı boş olamaz.")
            .MaximumLength(100).WithMessage("Şehir adı en fazla 100 karakter olabilir.");
    }
}
