using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Cities.GetCities;

internal sealed class GetCitiesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        // Tüm şehirleri listele
        endpoints.MapGet("/api/v1/general/cities", async (
            Guid? countryId,
            bool? isActive,
            int? pageNumber,
            int? pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCitiesQuery(
                null,
                countryId,
                isActive,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Cities")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Cities");

        // Belirli bir eyalete/bölgeye ait şehirleri listele
        endpoints.MapGet("/api/v1/general/states/{stateId}/cities", async (
            Guid stateId,
            bool? isActive,
            int? pageNumber,
            int? pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCitiesQuery(
                stateId,
                null,
                isActive,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Cities")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
