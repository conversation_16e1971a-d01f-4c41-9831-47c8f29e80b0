using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Cities.UpdateCity;

internal sealed class UpdateCityCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<UpdateCityCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateCityCommand request, CancellationToken cancellationToken)
    {
        var city = await _dbContext.City
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (city == null)
        {
            return Result.Failure(GeneralErrors.CityNotFound(request.Id));
        }

        // Eyaletin varlığını kontrol et
        var state = await _dbContext.State
            .FirstOrDefaultAsync(s => s.Id == request.StateId, cancellationToken);

        if (state == null)
        {
            return Result.Failure(GeneralErrors.StateNotFound(request.StateId));
        }

        // İsim benzersizliğini kontrol et (aynı eyalet içinde, kendi ID'si hariç)
        var existingCity = await _dbContext.City
            .FirstOrDefaultAsync(c =>
                c.StateId == request.StateId &&
                c.Name == request.Name &&
                c.Id != request.Id,
                cancellationToken);

        if (existingCity != null)
        {
            return Result.Failure(GeneralErrors.CityNameExists(request.Name, request.StateId));
        }

        city.Update(
            request.Name,
            request.StateId,
            request.IsActive);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
