using FluentValidation;

namespace General.Application.Cities.UpdateCity;

internal sealed class UpdateCityCommandValidator : AbstractValidator<UpdateCityCommand>
{
    public UpdateCityCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Şehir ID'si boş olamaz.");

        RuleFor(x => x.StateId)
            .NotEmpty().WithMessage("Eyalet/Bölge ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Şehir adı boş olamaz.")
            .MaximumLength(100).WithMessage("Şehir adı en fazla 100 karakter olabilir.");
    }
}
