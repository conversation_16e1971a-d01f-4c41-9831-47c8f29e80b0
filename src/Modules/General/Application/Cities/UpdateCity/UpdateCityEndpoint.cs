using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Cities.UpdateCity;

internal sealed class UpdateCityEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPut("/api/v1/general/cities/{id}", async (
            Guid id,
            UpdateCityCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("URL'deki ID ile request body'deki ID eşleşmiyor.");
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                () => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("General.Cities")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Cities");
    }
}
