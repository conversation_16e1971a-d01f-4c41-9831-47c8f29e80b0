using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Cities.GetCity;

internal sealed class GetCityQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetCityQuery, Result<CityDetailDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<CityDetailDto>> Handle(GetCityQuery request, CancellationToken cancellationToken)
    {
        var city = await _dbContext.City
            .Include(c => c.State)
            .ThenInclude(s => s.Country)
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (city == null)
        {
            return Result.Failure<CityDetailDto>(GeneralErrors.CityNotFound(request.Id));
        }

        var cityDto = new CityDetailDto(
            city.Id,
            city.Name,
            city.IsActive,
            city.StateId,
            city.State.Name,
            city.State.CountryId,
            city.State.Country.Name,
            city.InsertDate,
            city.UpdateDate);

        return Result.Success(cityDto);
    }
}
