using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Files.ListFiles;

public class ListFilesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapGet("/api/v1/general/files", async (
            Guid? folderId,
            string? searchTerm,
            int? pageNumber,
            int? pageSize,
            ISender sender) =>
        {
            var query = new ListFilesQuery(
                folderId,
                searchTerm,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await sender.Send(query);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Files")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Files");
    }
}
