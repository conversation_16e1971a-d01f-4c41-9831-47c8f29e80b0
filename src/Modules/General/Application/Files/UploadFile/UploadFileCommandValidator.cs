using FluentValidation;

namespace General.Application.Files.UploadFile;

internal class UploadFileCommandValidator : AbstractValidator<UploadFileCommand>
{
    public UploadFileCommandValidator()
    {
        RuleFor(x => x.File)
            .NotNull()
            .WithMessage("Dosya boş olamaz.");

        RuleFor(x => x.File.Length)
            .GreaterThan(0)
            .WithMessage("Dosya boyutu 0'dan büyük olmalıdır.")
            .LessThanOrEqualTo(100 * 1024 * 1024) // 100MB
            .WithMessage("Dosya boyutu en fazla 100MB olabilir.");

        RuleFor(x => x.FolderId)
            .NotEmpty()
            .WithMessage("Klasör ID'si boş olamaz.");
    }
}
