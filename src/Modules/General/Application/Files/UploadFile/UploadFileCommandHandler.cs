using General.Application.Abstractions;
using General.Domain;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using File = General.Domain.File;

namespace General.Application.Files.UploadFile;

internal sealed class UploadFileCommandHandler(
    IGeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<UploadFileCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result<Guid>> Handle(UploadFileCommand request, CancellationToken cancellationToken)
    {
        // Klasörün var olup olmadığını kontrol et
        var folder = await _dbContext.Folder
            .FirstOrDefaultAsync(f => f.Id == request.FolderId, cancellationToken);

        if (folder == null)
        {
            return Result.Failure<Guid>(GeneralErrors.FolderNotFound(request.FolderId));
        }

        try
        {
            // Dosyayı kaydet
            var (fileName, storagePath) = await _storageService.SaveFileAsync(
                request.File,
                folder.Path,
                cancellationToken);

            // Dosya bilgilerini oluştur
            var file = File.Create(
                fileName,
                request.File.FileName,
                Path.GetExtension(request.File.FileName),
                request.File.ContentType,
                request.File.Length,
                folder.Id,
                storagePath);

            // Metadata varsa ekle
            if (request.Metadata != null)
            {
                file.UpdateMetadata(request.Metadata);
            }

            // Dosyayı veritabanına kaydet
            _dbContext.File.Add(file);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Event'i yayınla
            file.Raise(new FileUploadedEvent(file.Id));
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success(file.Id);
        }
        catch (Exception ex)
        {
            return Result.Failure<Guid>(GeneralErrors.FileUploadFailed(ex.Message));
        }
    }
}
