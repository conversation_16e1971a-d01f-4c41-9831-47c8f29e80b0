using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Files.DeleteFile;

public class DeleteFileEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapDelete("/api/v1/general/files/{id}", async (
            Guid id,
            ISender sender) =>
        {
            var command = new DeleteFileCommand(id);
            var result = await sender.Send(command);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("General.Files")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Files");
    }
}
