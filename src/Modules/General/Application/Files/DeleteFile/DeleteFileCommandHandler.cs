using General.Application.Abstractions;
using General.Domain;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Files.DeleteFile;

internal sealed class DeleteFileCommandHandler(
    IGeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<DeleteFileCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result> Handle(DeleteFileCommand request, CancellationToken cancellationToken)
    {
        var file = await _dbContext.File
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (file == null)
        {
            return Result.Failure(GeneralErrors.FileNotFound(request.Id));
        }

        try
        {
            // Fiziksel dosyayı sil
            var isDeleted = await _storageService.DeleteFileAsync(file.StoragePath, cancellationToken);

            if (!isDeleted)
            {
                return Result.Failure(GeneralErrors.FileDeleteFailed("Fiziksel dosya silinemedi."));
            }

            // Event'i yayınla
            file.Raise(new FileDeletedEvent(file.Id));

            // Veritabanından sil
            _dbContext.File.Remove(file);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(GeneralErrors.FileDeleteFailed(ex.Message));
        }
    }
}
