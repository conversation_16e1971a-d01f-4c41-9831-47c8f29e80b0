using General.Application.Abstractions;
using General.Domain;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Files.GetFile;

internal sealed class GetFileQueryHandler(
    IGeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<GetFileQuery, Result<FileResult>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result<FileResult>> Handle(GetFileQuery request, CancellationToken cancellationToken)
    {
        var file = await _dbContext.File
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (file == null)
        {
            return Result.Failure<FileResult>(GeneralErrors.FileNotFound(request.Id));
        }

        try
        {
            var fileStream = await _storageService.GetFileStreamAsync(file.StoragePath, cancellationToken);
            
            return Result.Success<FileResult>(new FileStreamResult(fileStream, file.MimeType)
            {
                FileDownloadName = file.OriginalFileName
            });
        }
        catch (FileNotFoundException)
        {
            return Result.Failure<FileResult>(GeneralErrors.FileNotFound("Fiziksel dosya bulunamadı."));
        }
        catch (Exception ex)
        {
            return Result.Failure<FileResult>(Error.Failure("General.File.ReadError", ex.Message));
        }
    }
}
