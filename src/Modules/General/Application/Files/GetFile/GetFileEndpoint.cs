using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Files.GetFile;

public class GetFileEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapGet("/api/v1/general/files/{id}", async (
            Guid id,
            ISender sender) =>
        {
            var query = new GetFileQuery(id);
            var result = await sender.Send(query);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Files")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Files");
    }
}
