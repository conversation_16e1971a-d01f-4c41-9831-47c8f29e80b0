using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Languages.DeleteLanguage;

internal sealed class DeleteLanguageCommandHandler(IGeneralDbContext dbContext) : IRequestHandler<DeleteLanguageCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteLanguageCommand request, CancellationToken cancellationToken)
    {
        var language = await _dbContext.Language
            .FirstOrDefaultAsync(l => l.Id == request.Id, cancellationToken);

        if (language == null)
        {
            return Result.Failure("404", "Dil bulunamadı.");
        }
        _dbContext.Language.Remove(language);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
