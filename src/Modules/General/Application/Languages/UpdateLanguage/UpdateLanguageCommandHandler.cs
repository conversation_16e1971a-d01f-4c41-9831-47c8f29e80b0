using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Languages.UpdateLanguage;

internal sealed class UpdateLanguageCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<UpdateLanguageCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateLanguageCommand request, CancellationToken cancellationToken)
    {
        var language = await _dbContext.Language
            .FirstOrDefaultAsync(l => l.Id == request.Id, cancellationToken);

        if (language == null)
        {
            return Result.Failure("404", "Dil bulunamadı.");
        }

        language.Update(
            request.Code,
            request.Name,
            request.IsActive,
            request.FlagIcon);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
