using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Languages.UpdateLanguage;

internal sealed class UpdateLanguageEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPut("/api/v1/general/languages/{id}", async (
            Guid id,
            UpdateLanguageCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("URL'deki ID ile request body'deki ID eşleşmiyor.");
            }
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("General.Language")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Language");
    }
}
