using FluentValidation;
using General.Application.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace General.Application.Languages.UpdateLanguage;

public class UpdateLanguageCommandValidator : AbstractValidator<UpdateLanguageCommand>
{
    public UpdateLanguageCommandValidator(IGeneralDbContext dbContext)
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Dil ID'si boş olamaz.");

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Dil kodu boş olamaz.")
            .MaximumLength(10).WithMessage("Dil kodu en fazla 10 karakter olabilir.")
            .MustAsync(async (command, code, cancellation) => !await dbContext.Language
                    .AnyAsync(l => l.Code == code && l.Id != command.Id, cancellation)).WithMessage("Bu dil kodu zaten kullanılıyor.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Dil adı boş olamaz.")
            .MaximumLength(100).WithMessage("Dil adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.FlagIcon)
            .MaximumLength(100).WithMessage("Bayrak ikonu en fazla 100 karakter olabilir.");
    }
}
