using FluentValidation;
using General.Application.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace General.Application.Languages.CreateLanguage;

public class CreateLanguageCommandValidator : AbstractValidator<CreateLanguageCommand>
{
    public CreateLanguageCommandValidator(IGeneralDbContext dbContext)
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Dil kodu boş olamaz.")
            .MaximumLength(10).WithMessage("Dil kodu en fazla 10 karakter olabilir.")
            .MustAsync(async (code, cancellation) => !await dbContext.Language.AnyAsync(l => l.Code == code, cancellation)).WithMessage("Bu dil kodu zaten kullanılıyor.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Dil adı boş olamaz.")
            .MaximumLength(100).WithMessage("Dil adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.FlagIcon)
            .MaximumLength(100).WithMessage("Bayrak ikonu en fazla 100 karakter olabilir.");
    }
}
