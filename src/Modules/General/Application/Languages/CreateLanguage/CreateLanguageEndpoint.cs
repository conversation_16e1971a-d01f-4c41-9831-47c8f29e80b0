using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Languages.CreateLanguage;

public class CreateLanguageEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/languages", async (
            CreateLanguageCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.Created, CustomResults.Problem);
        })
        .WithTags("General.Language")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Language");
    }
}
