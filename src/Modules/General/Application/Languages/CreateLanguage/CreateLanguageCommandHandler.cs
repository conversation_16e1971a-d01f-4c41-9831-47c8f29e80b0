using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Shared.Application;

namespace General.Application.Languages.CreateLanguage;

public class CreateLanguageCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<CreateLanguageCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateLanguageCommand request, CancellationToken cancellationToken)
    {
        var language = Language.Create(
            request.Code,
            request.Name,
            request.IsActive,
            request.FlagIcon);

        _dbContext.Language.Add(language);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(language.Id);
    }
}
