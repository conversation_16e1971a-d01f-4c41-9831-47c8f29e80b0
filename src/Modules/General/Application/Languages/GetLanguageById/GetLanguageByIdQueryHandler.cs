using FluentValidation;
using General.Application.Abstractions;
using General.Application.Languages.GetLanguages;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Languages.GetLanguageById;

public class GetLanguageByIdQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetLanguageByIdQuery, Result<LanguageDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<LanguageDto>> Handle(GetLanguageByIdQuery request, CancellationToken cancellationToken)
    {
        var language = await _dbContext.Language
            .Where(l => l.Id == request.Id)
            .Select(l => new LanguageDto(
                l.Id,
                l.Code,
                l.Name,
                l.IsActive,
                l.FlagIcon))
            .FirstOrDefaultAsync(cancellationToken);

        if (language == null)
        {
            return Result.Failure<LanguageDto>("404", "Dil bulunamadı.");
        }

        return Result.Success(language);
    }
}
