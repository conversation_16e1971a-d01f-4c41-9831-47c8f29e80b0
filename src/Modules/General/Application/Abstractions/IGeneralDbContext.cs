using General.Domain;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Data;
using File = General.Domain.File;

namespace General.Application.Abstractions;

public interface IGeneralDbContext : IBaseDbContext
{
    DbSet<Language> Language { get; }
    DbSet<Country> Country { get; }
    DbSet<State> State { get; }
    DbSet<City> City { get; }
    DbSet<File> File { get; }
    DbSet<Folder> Folder { get; } 
    DbSet<ExportAuditLog> ExportAuditLog { get; }  
}
