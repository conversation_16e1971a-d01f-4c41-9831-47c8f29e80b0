using General.Application.Abstractions;
using General.Domain;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.CreateFolder;

internal sealed class CreateFolderCommandHandler(
    IGeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<CreateFolderCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result<Guid>> Handle(CreateFolderCommand request, CancellationToken cancellationToken)
    {
        // Aynı isimde klasör var mı kontrol et
        var existingFolder = await _dbContext.Folder
            .FirstOrDefaultAsync(f =>
                f.Name == request.Name &&
                f.ParentFolderId == request.ParentFolderId,
                cancellationToken);

        if (existingFolder != null)
        {
            return Result.Failure<Guid>(GeneralErrors.FolderNameExists(request.Name, request.ParentFolderId));
        }

        string folderPath;

        if (request.ParentFolderId.HasValue)
        {
            // Üst klasörü kontrol et
            var parentFolder = await _dbContext.Folder
                .FirstOrDefaultAsync(f => f.Id == request.ParentFolderId, cancellationToken);

            if (parentFolder == null)
            {
                return Result.Failure<Guid>(GeneralErrors.FolderNotFound(request.ParentFolderId.Value));
            }

            folderPath = Path.Combine(parentFolder.Path, request.Name);
        }
        else
        {
            // Kök klasör
            folderPath = request.Name;
        }

        try
        {
            // Fiziksel klasörü oluştur
            var isCreated = await _storageService.CreateFolderAsync(folderPath, cancellationToken);

            if (!isCreated)
            {
                return Result.Failure<Guid>(GeneralErrors.FolderCreateFailed("Fiziksel klasör oluşturulamadı."));
            }

            // Veritabanı kaydını oluştur
            var folder = Folder.Create(
                request.Name,
                request.ParentFolderId,
                folderPath);

            // Attributes varsa ekle
            if (request.Attributes != null)
            {
                folder.UpdateAttributes(request.Attributes);
            }

            _dbContext.Folder.Add(folder);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Event'i yayınla
            folder.Raise(new FolderCreatedEvent(folder.Id));
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success(folder.Id);
        }
        catch (Exception ex)
        {
            return Result.Failure<Guid>(GeneralErrors.FolderCreateFailed(ex.Message));
        }
    }
}
