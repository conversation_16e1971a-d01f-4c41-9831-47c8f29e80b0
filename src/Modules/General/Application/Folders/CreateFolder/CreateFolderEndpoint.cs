using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Folders.CreateFolder;

public class CreateFolderEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/folders", async (
            CreateFolderCommand command,
            ISender sender) =>
        {
            var result = await sender.Send(command);
            return result.Match(Results.Created, CustomResults.Problem);
        })
        .WithTags("General.Folders")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Folders");
    }
}
