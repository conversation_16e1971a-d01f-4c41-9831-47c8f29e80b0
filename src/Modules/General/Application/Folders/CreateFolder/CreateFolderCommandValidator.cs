using FluentValidation;

namespace General.Application.Folders.CreateFolder;

internal class CreateFolderCommandValidator : AbstractValidator<CreateFolderCommand>
{
    public CreateFolderCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Klasör adı boş olamaz.")
            .MaximumLength(255)
            .WithMessage("Klasör adı en fazla 255 karakter olabilir.")
            .Matches(@"^[^\\/:*?""<>|]+$")
            .WithMessage("Klasör adı geçersiz karakterler içeriyor.");
    }
}
