using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Folders.GetFolderContent;

public class GetFolderContentEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapGet("/api/v1/general/folders/{id}/content", async (
            Guid id,
            ISender sender) =>
        {
            var query = new GetFolderContentQuery(id);
            var result = await sender.Send(query);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.Folders")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Folders");
    }
}
