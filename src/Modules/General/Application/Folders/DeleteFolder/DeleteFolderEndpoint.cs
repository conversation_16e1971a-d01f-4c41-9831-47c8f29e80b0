using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Folders.DeleteFolder;

public class DeleteFolderEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapDelete("/api/v1/general/folders/{id}", async (
            Guid id,
            bool? recursive,
            ISender sender) =>
        {
            var command = new DeleteFolderCommand(id, recursive ?? false);
            var result = await sender.Send(command);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("General.Folders")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Folders");
    }
}
