namespace General.Application.Shared.DTOs;

public record FolderDto(
    Guid Id,
    string Name,
    Guid? ParentFolderId,
    string Path,
    Dictionary<string, object> Attributes,
    DateTime InsertDate,
    DateTime? UpdateDate);

public record FolderDetailDto(
    Guid Id,
    string Name,
    Guid? ParentFolderId,
    string? ParentFolderName,
    string Path,
    Dictionary<string, object> Attributes,
    DateTime InsertDate,
    DateTime? UpdateDate);

public record FolderContentDto(
    Guid Id,
    string Name,
    Guid? ParentFolderId,
    string Path,
    List<FolderDto> SubFolders,
    List<FileDto> Files);
