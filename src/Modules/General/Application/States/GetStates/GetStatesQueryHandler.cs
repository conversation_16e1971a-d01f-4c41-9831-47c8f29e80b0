using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.States.GetStates;

internal sealed class GetStatesQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetStatesQuery, PagedResult<StateDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<PagedResult<StateDto>> Handle(GetStatesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.State
            .Include(s => s.Country)
            .Include(s => s.Cities)
            .AsQueryable();

        if (request.CountryId.HasValue)
        {
            query = query.Where(s => s.CountryId == request.CountryId.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(s => s.IsActive == request.IsActive.Value);
        }

        // Get total count
        var totalCount = await _dbContext.State.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var states = await query
            .OrderBy(s => s.Country.Name)
            .ThenBy(s => s.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new StateDto(
                s.Id,
                s.Name,
                s.Code,
                s.IsActive,
                s.CountryId,
                s.Country.Name,
                s.Cities.Count))
            .ToListAsync(cancellationToken);

        var result = PagedResult<StateDto>.Success(states);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
