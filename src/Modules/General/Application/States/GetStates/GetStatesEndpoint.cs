using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.States.GetStates;

internal sealed class GetStatesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        // Tüm eyaletleri listele
        endpoints.MapGet("/api/v1/general/states", async (
            bool? isActive,
            int? pageNumber,
            int? pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetStatesQuery(
                null,
                isActive,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.States")
        .WithGroupName("apiv1")
        .RequireAuthorization();

        // Belirli bir ülkeye ait eyaletleri listele
        endpoints.MapGet("/api/v1/general/countries/{countryId}/states", async (
            Guid countryId,
            bool? isActive,
            int? pageNumber,
            int? pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetStatesQuery(
                countryId,
                isActive,
                pageNumber ?? 1,
                pageSize ?? 20);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.States")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
