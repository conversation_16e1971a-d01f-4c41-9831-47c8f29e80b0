using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.States.DeleteState;

internal sealed class DeleteStateCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<DeleteStateCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteStateCommand request, CancellationToken cancellationToken)
    {
        var state = await _dbContext.State
            .Include(s => s.Cities)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (state == null)
        {
            return Result.Failure(GeneralErrors.StateNotFound(request.Id));
        }

        if (state.Cities.Any())
        {
            return Result.Failure(GeneralErrors.HasDependentCities);
        }

        _dbContext.State.Remove(state);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
