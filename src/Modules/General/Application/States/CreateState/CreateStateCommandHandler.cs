using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.States.CreateState;

internal sealed class CreateStateCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<CreateStateCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateStateCommand request, CancellationToken cancellationToken)
    {
        // Ülkenin varlığını kontrol et
        var country = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Id == request.CountryId, cancellationToken);

        if (country == null)
        {
            return Result.Failure<Guid>(GeneralErrors.CountryNotFound(request.CountryId));
        }

        // Kod benzersizliğini kontrol et (aynı ülke içinde)
        if (!string.IsNullOrEmpty(request.Code))
        {
            var existingState = await _dbContext.State
                .FirstOrDefaultAsync(s => s.CountryId == request.CountryId && s.Code == request.Code, cancellationToken);

            if (existingState != null)
            {
                return Result.Failure<Guid>(GeneralErrors.StateCodeExists(request.Code, request.CountryId));
            }
        }

        var state = State.Create(
            request.Name,
            request.Code,
            request.CountryId,
            request.IsActive);

        _dbContext.State.Add(state);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(state.Id);
    }
}
