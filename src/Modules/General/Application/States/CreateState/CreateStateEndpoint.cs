using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.States.CreateState;

internal sealed class CreateStateEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/countries/{countryId}/states", async (
            Guid countryId,
            CreateStateCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (countryId != command.CountryId)
            {
                return Results.BadRequest("URL'deki ülke ID'si ile request body'deki ülke ID'si eşleşmiyor.");
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/general/states/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("General.States")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.States");
    }
}
