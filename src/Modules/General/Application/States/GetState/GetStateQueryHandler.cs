using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.States.GetState;

internal sealed class GetStateQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetStateQuery, Result<StateDetailDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<StateDetailDto>> Handle(GetStateQuery request, CancellationToken cancellationToken)
    {
        var state = await _dbContext.State
            .Include(s => s.Country)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (state == null)
        {
            return Result.Failure<StateDetailDto>(GeneralErrors.StateNotFound(request.Id));
        }

        var stateDto = new StateDetailDto(
            state.Id,
            state.Name,
            state.Code,
            state.IsActive,
            state.CountryId,
            state.Country.Name,
            state.InsertDate,
            state.UpdateDate);

        return Result.Success(stateDto);
    }
}
