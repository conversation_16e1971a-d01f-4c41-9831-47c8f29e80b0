using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.States.UpdateState;

internal sealed class UpdateStateCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<UpdateStateCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateStateCommand request, CancellationToken cancellationToken)
    {
        var state = await _dbContext.State
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (state == null)
        {
            return Result.Failure(GeneralErrors.StateNotFound(request.Id));
        }

        // Ülkenin varlığını kontrol et
        var country = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Id == request.CountryId, cancellationToken);

        if (country == null)
        {
            return Result.Failure(GeneralErrors.CountryNotFound(request.CountryId));
        }

        // Kod benzersizliğini kontrol et (aynı ülke içinde, kendi ID'si hariç)
        if (!string.IsNullOrEmpty(request.Code))
        {
            var existingState = await _dbContext.State
                .FirstOrDefaultAsync(s =>
                    s.CountryId == request.CountryId &&
                    s.Code == request.Code &&
                    s.Id != request.Id,
                    cancellationToken);

            if (existingState != null)
            {
                return Result.Failure(GeneralErrors.StateCodeExists(request.Code, request.CountryId));
            }
        }

        state.Update(
            request.Name,
            request.Code,
            request.CountryId,
            request.IsActive);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
