using FluentValidation;

namespace General.Application.States.UpdateState;

internal sealed class UpdateStateCommandValidator : AbstractValidator<UpdateStateCommand>
{
    public UpdateStateCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Eyalet/Bölge ID'si boş olamaz.");

        RuleFor(x => x.CountryId)
            .NotEmpty().WithMessage("Ülke ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Eyalet/Bölge adı boş olamaz.")
            .MaximumLength(100).WithMessage("Eyalet/Bölge adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.Code)
            .MaximumLength(10).WithMessage("Eyalet/Bölge kodu en fazla 10 karakter olabilir.");
    }
}
