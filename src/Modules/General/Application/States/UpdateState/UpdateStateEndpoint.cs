using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.States.UpdateState;

internal sealed class UpdateStateEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPut("/api/v1/general/states/{id}", async (
            Guid id,
            UpdateStateCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("URL'deki ID ile request body'deki ID eşleşmiyor.");
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                () => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("General.States")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.States");
    }
}
