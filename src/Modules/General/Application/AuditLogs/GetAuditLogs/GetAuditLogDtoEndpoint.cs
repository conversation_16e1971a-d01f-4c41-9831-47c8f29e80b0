using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.AuditLogs.GetAuditLogs;

internal sealed class GetAuditLogDtoEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/general/getauditlogs/list", async (
            [AsParameters] GetAuditLogDtoQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("General.AuditLogs")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.AuditLogs");
    } 
}
