using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Infrastructure.Localization;
using Shared.Application;
using Shared.Contracts;

namespace General.Application.AuditLogs.GetAuditLogs;

internal sealed class GetAuditLogDtoQueryHandler: IRequestHandler<GetAuditLogDtoQuery, PagedResult<AuditLogDto>>
{
    private readonly IGeneralDbContext _dbContext;
    private readonly ILocalizer _localizer;
    private readonly ISharedUserService _userService;
    public GetAuditLogDtoQueryHandler(
        IGeneralDbContext dbContext,
        ILocalizer localizer,
        ISharedUserService userService
    )
    {
        _dbContext = dbContext;
        _localizer = localizer;
        _userService = userService;
    }
    public async Task<PagedResult<AuditLogDto>> Handle(GetAuditLogDtoQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.ExportAuditLog.AsQueryable();

        if (request.StartDate.HasValue)
        {
            query = query.Where(c => c.InsertDate >= request.StartDate.Value);
        }

        if (request.EndDate.HasValue)
        {
            query = query.Where(c => c.InsertDate <= request.EndDate.Value);
        }

        if (request.UserId.HasValue)
        {
            query = query.Where(c => c.UserId == request.UserId.Value);
        }

        if (!string.IsNullOrEmpty(request.Module))
        {
            query = query.Where(c => c.Module == request.Module);
        }

        if (!string.IsNullOrEmpty(request.FunctionName))
        {
            query = query.Where(c => c.FunctionName == request.FunctionName);
        }

        // Get total count
        var totalCount = await _dbContext.ExportAuditLog.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var auditLogs = await query
            .OrderByDescending(c => c.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(c => new AuditLogDto(
                c.Id,
                c.UserId,
                c.Module,
                c.FunctionName,
                c.ExportedCount,
                c.SelectedCount,
                c.FilterUsed,
                c.FileName,
                c.RequestFilter,
                c.InsertDate))
            .ToListAsync(cancellationToken);

        List<Guid> userIds = auditLogs.Where(x => x.UserId != null).Select(x => x.UserId).ToList();
        var users = await _userService.GetUsersByIdsAsync(userIds);
        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}");
        foreach (var item in auditLogs)
        {
        item.UserName = userDict.ContainsKey(item.UserId)
            ? userDict[item.UserId]
            : _localizer.Get("UnknownUser");
        }


        var result = PagedResult<AuditLogDto>.Success(auditLogs);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}

