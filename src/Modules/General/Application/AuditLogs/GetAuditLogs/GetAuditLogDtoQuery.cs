using MediatR;
using Shared.Application;

namespace General.Application.AuditLogs.GetAuditLogs;

public record GetAuditLogDtoQuery(
    DateTime? StartDate = null,
    DateTime? EndDate = null,
    Guid? UserId = null,
    string? Module = null,
    string? FunctionName = null,
    bool? IsActive = null,
    DateTime? InsertDate = null,
    int PageNumber = 1,
    int PageSize = 20
) : IRequest<PagedResult<AuditLogDto>>;
