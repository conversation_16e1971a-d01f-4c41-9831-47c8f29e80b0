using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Countries.DeleteCountry;

internal sealed class DeleteCountryCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<DeleteCountryCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(DeleteCountryCommand request, CancellationToken cancellationToken)
    {
        var country = await _dbContext.Country
            .Include(c => c.States)
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (country == null)
        {
            return Result.Failure(GeneralErrors.CountryNotFound(request.Id));
        }

        if (country.States.Any())
        {
            return Result.Failure(GeneralErrors.HasDependentStates);
        }

        _dbContext.Country.Remove(country);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
