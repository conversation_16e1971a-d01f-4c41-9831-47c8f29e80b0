using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Countries.GetCountry;

internal sealed class GetCountryQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetCountryQuery, Result<CountryDetailDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<CountryDetailDto>> Handle(GetCountryQuery request, CancellationToken cancellationToken)
    {
        var country = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (country == null)
        {
            return Result.Failure<CountryDetailDto>(GeneralErrors.CountryNotFound(request.Id));
        }

        var countryDto = new CountryDetailDto(
            country.Id,
            country.Name,
            country.Code,
            country.PhoneCode,
            country.IsActive,
            country.InsertDate,
            country.UpdateDate);

        return Result.Success(countryDto);
    }
}
