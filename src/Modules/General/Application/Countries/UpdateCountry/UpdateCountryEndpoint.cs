using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Countries.UpdateCountry;

internal sealed class UpdateCountryEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPut("/api/v1/general/countries/{id}", async (
            Guid id,
            UpdateCountryCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("URL'deki ID ile request body'deki ID eşleşmiyor.");
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                () => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("General.Countries")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Countries");
    }
}
