using FluentValidation;

namespace General.Application.Countries.UpdateCountry;

internal sealed class UpdateCountryCommandValidator : AbstractValidator<UpdateCountryCommand>
{
    public UpdateCountryCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Ülke ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Ülke adı boş olamaz.")
            .MaximumLength(100).WithMessage("Ülke adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Ülke kodu boş olamaz.")
            .MaximumLength(10).WithMessage("Ülke kodu en fazla 10 karakter olabilir.");

        RuleFor(x => x.PhoneCode)
            .MaximumLength(10).WithMessage("Telefon kodu en fazla 10 karakter olabilir.");
    }
}
