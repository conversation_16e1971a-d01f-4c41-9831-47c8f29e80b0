using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Countries.UpdateCountry;

internal sealed class UpdateCountryCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<UpdateCountryCommand, Result>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result> Handle(UpdateCountryCommand request, CancellationToken cancellationToken)
    {
        var country = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (country == null)
        {
            return Result.Failure(GeneralErrors.CountryNotFound(request.Id));
        }

        // Kod benzersizliğini kontrol et (kendi ID'si hariç)
        var existingCountry = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Code == request.Code && c.Id != request.Id, cancellationToken);

        if (existingCountry != null)
        {
            return Result.Failure(GeneralErrors.CountryCodeExists(request.Code));
        }

        country.Update(
            request.Name,
            request.Code,
            request.PhoneCode,
            request.IsActive);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
