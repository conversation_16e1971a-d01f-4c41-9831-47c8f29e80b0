using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace General.Application.Countries.CreateCountry;

internal sealed class CreateCountryEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/countries", async (
            CreateCountryCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/general/countries/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("General.Countries")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Countries");
    }
}
