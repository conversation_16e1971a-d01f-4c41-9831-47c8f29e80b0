using General.Application.Abstractions;
using General.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Countries.CreateCountry;

internal sealed class CreateCountryCommandHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<CreateCountryCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateCountryCommand request, CancellationToken cancellationToken)
    {
        // Kod benzersizliğini kontrol et
        var existingCountry = await _dbContext.Country
            .FirstOrDefaultAsync(c => c.Code == request.Code, cancellationToken);

        if (existingCountry != null)
        {
            return Result.Failure<Guid>(GeneralErrors.CountryCodeExists(request.Code));
        }

        var country = Country.Create(
            request.Name,
            request.Code,
            request.PhoneCode,
            request.IsActive);

        _dbContext.Country.Add(country);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(country.Id);
    }
}
