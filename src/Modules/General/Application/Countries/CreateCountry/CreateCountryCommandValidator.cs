using FluentValidation;

namespace General.Application.Countries.CreateCountry;

internal sealed class CreateCountryCommandValidator : AbstractValidator<CreateCountryCommand>
{
    public CreateCountryCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Ülke adı boş olamaz.")
            .MaximumLength(100).WithMessage("Ülke adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Ülke kodu boş olamaz.")
            .MaximumLength(10).WithMessage("<PERSON>lke kodu en fazla 10 karakter olabilir.");

        RuleFor(x => x.PhoneCode)
            .MaximumLength(10).WithMessage("Telefon kodu en fazla 10 karakter olabilir.");
    }
}
