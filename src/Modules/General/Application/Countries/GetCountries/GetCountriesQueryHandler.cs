using General.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Countries.GetCountries;

internal sealed class GetCountriesQueryHandler(
    IGeneralDbContext dbContext
) : IRequestHandler<GetCountriesQuery, PagedResult<CountryDto>>
{
    private readonly IGeneralDbContext _dbContext = dbContext;

    public async Task<PagedResult<CountryDto>> Handle(GetCountriesQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Country.AsQueryable();

        if (request.IsActive.HasValue)
        {
            query = query.Where(x => x.IsActive == request.IsActive.Value);
        }

        // Get total count
        var totalCount = await _dbContext.Country.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var countries = await query
            .OrderBy(x => x.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new CountryDto(
                x.Id,
                x.Name,
                x.Code,
                x.PhoneCode,
                x.IsActive,
                x.States.Count))
            .ToListAsync(cancellationToken);

        var result = PagedResult<CountryDto>.Success(countries);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
