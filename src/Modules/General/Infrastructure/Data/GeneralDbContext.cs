using System.Reflection;
using General.Application.Abstractions;
using General.Domain;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;
using File = General.Domain.File;

namespace General.Infrastructure.Data;

public class GeneralDbContext(
    DbContextOptions<GeneralDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), IGeneralDbContext
{
    public DbSet<Language> Language { get; set; }
    public DbSet<Country> Country { get; set; }
    public DbSet<State> State { get; set; }
    public DbSet<City> City { get; set; }
    public DbSet<File> File { get; set; }
    public DbSet<Folder> Folder { get; set; }  
    public DbSet<ExportAuditLog> ExportAuditLog { get; set; }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("General");
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
