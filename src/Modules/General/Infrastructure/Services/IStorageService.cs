using Microsoft.AspNetCore.Http;

namespace General.Infrastructure.Services;

public interface IStorageService
{
    Task<(string fileName, string storagePath)> SaveFileAsync(IFormFile file, string folderPath, CancellationToken cancellationToken = default);
    Task<(string fileName, string storagePath)> SaveFileAsync(Stream fileStream, string originalFileName, string folderPath, CancellationToken cancellationToken = default);
    Task<bool> DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default);
    Task<Stream> GetFileStreamAsync(string storagePath, CancellationToken cancellationToken = default);
    Task<bool> IsFolderExistAsync(string folderPath, CancellationToken cancellationToken = default);
    Task<bool> CreateFolderAsync(string folderPath, CancellationToken cancellationToken = default);
    Task<bool> DeleteFolderAsync(string folderPath, bool recursive = false, CancellationToken cancellationToken = default);
    Task<bool> MoveFileAsync(string sourceStoragePath, string destinationStoragePath, CancellationToken cancellationToken = default);
    Task<bool> MoveFolderAsync(string sourceFolderPath, string destinationFolderPath, CancellationToken cancellationToken = default);
    string GetUniqueFileName(string originalFileName, string folderPath);
}
