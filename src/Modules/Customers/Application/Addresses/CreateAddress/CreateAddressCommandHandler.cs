using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.CreateAddress;

public class CreateAddressCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateAddressCommand, Result<Guid>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<Guid>> <PERSON><PERSON>(CreateAddressCommand request, CancellationToken cancellationToken)
    {
        if (request.IsDefault)
        {
            var existingDefaultAddresses = await _context.Address
                .Where(a => a.CustomerId == request.CustomerId && a.IsDefault)
                .ToListAsync(cancellationToken);
            foreach (var existingaddress in existingDefaultAddresses)
            {
                existingaddress.IsDefault = false;
            }
        }
        var address = new Address
        {
            Id = Guid.NewGuid(),
            CustomerId = request.CustomerId,
            Title = request.Title,
            Phone = request.Phone,
            PhonePrefix = request.PhonePrefix,
            Email = request.Email,
            Country = request.Country,
            State = request.State,
            City = request.City,
            Province = request.Province,
            PostCode = request.PostCode,
            Detail = request.Detail,
            IsDefault = request.IsDefault
        };
        await _context.Address.AddAsync(address, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success(address.Id);
    }
}
