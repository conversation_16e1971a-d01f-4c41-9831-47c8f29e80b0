using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Addresses.CreateAddress;

public class CreateAddressEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("api/v1/customers/customers/{customerId}/addresses", async (
            Guid customerId,
            CreateAddressCommand command,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            command = command with { CustomerId = customerId };
            var result = await sender.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/customers/customers/{customerId}/addresses/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses");
    }
}
