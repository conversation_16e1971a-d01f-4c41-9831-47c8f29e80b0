using FluentValidation;
using Customers.Application.Abstractions;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Addresses.CreateAddress;

public class CreateAddressCommandValidator : AbstractValidator<CreateAddressCommand>
{
    private readonly ICustomersDbContext _context;

    public CreateAddressCommandValidator(ICustomersDbContext context)
    {
        _context = context;

        RuleFor(x => x.CustomerId)
            .NotEmpty()
            .MustAsync(async (customerId, cancellation) =>
            {
                return await _context.Customers
                    .AnyAsync(x => x.Id == customerId, cancellation);
            })
            .WithMessage("Müşteri bulunamadı.");

        RuleFor(x => x.Title)
            .NotEmpty()
            .MaximumLength(100)
            .WithMessage("Adres başlığı boş olamaz ve en fazla 100 karakter olabilir.");

        RuleFor(x => x.Email)
            .EmailAddress()
            .When(x => !string.IsNullOrEmpty(x.Email))
            .WithMessage("Geçerli bir e-posta adresi giriniz");

        RuleFor(x => x.Phone)
            .MaximumLength(20)
            .When(x => !string.IsNullOrEmpty(x.Phone))
            .WithMessage("Telefon numarası en fazla 20 karakter olabilir");

        RuleFor(x => x.Country)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.Country));

        RuleFor(x => x.State)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.State));

        RuleFor(x => x.City)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.City));

        RuleFor(x => x.Province)
            .MaximumLength(100)
            .When(x => !string.IsNullOrEmpty(x.Province));

        RuleFor(x => x.PostCode)
            .MaximumLength(20)
            .When(x => !string.IsNullOrEmpty(x.PostCode));
    }
}