using MediatR;
using Shared.Application;

namespace Customers.Application.Addresses.CreateAddress;

public sealed record CreateAddressCommand : IRequest<Result<Guid>>
{
    public Guid CustomerId { get; init; }
    public string Title { get; init; }
    public string? Phone { get; init; }
    public string? PhonePrefix { get; init; }
    public string? Email { get; init; }
    public string? Country { get; init; }
    public string? State { get; init; }
    public string? City { get; init; }
    public string? Province { get; init; }
    public string? PostCode { get; init; }
    public string? Detail { get; init; }
    public bool IsDefault { get; init; }
}
