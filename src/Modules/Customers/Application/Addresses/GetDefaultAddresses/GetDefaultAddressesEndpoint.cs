using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Addresses.GetDefaultAddresses;

public class GetDefaultAddressesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/customers/customers/{customerId}/default-address", async (
            Guid customerId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new GetDefaultAddressesQuery(customerId);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses");
    }
}
