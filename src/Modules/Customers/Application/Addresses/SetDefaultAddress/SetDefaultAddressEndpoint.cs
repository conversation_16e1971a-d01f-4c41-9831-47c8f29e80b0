using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Addresses.SetDefaultAddress;

public class SetDefaultAddressEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("api/v1/customers/customers/{customerId}/addresses/{addressId}/set-default", async (
            Guid customerId,
            Guid addressId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new SetDefaultAddressCommand
            {
                CustomerId = customerId,
                AddressId = addressId
            };

            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses");
    }
}
