using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.SetDefaultAddress;

public class SetDefaultAddressCommandHandler : IRequestHandler<SetDefaultAddressCommand, Result>
{
    private readonly ICustomersDbContext _context;

    public SetDefaultAddressCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(SetDefaultAddressCommand request, CancellationToken cancellationToken)
    {
        var addresses = await _context.Address
            .Where(a => a.CustomerId == request.CustomerId)
            .ToListAsync(cancellationToken);
        var targetAddress = addresses.FirstOrDefault(a => a.Id == request.AddressId);
        if (targetAddress == null)
        {
            return Result.Failure("Adres bulunamadı.");
        }
        foreach (var address in addresses.Where(a => a.IsDefault))
        {
            address.IsDefault = false;
        }
        targetAddress.IsDefault = true;
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
