using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Addresses.DeleteAddress;

public class DeleteAddressCommandValidator : AbstractValidator<DeleteAddressCommand>
{
    private readonly ICustomersDbContext _context;

    public DeleteAddressCommandValidator(ICustomersDbContext context)
    {
        _context = context;

        RuleFor(x => x.CustomerId)
            .NotEmpty()
            .MustAsync(async (customerId, cancellation) =>
                await _context.Customers.AnyAsync(c => c.Id == customerId, cancellation))
            .WithMessage("Müşteri bulunamadı.");

        RuleFor(x => x)
            .MustAsync(async (command, cancellation) =>
                await _context.Address.AnyAsync(a =>
                    a.Id == command.AddressId &&
                    a.CustomerId == command.CustomerId,
                    cancellation))
            .WithMessage("Adres bulunamadı veya müşteriye ait değil.");
    }
}
