using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Addresses.DeleteAddress;

public class DeleteAddressEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("api/v1/customers/customers/{customerId}/addresses/{addressId}", async (
            Guid customerId,
            Guid addressId,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new DeleteAddressCommand
            {
                CustomerId = customerId,
                AddressId = addressId
            };

            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses");
    }
}
