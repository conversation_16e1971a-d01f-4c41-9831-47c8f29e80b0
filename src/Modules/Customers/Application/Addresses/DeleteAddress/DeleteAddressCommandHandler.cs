using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.DeleteAddress;

public class DeleteAddressCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<DeleteAddressCommand, Result>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result> Handle(DeleteAddressCommand request, CancellationToken cancellationToken)
    {
        var address = await _context.Address
            .FirstOrDefaultAsync(a =>
                a.Id == request.AddressId &&
                a.CustomerId == request.CustomerId,
                cancellationToken);
        if (address == null)
        {
            return Result.Failure("404", "Adres bulunamadı.");
        }
        if (address.IsDefault)
        {
            var newDefaultAddress = await _context.Address
                .Where(a => a.CustomerId == request.CustomerId && a.Id != request.AddressId)
                .FirstOrDefaultAsync(cancellationToken);
            if (newDefaultAddress is not null)
            {
                newDefaultAddress.IsDefault = true;
            }
        }
        _context.Address.Remove(address);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
