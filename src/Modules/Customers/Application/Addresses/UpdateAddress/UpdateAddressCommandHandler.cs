using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.UpdateAddress;

public class UpdateAddressCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<UpdateAddressCommand, Result>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result> Handle(UpdateAddressCommand request, CancellationToken cancellationToken)
    {
        var address = await _context.Address
            .FirstOrDefaultAsync(a =>
                a.Id == request.AddressId &&
                a.CustomerId == request.CustomerId,
                cancellationToken);
        if (address == null)
        {
            return Result.Failure("Adres bulunamadı.");
        }
        if (request.IsDefault && !address.IsDefault)
        {
            var existingDefaultAddresses = await _context.Address
                .Where(a => a.CustomerId == request.CustomerId && a.IsDefault)
                .ToListAsync(cancellationToken);

            foreach (var defaultAddress in existingDefaultAddresses)
            {
                defaultAddress.IsDefault = false;
            }
        }
        address.Title = request.Title;
        address.Phone = request.Phone;
        address.PhonePrefix = request.PhonePrefix;
        address.Email = request.Email;
        address.Country = request.Country;
        address.State = request.State;
        address.City = request.City;
        address.Province = request.Province;
        address.PostCode = request.PostCode;
        address.Detail = request.Detail;
        address.IsDefault = request.IsDefault;

        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
