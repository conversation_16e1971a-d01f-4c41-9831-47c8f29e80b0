using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Addresses.UpdateAddress;

public class UpdateAddressEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("api/v1/customers/customers/{customerId}/addresses/{addressId}", async (
            Guid customerId,
            Guid addressId,
            UpdateAddressCommand command,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            command = command with { CustomerId = customerId, AddressId = addressId };
            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses");
    }
}
