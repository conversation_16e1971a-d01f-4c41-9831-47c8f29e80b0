using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.GetAddresses;

public class GetAddressesQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<GetAddressesQuery, Result<List<AddressDto>>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<List<AddressDto>>> Handle(GetAddressesQuery request, CancellationToken cancellationToken)
    {
        var addresses = await _context.Address
            .Where(a => a.CustomerId == request.CustomerId)
            .Select(a => new AddressDto
            {
                Id = a.Id,
                Title = a.Title,
                Phone = a.Phone,
                PhonePrefix = a.PhonePrefix,
                Email = a.Email,
                Country = a.Country,
                State = a.State,
                City = a.City,
                Province = a.Province,
                PostCode = a.PostCode,
                Detail = a.Detail,
                IsDefault = a.IsDefault
            })
            .ToListAsync(cancellationToken);

        return Result.Success(addresses);
    }
}
