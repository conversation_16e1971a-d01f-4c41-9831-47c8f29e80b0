using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Addresses.GetAddressesFilter;


public class GetAddressesFilterEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/addresses/filters", async (
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new GetAddressesFilterQuery(), cancellationToken);

            return result.Match(
                Results.Ok,
                CustomResults.Problem
            );
        })
        .WithTags("Customers.Addresses")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Addresses")
        .WithName("GetAddressFilters")
        .WithSummary("Get filter options for addresses")
        .WithDescription("Returns available country, state, city, province and postcode values based on existing addresses.");
    }
}
