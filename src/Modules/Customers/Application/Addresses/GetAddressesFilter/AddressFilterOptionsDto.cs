using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Addresses.GetAddressesFilter;

public class AddressFilterOptionsDto
{
    public List<string> Countries { get; set; } = [];
    public List<string> States { get; set; } = [];
    public List<string> Cities { get; set; } = [];
    public List<string> Provinces { get; set; } = [];
    public List<string> PostCodes { get; set; } = [];
}
