using Customers.Application.Abstractions;
using Customers.Application.Addresses.GetAddressesFilter;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Addresses.GetAddresses;

internal sealed class GetAddressesFilterQueryHandler : IRequestHandler<GetAddressesFilterQuery, Result<AddressFilterOptionsDto>>
{
    private readonly ICustomersDbContext _dbContext;

    public GetAddressesFilterQueryHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<AddressFilterOptionsDto>> Handle(GetAddressesFilterQuery request, CancellationToken cancellationToken)
    {
        var countries = await _dbContext.Address
            .Where(x => !string.IsNullOrEmpty(x.Country))
            .Select(x => x.Country!)
            .Distinct()
            .ToListAsync(cancellationToken);

        var states = await _dbContext.Address
            .Where(x => !string.IsNullOrEmpty(x.State))
            .Select(x => x.State!)
            .Distinct()
            .ToListAsync(cancellationToken);

        var cities = await _dbContext.Address
            .Where(x => !string.IsNullOrEmpty(x.City))
            .Select(x => x.City!)
            .Distinct()
            .ToListAsync(cancellationToken);

        var provinces = await _dbContext.Address
            .Where(x => !string.IsNullOrEmpty(x.Province))
            .Select(x => x.Province!)
            .Distinct()
            .ToListAsync(cancellationToken);

        var postCodes = await _dbContext.Address
            .Where(x => !string.IsNullOrEmpty(x.PostCode))
            .Select(x => x.PostCode!)
            .Distinct()
            .ToListAsync(cancellationToken);

        var dto = new AddressFilterOptionsDto
        {
            Countries = countries,
            States = states,
            Cities = cities,
            Provinces = provinces,
            PostCodes = postCodes
        };

        return Result.Success(dto);
    }
}
