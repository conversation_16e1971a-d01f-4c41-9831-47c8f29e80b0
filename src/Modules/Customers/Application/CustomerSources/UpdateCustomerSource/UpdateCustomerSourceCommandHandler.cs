using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.CustomerSources.UpdateCustomerSource;

public sealed class UpdateCustomerSourceCommandHandler(ICustomersDbContext context)
    : IRequestHandler<UpdateCustomerSourceCommand, Result>
{
    public async Task<Result> Handle(UpdateCustomerSourceCommand command, CancellationToken cancellationToken)
    {
        var customerSource = await context.CustomerSource
            .FirstOrDefaultAsync(x => x.Id == command.Id, cancellationToken);
        if (customerSource is null)
        {
            return Result.Failure("Müşteri kaynağı bulunamadı.");
        }
        customerSource.Name = command.Name;
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
