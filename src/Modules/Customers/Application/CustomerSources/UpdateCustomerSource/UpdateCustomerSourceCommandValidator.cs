using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.CustomerSources.UpdateCustomerSource;

public sealed class UpdateCustomerSourceCommandValidator : AbstractValidator<UpdateCustomerSourceCommand>
{
    public UpdateCustomerSourceCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Müşteri kaynağı ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Müşteri kaynağı adı boş olamaz.")
            .MaximumLength(100)
            .WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
            .MustAsync(async (command, name, cancellationToken) =>
                !await context.CustomerSource.AnyAsync(
                    x => x.Name == name && x.Id != command.Id,
                    cancellationToken))
            .WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
    }
}
