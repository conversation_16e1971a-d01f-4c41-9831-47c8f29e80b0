using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.CustomerSources.GetCustomerSource;

public sealed class GetCustomerSourceQueryHandler(ICustomersDbContext context)
    : IRequestHandler<GetCustomerSourceQuery, Result<CustomerSourceDto>>
{
    public async Task<Result<CustomerSourceDto>> Handle(GetCustomerSourceQuery query, CancellationToken cancellationToken)
    {
        var customerSource = await context.CustomerSource
            .FirstOrDefaultAsync(x => x.Id == query.Id, cancellationToken);
        if (customerSource is null)
        {
            return Result.Failure<CustomerSourceDto>("404", "Müşteri kaynağı bulunamadı.");
        }
        return Result.Success(new CustomerSourceDto(
            customerSource.Id,
            customerSource.Name));
    }
}
