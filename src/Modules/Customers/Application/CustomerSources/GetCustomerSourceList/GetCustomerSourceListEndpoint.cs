using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.CustomerSources.GetCustomerSourceList;

internal sealed class GetCustomerSourceListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/customers/sources", async (
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCustomerSourceListQuery();
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Customers.Sources")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
