using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.CustomerSources.GetCustomerSourceList;

public sealed class GetCustomerSourceListQueryHandler(ICustomersDbContext context)
    : IRequestHandler<GetCustomerSourceListQuery, Result<List<CustomerSourceListDto>>>
{
    public async Task<Result<List<CustomerSourceListDto>>> Handle(
        GetCustomerSourceListQuery query,
        CancellationToken cancellationToken)
    {
        var customerSources = await context.CustomerSource
            .OrderBy(x => x.Name)
            .Select(x => new CustomerSourceListDto(
                x.Id,
                x.Name))
            .ToListAsync(cancellationToken);

        return Result.Success(customerSources);
    }
}
