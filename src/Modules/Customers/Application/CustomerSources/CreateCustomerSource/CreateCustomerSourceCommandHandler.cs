using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.CustomerSources.CreateCustomerSource;

public sealed class CreateCustomerSourceCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateCustomerSourceCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateCustomerSourceCommand command, CancellationToken cancellationToken)
    {
        var customerSource = new CustomerSource
        {
            Name = command.Name
        };
        context.CustomerSource.Add(customerSource);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(customerSource.Id);
    }
}
