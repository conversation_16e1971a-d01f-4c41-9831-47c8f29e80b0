using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.CustomerSources.CreateCustomerSource;

public sealed class CreateCustomerSourceCommandValidator : AbstractValidator<CreateCustomerSourceCommand>
{
    public CreateCustomerSourceCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Müşteri kaynağı adı boş olamaz.")
            .MaximumLength(100).WithMessage("Müşteri kaynağı adı 100 karakterden uzun olamaz.")
            .MustAsync(async (name, cancellationToken) =>
                !await context.CustomerSource.AnyAsync(x => x.Name == name, cancellationToken))
            .WithMessage("Bu isimde bir müşteri kaynağı zaten mevcut.");
    }
}
