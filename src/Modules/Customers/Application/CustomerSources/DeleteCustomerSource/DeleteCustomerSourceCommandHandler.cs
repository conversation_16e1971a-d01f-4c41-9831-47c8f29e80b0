using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.CustomerSources.DeleteCustomerSource;

public sealed class DeleteCustomerSourceCommandHandler(ICustomersDbContext context)
    : IRequestHandler<DeleteCustomerSourceCommand, Result>
{
    public async Task<Result> Handle(DeleteCustomerSourceCommand command, CancellationToken cancellationToken)
    {
        var customerSource = await context.CustomerSource
            .FirstOrDefaultAsync(x => x.Id == command.Id, cancellationToken);

        if (customerSource is null)
            return Result.Failure("Müşteri kaynağı bulunamadı.");

        context.CustomerSource.Remove(customerSource);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
