using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.CustomerSources.DeleteCustomerSource;

internal sealed class DeleteCustomerSourceEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("/api/v1/customers/sources/{id}", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new DeleteCustomerSourceCommand(id);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                Results.NoContent,
                CustomResults.Problem);
        })
        .WithTags("Customers.Sources")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Sources");
    }
}
