using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Professions.CreateProfession;

public class CreateProfessionCommandValidator : AbstractValidator<CreateProfessionCommand>
{
    private readonly ICustomersDbContext _dbContext;

    public CreateProfessionCommandValidator(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Meslek adı boş olamaz.")
            .MaximumLength(100).WithMessage("Meslek adı 100 karakterden uzun olamaz.")
            .MustAsync(BeUniqueName).WithMessage("Bu meslek adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.Profession
            .AnyAsync(p => p.Name == name, cancellationToken);
    }
}
