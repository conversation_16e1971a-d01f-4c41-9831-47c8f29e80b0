using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Professions.CreateProfession;

internal sealed class CreateProfessionEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/professions", async (
            CreateProfessionCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/customers/professions/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Customers.Professions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Professions");
    }
}
