using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Professions.CreateProfession;

public class CreateProfessionCommandHandler(
    ICustomersDbContext dbContext
) : IRequestHandler<CreateProfessionCommand, Result<Guid>>
{
    private readonly ICustomersDbContext _dbContext = dbContext;

    public async Task<Result<Guid>> Handle(CreateProfessionCommand request, CancellationToken cancellationToken)
    {
        var profession = new Profession
        {
            Id = Guid.NewGuid(),
            Name = request.Name
        };

        _dbContext.Profession.Add(profession);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(profession.Id);
    }
}
