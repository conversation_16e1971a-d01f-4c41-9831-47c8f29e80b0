using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Professions.GetProfession;

public class GetProfessionQueryHandler : IRequestHandler<GetProfessionQuery, Result<ProfessionDto>>
{
    private readonly ICustomersDbContext _dbContext;

    public GetProfessionQueryHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<ProfessionDto>> Handle(GetProfessionQuery request, CancellationToken cancellationToken)
    {
        var profession = await _dbContext.Profession
            .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

        if (profession == null)
        {
            return Result.Failure<ProfessionDto>("404", "Meslek bulunamadı.");
        }

        var result = new ProfessionDto(
            profession.Id,
            profession.Name);

        return Result.Success(result);
    }
}
