using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Professions.DeleteProfession;

public class DeleteProfessionCommandHandler : IRequestHandler<DeleteProfessionCommand, Result>
{
    private readonly ICustomersDbContext _dbContext;

    public DeleteProfessionCommandHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result> Handle(DeleteProfessionCommand request, CancellationToken cancellationToken)
    {
        var profession = await _dbContext.Profession
            .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

        if (profession == null)
        {
            return Result.Failure("404", "Meslek bulunamadı.");
        }

        // Check if there are any customers using this profession
        var isUsed = await _dbContext.Customers
            .AnyAsync(c => c.ProfessionId == request.Id, cancellationToken);

        if (isUsed)
        {
            return Result.Failure("400", "Bu meslek kullanımda olduğu için silinemez.");
        }

        _dbContext.Profession.Remove(profession);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
