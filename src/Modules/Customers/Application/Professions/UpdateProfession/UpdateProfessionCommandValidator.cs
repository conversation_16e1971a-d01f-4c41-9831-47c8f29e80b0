using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Professions.UpdateProfession;

public class UpdateProfessionCommandValidator : AbstractValidator<UpdateProfessionCommand>
{
    private readonly ICustomersDbContext _dbContext;

    public UpdateProfessionCommandValidator(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Meslek ID'si boş olamaz.")
            .MustAsync(BeExistingProfession).WithMessage("Belirtilen meslek bulunamadı.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Meslek adı boş olamaz.")
            .MaximumLength(100).WithMessage("Meslek adı 100 karakterden uzun olamaz.")
            .MustAsync(BeUniqueName).WithMessage("Bu meslek adı zaten kullanılıyor.");
    }

    private async Task<bool> BeExistingProfession(Guid id, CancellationToken cancellationToken)
    {
        return await _dbContext.Profession
            .AnyAsync(p => p.Id == id, cancellationToken);
    }

    private async Task<bool> BeUniqueName(UpdateProfessionCommand command, string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.Profession
            .AnyAsync(p => p.Name == name && p.Id != command.Id, cancellationToken);
    }
}
