using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Professions.UpdateProfession;

public class UpdateProfessionCommandHandler : IRequestHandler<UpdateProfessionCommand, Result>
{
    private readonly ICustomersDbContext _dbContext;

    public UpdateProfessionCommandHandler(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result> Handle(UpdateProfessionCommand request, CancellationToken cancellationToken)
    {
        var profession = await _dbContext.Profession
            .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

        if (profession == null)
        {
            return Result.Failure("Profession.NotFound", "Meslek bulunamadı.");
        }

        profession.Name = request.Name;

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
