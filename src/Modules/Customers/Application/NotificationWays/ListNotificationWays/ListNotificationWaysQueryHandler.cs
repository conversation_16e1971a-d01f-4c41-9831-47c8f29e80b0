using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.NotificationWays.ListNotificationWays;

internal sealed class ListNotificationWaysQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<ListNotificationWaysQuery, Result<PagedResult<NotificationWayDto>>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<PagedResult<NotificationWayDto>>> Handle(ListNotificationWaysQuery request, CancellationToken cancellationToken)
    {
        var query = _context.NotificationWays
            .AsNoTracking();
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(nw => nw.Name.ToLower().Contains(searchTerm));
        }
        var totalCount = await query.CountAsync(cancellationToken);
        var notificationWays = await query
            .OrderBy(nw => nw.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(nw => new NotificationWayDto(nw.Id, nw.Name))
            .ToListAsync(cancellationToken);
        var result = PagedResult<NotificationWayDto>.Success(notificationWays);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = totalCount;
        return result;
    }
}
