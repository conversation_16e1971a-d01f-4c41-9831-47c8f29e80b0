using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.NotificationWays.CreateNotificationWay;

internal sealed class CreateNotificationWayEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/notification-ways", async (
            CreateNotificationWayCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            if (result.IsSuccess)
            {
                return Results.Created($"/api/v1/customers/notification-ways/{result.Value}", result.Value);
            }
            return CustomResults.Problem(result);
        })
        .WithTags("Customers.NotificationWays")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.NotificationWays");
    }
}
