using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.NotificationWays.CreateNotificationWay;

internal sealed class CreateNotificationWayCommandHandler : IRequestHandler<CreateNotificationWayCommand, Result<Guid>>
{
    private readonly ICustomersDbContext _context;

    public CreateNotificationWayCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result<Guid>> Handle(CreateNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var existingNotificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Name == request.Name, cancellationToken);
        if (existingNotificationWay != null)
        {
            return Result.Failure<Guid>("Bu isimde bir bildirim kanalı zaten mevcut");
        }
        var notificationWay = new NotificationWay
        {
            Name = request.Name
        };
        _context.NotificationWays.Add(notificationWay);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success(notificationWay.Id);
    }
}
