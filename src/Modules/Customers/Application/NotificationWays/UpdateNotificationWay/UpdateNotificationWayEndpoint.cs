using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.NotificationWays.UpdateNotificationWay;

internal sealed class UpdateNotificationWayEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/customers/notification-ways/{id}", async (
            Guid id,
            UpdateNotificationWayRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateNotificationWayCommand(id, request.Name);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.NotificationWays")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.NotificationWays");
    }
}
