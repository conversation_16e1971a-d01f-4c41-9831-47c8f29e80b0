using FluentValidation;

namespace Customers.Application.NotificationWays.UpdateNotificationWay;

internal sealed class UpdateNotificationWayCommandValidator : AbstractValidator<UpdateNotificationWayCommand>
{
    public UpdateNotificationWayCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Bildirim kanalı adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Bildirim kanalı adı 100 karakteri geçemez");
    }
}
