using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.NotificationWays.UpdateNotificationWay;

internal sealed class UpdateNotificationWayCommandHandler : IRequestHandler<UpdateNotificationWayCommand, Result>
{
    private readonly ICustomersDbContext _context;

    public UpdateNotificationWayCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(UpdateNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Id == request.Id, cancellationToken);
        if (notificationWay == null)
        {
            return Result.Failure("Bildirim kanalı bulunamadı");
        }
        var existingWithSameName = await _context.NotificationWays
            .AnyAsync(nw => nw.Name == request.Name && nw.Id != request.Id, cancellationToken);
        if (existingWithSameName)
        {
            return Result.Failure("Bu isimde başka bir bildirim kanalı zaten mevcut");
        }
        notificationWay.Name = request.Name;
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
