using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.NotificationWays.GetNotificationWay;

internal sealed class GetNotificationWayQueryHandler : IRequestHandler<GetNotificationWayQuery, Result<NotificationWayResponse>>
{
    private readonly ICustomersDbContext _context;

    public GetNotificationWayQueryHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result<NotificationWayResponse>> Handle(GetNotificationWayQuery request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .Where(nw => nw.Id == request.Id)
            .Select(nw => new NotificationWayResponse(nw.Id, nw.Name))
            .FirstOrDefaultAsync(cancellationToken);
        if (notificationWay == null)
        {
            return Result.Failure<NotificationWayResponse>("Bildirim kanalı bulunamadı");
        }
        return Result.Success(notificationWay);
    }
}
