using Customers.Application.Abstractions;
using Customers.Domain.Events;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.NotificationWays.DeleteNotificationWay;

internal sealed class DeleteNotificationWayCommandHandler : IRequestHandler<DeleteNotificationWayCommand, Result>
{
    private readonly ICustomersDbContext _context;

    public DeleteNotificationWayCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(DeleteNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Id == request.Id, cancellationToken);
        if (notificationWay == null)
        {
            return Result.Failure("Bildirim kanalı bulunamadı");
        }
        _context.NotificationWays.Remove(notificationWay);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
