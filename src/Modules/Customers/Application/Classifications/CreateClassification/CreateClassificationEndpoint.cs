using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Classifications.CreateClassification;

internal sealed class CreateClassificationEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/classifications", async (
            CreateClassificationCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/customers/classifications/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Customers.Classifications")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Classifications");
    }
}
