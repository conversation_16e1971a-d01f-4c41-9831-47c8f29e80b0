using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Classifications.CreateClassification;

public class CreateClassificationCommandValidator : AbstractValidator<CreateClassificationCommand>
{
    public CreateClassificationCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Müşteri sınıfı adı boş olamaz.")
            .MaximumLength(100).WithMessage("Müşteri sınıfı adı 100 karakterden uzun olamaz.")
            .MustAsync(async (name, ct) => !await context.Classification
                .AnyAsync(x => x.Name == name, ct))
            .WithMessage("Bu isimde bir müşteri sınıfı zaten mevcut.");
    }
}
