using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Classifications.CreateClassification;

public class CreateClassificationCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateClassificationCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateClassificationCommand request, CancellationToken cancellationToken)
    {
        var classification = new Classification()
        {
            Name = request.Name
        };
        context.Classification.Add(classification);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(classification.Id);
    }
}
