using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Classifications.GetClassification;

public class GetClassificationQueryHandler(ICustomersDbContext context)
    : IRequestHandler<GetClassificationQuery, Result<ClassificationDetailDto>>
{
    public async Task<Result<ClassificationDetailDto>> Handle(
        GetClassificationQuery request,
        CancellationToken cancellationToken)
    {
        var classification = await context.Classification
            .Where(x => x.Id == request.Id)
            .Select(x => new ClassificationDetailDto(x.Id, x.Name))
            .FirstOrDefaultAsync(cancellationToken);

        if (classification is null)
        {
            return Result.Failure<ClassificationDetailDto>("Müşteri sınıfı bulunamadı.");
        }

        return Result.Success(classification);
    }
}
