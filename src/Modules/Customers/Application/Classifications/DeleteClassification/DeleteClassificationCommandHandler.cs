using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Classifications.DeleteClassification;

public class DeleteClassificationCommandHandler(ICustomersDbContext context)
    : IRequestHandler<DeleteClassificationCommand, Result>
{
    public async Task<Result> Handle(DeleteClassificationCommand request, CancellationToken cancellationToken)
    {
        var classification = await context.Classification.FindAsync(
            [request.Id],
            cancellationToken);

        if (classification is null)
        {
            return Result.Failure("Müşteri sınıfı bulunamadı.");
        }

        // Check if classification is being used by any customers
        var isUsed = await context.Customers
            .AnyAsync(c => c.CustomerClassifications.Any(x => x.ClassificationId == request.Id), cancellationToken);

        if (isUsed)
        {
            return Result.Failure("Bu sınıflandırma müşteriler tarafından kullanılmaktadır ve silinemez.");
        }

        context.Classification.Remove(classification);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
