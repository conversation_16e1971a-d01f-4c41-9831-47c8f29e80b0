using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Classifications.UpdateClassification;

public class UpdateClassificationCommandValidator : AbstractValidator<UpdateClassificationCommand>
{
    public UpdateClassificationCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Müşteri sınıfı ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Müşteri sınıfı adı boş olamaz.")
            .MaximumLength(100).WithMessage("Müşteri sınıfı adı 100 karakterden uzun olamaz.")
            .MustAsync(async (command, name, ct) => !await context.Classification
                .AnyAsync(x => x.Name == name && x.Id != command.Id, ct))
            .WithMessage("Bu isimde bir müşteri sınıfı zaten mevcut.");
    }
}
