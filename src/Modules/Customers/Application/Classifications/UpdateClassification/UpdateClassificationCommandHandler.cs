using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Classifications.UpdateClassification;

public class UpdateClassificationCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<UpdateClassificationCommand, Result>
{
    public async Task<Result> Handle(UpdateClassificationCommand request, CancellationToken cancellationToken)
    {
        var classification = await context.Classification
            .FindAsync([request.Id], cancellationToken);

        if (classification is null)
        {
            return Result.Failure("Müşteri sınıfı bulunamadı.");
        }
        classification.Name = request.Name;
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
