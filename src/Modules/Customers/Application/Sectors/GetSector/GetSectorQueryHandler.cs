using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Sectors.GetSector;

internal sealed class GetSectorQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<GetSectorQuery, Result<SectorDto>>
{
    public async Task<Result<SectorDto>> Handle(GetSectorQuery request, CancellationToken cancellationToken)
    {
        var entity = await context.Sector
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (entity == null)
        {
            return Result.Failure<SectorDto>($"Sektör bulunamadı (ID: {request.Id})");
        }

        var dto = new SectorDto(
            entity.Id,
            entity.Name);

        return Result.Success(dto);
    }
}
