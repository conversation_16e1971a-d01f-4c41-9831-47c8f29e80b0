using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Sectors.UpdateSector;

public class UpdateSectorCommandValidator : AbstractValidator<UpdateSectorCommand>
{
    private readonly ICustomersDbContext _dbContext;

    public UpdateSectorCommandValidator(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(v => v.Id)
            .NotEmpty().WithMessage("ID boş olamaz.");

        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Sektör adı boş olamaz.")
            .MaximumLength(100).WithMessage("Sektör adı 100 karakterden uzun olamaz.")
            .MustAsync(BeUniqueName).WithMessage("Bu meslek adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueName(UpdateSectorCommand command, string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.Profession
            .AnyAsync(p => p.Name == name && p.Id != command.Id, cancellationToken);
    }
}
