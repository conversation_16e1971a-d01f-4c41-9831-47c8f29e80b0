using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Sectors.UpdateSector;

internal sealed class UpdateSectorCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<UpdateSectorCommand, Result>
{
    public async Task<Result> Handle(UpdateSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = await context.Sector
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (entity == null)
        {
            return Result.Failure($"Sektör bulunamadı (ID: {request.Id})");
        }

        entity.Name = request.Name;
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
