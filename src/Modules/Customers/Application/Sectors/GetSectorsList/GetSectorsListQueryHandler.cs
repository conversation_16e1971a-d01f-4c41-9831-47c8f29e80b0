using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Sectors.GetSectorsList;

public class GetSectorsListQueryHandler(
    ICustomersDbContext dbContext
) : IRequestHandler<GetSectorsListQuery, PagedResult<SectorListDto>>
{
    private readonly ICustomersDbContext _dbContext = dbContext;

    public async Task<PagedResult<SectorListDto>> Handle(GetSectorsListQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Sector.AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query.Where(p => p.Name.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await _dbContext.Sector.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var sectors = await query
            .OrderBy(p => p.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(p => new SectorListDto(
                p.Id,
                p.Name))
            .ToListAsync(cancellationToken);
        if (request.CustomerCount == true)
        {
            foreach (var item in sectors)
            {
                item.CustomerCount = _dbContext.Customers.Count(c => c.SectorId == item.Id);
            }
        }
        var result = PagedResult<SectorListDto>.Success(sectors);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
