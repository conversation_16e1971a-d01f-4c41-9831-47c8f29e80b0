using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Sectors.DeleteSector;

internal sealed class DeleteSectorCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<DeleteSectorCommand, Result>
{
    public async Task<Result> Handle(DeleteSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = await context.Sector
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (entity == null)
        {
            return Result.Failure($"Sektör bulunamadı (ID: {request.Id})");
        }

        // Check if any customers are using this sector
        var hasCustomers = await context.Customers
            .AnyAsync(c => c.SectorId == request.Id, cancellationToken);

        if (hasCustomers)
        {
            return Result.Failure("Bu sektöre atanmış müşteriler olduğu için silinemez.");
        }

        context.Sector.Remove(entity);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
