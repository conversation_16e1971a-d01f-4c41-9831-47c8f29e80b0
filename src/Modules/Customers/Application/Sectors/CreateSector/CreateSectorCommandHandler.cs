using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Sectors.CreateSector;

internal sealed class CreateSectorCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateSectorCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = new Sector
        {
            Id = Guid.NewGuid(),
            Name = request.Name
        };

        context.Sector.Add(entity);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(entity.Id);
    }
}
