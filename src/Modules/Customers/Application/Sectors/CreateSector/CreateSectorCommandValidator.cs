using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Sectors.CreateSector;

public class CreateSectorCommandValidator : AbstractValidator<CreateSectorCommand>
{
    private readonly ICustomersDbContext _dbContext;
    public CreateSectorCommandValidator(ICustomersDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Sektör adı boş olamaz.")
            .MaximumLength(100).WithMessage("Sektör adı 100 karakterden uzun olamaz.")
            .MustAsync(BeUniqueName).WithMessage("Bu sektör adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
    {
        return !await _dbContext.Profession
            .AnyAsync(p => p.Name == name, cancellationToken);
    }
}
