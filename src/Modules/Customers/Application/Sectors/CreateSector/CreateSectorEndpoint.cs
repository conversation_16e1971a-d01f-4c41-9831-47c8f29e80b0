using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Sectors.CreateSector;

internal sealed class CreateSectorEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/sectors", async (
            CreateSectorCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/customers/sectors/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Customers.Sectors")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Sectors");
    }
}
