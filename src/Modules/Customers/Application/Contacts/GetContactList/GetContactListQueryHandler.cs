using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Contacts.GetContactList;

internal sealed class GetContactListQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<GetContactListQuery, Result<GetContactListResponse>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<GetContactListResponse>> Handle(
        GetContactListQuery request,
        CancellationToken cancellationToken)
    {
        var query = _context.Contacts.AsNoTracking();

        if (request.CustomerId.HasValue)
        {
            query = query.Where(x => x.CustomerId == request.CustomerId);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(x =>
                x.ContactInfo.ToLower().Contains(searchTerm) ||
                x.Name.ToLower().Contains(searchTerm) ||
                x.Surname.ToLower().Contains(searchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(x => x.IsDefault)
            .ThenBy(x => x.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new ContactDto(
                x.Id,
                x.CustomerId,
                x.ContactInfo,
                x.IsDefault,
                x.Name,
                x.Surname,
                x.Email,
                x.Phone,
                x.PhonePrefix,
                x.Language,
                x.Type.ToString(),
                x.Status,
                x.IsNotification,
                x.IsSms,
                x.IsEmail))
            .ToListAsync(cancellationToken);

        var response = new GetContactListResponse(
            totalCount,
            request.PageNumber,
            request.PageSize,
            items);

        return Result.Success(response);
    }
}
