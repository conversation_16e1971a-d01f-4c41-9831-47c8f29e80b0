using FluentValidation;

namespace Customers.Application.Contacts.GetContactList;

public class GetContactListQueryValidator : AbstractValidator<GetContactListQuery>
{
    public GetContactListQueryValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .LessThanOrEqualTo(100)
            .WithMessage("Page size must be between 1 and 100");

        When(x => !string.IsNullOrEmpty(x.SearchTerm), () =>
        {
            RuleFor(x => x.SearchTerm)
                .MinimumLength(2)
                .MaximumLength(50)
                .WithMessage("Search term must be between 2 and 50 characters");
        });
    }
}
