using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.GetContactList;

public record GetContactListQuery(
    Guid? CustomerId = null,
    int PageNumber = 1,
    int PageSize = 10,
    string? SearchTerm = null
) : IRequest<Result<GetContactListResponse>>;

public record GetContactListResponse(
    int TotalCount,
    int PageNumber,
    int PageSize,
    IReadOnlyList<ContactDto> Items);

public record ContactDto(
    Guid Id,
    Guid CustomerId,
    string ContactInfo,
    bool IsDefault,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string Language,
    string Type,
    bool Status,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail);
