using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Contacts.GetContact;

internal sealed class GetContactQueryHandler(
    ICustomersDbContext context
) : IRequestHand<PERSON><GetContactQuery, Result<GetContactResponse>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<GetContactResponse>> Handle(
        GetContactQuery request,
        CancellationToken cancellationToken)
    {
        var contact = await _context.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (contact is null)
        {
            return Result.Failure<GetContactResponse>("Contact not found");
        }

        var response = new GetContactResponse(
            contact.Id,
            contact.CustomerId,
            contact.ContactInfo,
            contact.IsDefault,
            contact.Name,
            contact.Surname,
            contact.Email,
            contact.Phone,
            contact.PhonePrefix,
            contact.Language,
            contact.Type.ToString(),
            contact.Status,
            contact.IsNotification,
            contact.IsSms,
            contact.IsEmail);

        return Result.Success(response);
    }
}
