using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.GetContact;

public record GetContactQuery(
    Guid Id
) : IRequest<Result<GetContactResponse>>;

public record GetContactResponse(
    Guid Id,
    Guid CustomerId,
    string ContactInfo,
    bool IsDefault,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string Language,
    string Type,
    bool Status,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail);
