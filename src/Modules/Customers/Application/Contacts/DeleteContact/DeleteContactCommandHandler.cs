using Customers.Application.Abstractions;
using Customers.Domain.Events;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Shared.Application;

namespace Customers.Application.Contacts.DeleteContact;

internal sealed class DeleteContactCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<DeleteContactCommand, Result<Unit>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<Unit>> Handle(
        DeleteContactCommand request,
        CancellationToken cancellationToken)
    {
        var contact = await _context.Contacts
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (contact is null)
        {
            return Result.Failure<Unit>("Contact not found");
        }

        // Store values before deletion for cache invalidation
        var customerId = contact.CustomerId;
        var contactType = contact.Type;

        _context.Contacts.Remove(contact);

        // Raise domain event before saving changes
        contact.Raise(new ContactDeletedEvent(
            contact.Id,
            contact.CustomerId));

        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success(Unit.Value);
    }
}
