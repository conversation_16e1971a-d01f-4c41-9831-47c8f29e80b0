using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Contacts.UpdateContact;

internal sealed class UpdateContactHandler(
    ICustomersDbContext context
) : IRequestHandler<UpdateContactCommand, Result>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result> Handle(UpdateContactCommand request, CancellationToken cancellationToken)
    {
        var contact = await _context.Contacts
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (contact is null)
        {
            return Result.Failure("Contact not found");
        }

        if (request.IsDefault)
        {
            var otherDefaultContacts = await _context.Contacts
                .Where(x => x.CustomerId == contact.CustomerId &&
                        x.Type == contact.Type &&
                        x.Id != contact.Id &&
                        x.IsDefault)
                .ToListAsync(cancellationToken);

            foreach (var otherContact in otherDefaultContacts)
            {
                otherContact.SetAsDefault(false);
            }
        }

        contact.UpdateContactInfo(
            request.ContactInfo,
            request.Name,
            request.Surname,
            request.Email,
            request.Phone,
            request.PhonePrefix,
            request.Language,
            request.Status,
            request.IsNotification,
            request.IsSms,
            request.IsEmail);

        contact.SetAsDefault(request.IsDefault);

        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
