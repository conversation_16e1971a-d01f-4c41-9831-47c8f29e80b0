using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.UpdateContact;

public sealed record UpdateContactCommand(
    Guid Id,
    string ContactInfo,
    bool IsDefault,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string Language,
    bool Status,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail
) : IRequest<Result>;
