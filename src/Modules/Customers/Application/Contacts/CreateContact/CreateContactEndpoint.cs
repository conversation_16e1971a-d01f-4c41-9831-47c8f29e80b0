using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Contacts.CreateContact;

internal sealed class CreateContactEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/contacts/create", async (
            CreateContactCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                success => Results.Created($"/api/customers/contacts/get/{success}", success),
                CustomResults.Problem);
        })
        .WithTags("Customers.Contacts")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Contacts");
    }
}
