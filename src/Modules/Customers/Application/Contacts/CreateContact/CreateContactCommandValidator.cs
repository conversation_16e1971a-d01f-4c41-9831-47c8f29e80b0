using FluentValidation;

namespace Customers.Application.Contacts.CreateContact;

public class CreateContactCommandValidator : AbstractValidator<CreateContactCommand>
{
    public CreateContactCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty()
            .WithMessage("Customer ID is required");

        RuleFor(x => x.ContactInfo)
            .NotEmpty()
            .WithMessage("Contact information is required")
            .MaximumLength(255)
            .WithMessage("Contact information cannot exceed 255 characters");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required")
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters");

        RuleFor(x => x.Surname)
            .NotEmpty()
            .WithMessage("Surname is required")
            .MaximumLength(100)
            .WithMessage("Surname cannot exceed 100 characters");
    }
}
