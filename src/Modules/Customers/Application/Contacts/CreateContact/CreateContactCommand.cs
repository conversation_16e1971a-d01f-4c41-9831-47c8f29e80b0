using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.CreateContact;

public record CreateContactCommand(
    Guid CustomerId,
    string ContactInfo,
    bool IsDefault,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string Language,
    ContactType Type,
    bool Status,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail
) : IRequest<Result<Guid>>;
