using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Contacts.CreateContact;

internal sealed class CreateContactCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateContactCommand, Result<Guid>>
{
    private readonly ICustomersDbContext _context = context;

    public async Task<Result<Guid>> Handle(
        CreateContactCommand request,
        CancellationToken cancellationToken)
    {
        var customer = await _context.Customers
            .Include(x => x.Contacts)
            .FirstOrDefaultAsync(x => x.Id == request.CustomerId, cancellationToken);

        if (customer is null)
        {
            return Result.Failure<Guid>(CustomerErrors.CustomerNotFound(request.CustomerId));
        }
        var contact = Contact.Create(
            request.CustomerId,
            request.ContactInfo,
            request.IsDefault,
            request.Name,
            request.Surname,
            request.Email,
            request.Phone,
            request.PhonePrefix,
            request.Language,
            request.Type,
            request.Status,
            request.IsNotification,
            request.IsSms,
            request.IsEmail);
        customer.AddContact(contact);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success(contact.Id);
    }
}
