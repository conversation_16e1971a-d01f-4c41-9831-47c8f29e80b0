using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Contacts.GetDefaultContact;

internal sealed class GetDefaultContactQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<GetDefaultContactQuery, Result<GetDefaultContactResponse>>
{
    public async Task<Result<GetDefaultContactResponse>> <PERSON><PERSON>(
        GetDefaultContactQuery request,
        CancellationToken cancellationToken)
    {
        var contact = await context.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x =>
                x.CustomerId == request.CustomerId &&
                x.Type == request.Type &&
                x.IsDefault,
                cancellationToken);

        if (contact is null)
        {
            return Result.Failure<GetDefaultContactResponse>(
                $"No default contact found for customer {request.CustomerId} with type {request.Type}");
        }

        var response = new GetDefaultContactResponse(
            contact.Id,
            contact.ContactInfo,
            contact.Name,
            contact.Surname,
            contact.Email,
            contact.Phone,
            contact.PhonePrefix,
            contact.Language,
            contact.Type.ToString(),
            contact.Status,
            contact.IsNotification,
            contact.IsSms,
            contact.IsEmail);

        return Result.Success(response);
    }
}
