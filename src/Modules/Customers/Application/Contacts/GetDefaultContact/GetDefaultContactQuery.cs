using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.GetDefaultContact;

public record GetDefaultContactQuery(
    Guid CustomerId,
    ContactType Type
) : IRequest<Result<GetDefaultContactResponse>>;

public record GetDefaultContactResponse(
    Guid Id,
    string ContactInfo,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string Language,
    string Type,
    bool Status,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail);
