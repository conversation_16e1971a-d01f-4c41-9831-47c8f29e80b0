using Customers.Domain;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Contacts.GetDefaultContact;

internal sealed class GetDefaultContactEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/customers/contacts/default/{customerId}/{type}", async (
            Guid customerId,
            ContactType type,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetDefaultContactQuery(customerId, type);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Customers.Contacts")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Contacts");
    }
}
