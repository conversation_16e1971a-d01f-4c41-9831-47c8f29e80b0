using Customers.Application.Abstractions;
using Customers.Application.Classifications.GetClassifications;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.GetTempCustomer;

internal sealed class GetTempCustomerQueryHandler(ICustomersDbContext context) : IRequestHandler<GetTempCustomerQuery, Result<GetTempCustomerResponse>>
{
    public async Task<Result<GetTempCustomerResponse>> Handle(GetTempCustomerQuery request, CancellationToken cancellationToken)
    {
        var customer = await context.TempCustomers 
            .Include(x => x.TempCustomerClassifications)
                .ThenInclude(x => x.Classification)   
            .Include(x => x.CustomerSource) 
            .FirstOrDefaultAsync(x => x.Id == request.Id && !x.IsDeleted, cancellationToken);

        if (customer is null)
        {
            return Result.Failure<GetTempCustomerResponse>(CustomerErrors.CustomerNotFound(request.Id));
        }


        var classifications = customer.TempCustomerClassifications != null
            ? customer.TempCustomerClassifications
                .Where(cc => cc.Classification != null)
                .Select(cc => new ClassificationDto(cc.ClassificationId, cc.Classification.Name))
                .ToList()
            : [];

        var result = new GetTempCustomerResponse(
            customer.Id,
            customer.Name,
            customer.Surname,
            customer.Email,
            customer.Phone,
            customer.PhonePrefix,
            customer.TaxOffice,
            customer.TaxNumber,
            customer.IdentificationNumber,
            customer.Country,
            customer.State,
            customer.City,
            customer.Province,
            customer.PostCode,
            customer.Address,
            customer.MainLanguage,
            customer.AvailableLanguage,
            customer.Description,
            customer.MailBcc,
            customer.Type,
            customer.Kind,
            customer.Status,
            customer.CustomerSourceId,
            customer.CustomerSource?.Name,
            customer.NotificationWayId,
            customer.NotificationWay?.Name,
            //customer.TempCustomerClassifications.Select(x => x.Classification.Name).ToArray(),
            classifications,
            customer.AdvisorId,
            customer.IsDeleted,
            customer.SectorId,
            customer.ProfessionId
        );

        return Result.Success(result);
    }

}
