using Customers.Application.Classifications.GetClassifications;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.GetTempCustomer;

public record GetTempCustomerQuery(Guid Id) : IRequest<Result<GetTempCustomerResponse>>;

public record GetTempCustomerResponse(
   Guid Id,
    string Name,
    string Surname,
    string? Email,
    string? Phone,
    string? PhonePrefix,
    string? TaxOffice,
    string? TaxNumber,
    string? IdentificationNumber,
    string? Country,
    string? State,
    string? City,
    string? Province,
    string? PostCode,
    string? Address,
    string? MainLanguage,
    string? AvailableLanguage,
    string? Description,
    string? MailBcc,
    CustomerType? Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    string? CustomerSourceName,
    Guid? NotificationWayId,
    string? NotificationWayName,
    //string[]? Classifications,
    List<ClassificationDto>? Classifications,
    Guid? AdvisorId,
    bool IsDeleted,
    Guid? SectorId,
    Guid? ProfessionId
    );

