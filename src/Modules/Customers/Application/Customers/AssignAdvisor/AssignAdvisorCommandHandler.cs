using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.AssignAdvisor;

internal sealed class AssignAdvisorCommandHandler(ICustomersDbContext context) : IRequestHandler<AssignAdvisorCommand, Result>
{
    public async Task<Result> Handle(AssignAdvisorCommand request, CancellationToken cancellationToken)
    {
        var customer = await context.Customers
            .FirstOrDefaultAsync(x => x.Id == request.CustomerId, cancellationToken);
        if (customer is null)
        {
            return Result.Failure(CustomerErrors.CustomerNotFound(request.CustomerId));
        }
        // Domain logic handles event creation and history tracking
        customer.AssignAdvisor(request.AdvisorId);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
