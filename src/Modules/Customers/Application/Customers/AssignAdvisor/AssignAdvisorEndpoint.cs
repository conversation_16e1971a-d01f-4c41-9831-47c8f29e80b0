using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.AssignAdvisor;

internal sealed class AssignAdvisorEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/customers/management/{id}/assign-advisor/{advisorId}", async (
            Guid id,
            Guid advisorId,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new AssignAdvisorCommand(id, advisorId);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management");
    }
}
