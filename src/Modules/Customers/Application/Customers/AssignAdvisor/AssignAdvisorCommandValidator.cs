using FluentValidation;

namespace Customers.Application.Customers.AssignAdvisor;

public class AssignAdvisorCommandValidator : AbstractValidator<AssignAdvisorCommand>
{
    public AssignAdvisorCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty()
            .WithMessage("Customer ID is required.");

        RuleFor(x => x.AdvisorId)
            .NotEmpty()
            .WithMessage("Advisor ID is required.");
    }
}
