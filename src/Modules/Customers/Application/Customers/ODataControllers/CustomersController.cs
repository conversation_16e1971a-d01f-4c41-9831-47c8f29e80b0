using Customers.Application.Abstractions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Customers.ODataControllers;

[Route("api/v1/customers/management/odata")]
[Tags("Customers.Management")]
public class CustomersController(
    ICustomersDbContext context
) : ODataController
{
    private readonly ICustomersDbContext _context = context;

    [EnableQuery(PageSize = 10)]
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(_context.Customers
            .Include(c => c.Contacts)
            .Select(c => new CustomerDTO
            {
                Id = c.Id,
                Name = c.Name,
                Surname = c.Surname,
                Email = c.Email,
                Phone = c.Phone,
                PhonePrefix = c.PhonePrefix,
                Type = c.Type,
                AdvisorIds = c.AdvisorIds,
                AttributeData = c.AttributeData,
                Contacts = c.Contacts.Select(contact => new ContactDTO
                {
                    Id = contact.Id,
                    ContactInfo = contact.ContactInfo,
                    IsDefault = contact.IsDefault,
                    Name = contact.Name,
                    Surname = contact.Surname,
                    Email = contact.Email,
                    Phone = contact.Phone,
                    PhonePrefix = contact.PhonePrefix,
                    Language = contact.Language,
                    Type = contact.Type,
                    IsNotification = contact.IsNotification,
                    IsSms = contact.IsSms,
                    IsEmail = contact.IsEmail
                }).ToList()
            }));
    }
}
