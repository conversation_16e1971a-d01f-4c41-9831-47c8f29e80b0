using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.GetTempCustomerList;

public record GetTempCustomerListQuery(
    int PageNumber = 1,
    int PageSize = 10,
    CustomerKind? Kind = null,
    CustomerStatus? Status = null,
    CustomerType? Type = null,
    string? Name=null,
    string? Surname=null,
    string? SearchTerm = null,
    string? FullName = null,
    string? TaxOffice = null,
    string? TaxNumber = null,
    string? Phone = null,
    string? PhonePrefix = null,
    string? Email = null,
    string? Country = null,
    string? State = null,
    string? City = null,
    Guid? NotificationWayId = null,
    string? IdentificationNumberOrTaxNumber = null,
    string[]? Classifications= null,
    string? CustomerSource = null,
    Guid[]? ClassificationIds = null

) : IRequest<PagedResult<TempCustomerListItemDto>>;

public record TempCustomerListItemDto(
    Guid Id,
    string? Name,
    string? Surname,
    string? Email,
    string? Phone,
    string? PhonePrefix,
    string? TaxOffice,
    string? TaxNumber,
    string? IdentificationNumber,
    string? Address,
    string? Country,
    string? State,
    string? City,
    string? Province,
    string? PostCode,
    string? MainLanguage,
    string? AvailableLanguage,
    string? Description,
    string? MailBcc,
    CustomerType? Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    string? CustomerSource,
    string[]? Classifications,
    Guid? AdvisorId,
    DateTime InsertDate
);
