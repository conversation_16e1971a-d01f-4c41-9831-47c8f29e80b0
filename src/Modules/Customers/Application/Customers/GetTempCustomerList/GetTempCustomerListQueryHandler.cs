using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.GetTempCustomerList;

internal sealed class GetTempCustomerListQueryHandler(
    ICustomersDbContext context
) : IRequestHandler<GetTempCustomerListQuery, PagedResult<TempCustomerListItemDto>>
{
    public async Task<PagedResult<TempCustomerListItemDto>> Handle(
    GetTempCustomerListQuery request,
    CancellationToken cancellationToken)
    {
        var query = context.TempCustomers
        //.Include(x => x.Addresses) 
        .Where(x => !x.IsDeleted)
        .AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(x =>
                x.Name != null && x.Name.ToLower().Contains(searchTerm) ||
                x.Surname != null && x.Surname.ToLower().Contains(searchTerm) ||
                x.Email != null && x.Email.ToLower().Contains(searchTerm) ||
                x.Phone != null && x.Phone.Contains(searchTerm));
        }

        if (request.Name != null)
        {
            query = query.Where(x => x.Name != null && x.Name.Contains(request.Name));
        }
        if (request.Surname != null)
        {
            query = query.Where(x => x.Surname != null && x.Surname.Contains(request.Surname));
        }
        if (request.TaxOffice != null)
        {
            query = query.Where(x => x.TaxOffice != null && x.TaxOffice.Contains(request.TaxOffice));
        }
        if (request.TaxNumber != null)
        {
            query = query.Where(x => x.TaxNumber != null && x.TaxNumber.Contains(request.TaxNumber));
        }
         
        if(request.Country != null)
        {
            query = query.Where(x => x.Country != null && x.Country.Contains(request.Country));
        }
        if (!string.IsNullOrWhiteSpace(request.Country))
        {
            query = query.Where(x => x.Country.Contains(request.Country));
        }
        if (!string.IsNullOrWhiteSpace(request.State))
        {
            query = query.Where(x => x.State.Contains(request.State));
        }
        if (!string.IsNullOrWhiteSpace(request.City))
        {
            query = query.Where(x => x.City.Contains(request.City));
        }
        if (request.NotificationWayId.HasValue)
        {
            query = query.Where(x => x.NotificationWayId == request.NotificationWayId.Value);
        }
        if (request.Kind.HasValue)
        {
            query = query.Where(x => x.Kind == request.Kind.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(x => x.Status == request.Status.Value);
        }

        if (request.Type.HasValue)
        {
            query = query.Where(x => x.Type == request.Type.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.FullName))
        {
            query = query.Where(x => (x.Name + " " + x.Surname).Contains(request.FullName));
        }

        if (!string.IsNullOrWhiteSpace(request.Phone))
        {
            query = query.Where(x => x.Phone != null && x.Phone.Contains(request.Phone));
        }

        if (!string.IsNullOrWhiteSpace(request.PhonePrefix))
        {
            query = query.Where(x => x.PhonePrefix != null && x.PhonePrefix.Contains(request.PhonePrefix));
        }

        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            query = query.Where(x => x.Email != null && x.Email.Contains(request.Email));
        }

        if (request.Classifications.Length > 0)
        {
            query = query.Where(x => x.TempCustomerClassifications.Any(c => request.Classifications.Contains(c.ClassificationId.ToString())));
        } 

        if (!string.IsNullOrWhiteSpace(request.CustomerSource))
        {
            query = query.Where(x => x.CustomerSource != null && x.CustomerSource.Name.Contains(request.CustomerSource));
        }

        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            query = query.Where(x => x.Email != null && x.Email.Contains(request.Email));
        }

        if (!string.IsNullOrWhiteSpace(request.IdentificationNumberOrTaxNumber))
        {
            query = query.Where(x =>
                x.IdentificationNumber != null && x.IdentificationNumber.Contains(request.IdentificationNumberOrTaxNumber) ||
                x.TaxNumber != null && x.TaxNumber.Contains(request.IdentificationNumberOrTaxNumber));
        }
        if (request.ClassificationIds?.Length > 0)
        {
            query = query.Where(x => x.TempCustomerClassifications.Any(y => request.ClassificationIds.Contains(y.ClassificationId)));
        }

        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await context.TempCustomers.Where(x => !x.IsDeleted).CountAsync(cancellationToken);

        var items = await query
            .OrderByDescending(x => x.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new TempCustomerListItemDto(
                x.Id,
                x.Name,
                x.Surname,
                x.Email,
                x.Phone,
                x.PhonePrefix,
                x.TaxOffice,
                x.TaxNumber,
                x.IdentificationNumber,
                x.Address,
                x.Country,
                x.State,
                x.City,
                x.Province,
                x.PostCode,
                x.MainLanguage,
                x.AvailableLanguage,
                x.Description,
                x.MailBcc,
                x.Type,
                x.Kind,
                x.Status,
                x.CustomerSourceId,
                x.CustomerSource.Name,
                x.TempCustomerClassifications.Select(x => x.Classification.Name).ToArray(),
                x.AdvisorId,
                x.InsertDate))
            .ToListAsync(cancellationToken);

        return new PagedResult<TempCustomerListItemDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }

}
