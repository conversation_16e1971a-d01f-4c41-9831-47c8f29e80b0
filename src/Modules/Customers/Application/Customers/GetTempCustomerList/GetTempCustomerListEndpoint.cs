using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.GetTempCustomerList;

internal sealed class GetTempCustomerListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/customers/management/temp/list", async (
            [AsParameters] GetTempCustomerListQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management");
    }
}
