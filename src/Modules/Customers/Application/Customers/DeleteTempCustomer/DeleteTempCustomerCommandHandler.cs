using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Infrastructure.Localization;

namespace Customers.Application.Customers.DeleteTempCustomer;

internal sealed class DeleteTempCustomerCommandHandler : IRequestHandler<DeleteTempCustomerCommand, Result>
{
    private readonly ICustomersDbContext _customersDbContext;
    private readonly ILocalizer _localizer;
    private readonly ILogger<DeleteTempCustomerCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public DeleteTempCustomerCommandHandler(
        ICustomersDbContext customersDbContext,
        ILocalizer localizer,
        ILogger<DeleteTempCustomerCommandHandler> logger,
        IWorkContext workContext,
        IEventBus eventBus)
    {
        _customersDbContext = customersDbContext;
        _localizer = localizer;
        _logger = logger;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result> Handle(DeleteTempCustomerCommand request, CancellationToken cancellationToken)
    {
        using var transaction = await _customersDbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var tempCustomer = await _customersDbContext.TempCustomers
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (tempCustomer is null)
            {
                _logger.LogWarning("TempCustomer not found for deletion. Id: {TempCustomerId}", request.Id);
                return Result.Failure(CustomerErrors.CustomerNotFound(request.Id));
            }

             if (tempCustomer != null)
            {
                _customersDbContext.TempCustomers.Remove(tempCustomer); 
            }

            await _customersDbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "TempCustomer",
                1,
                false,
                1,
                "",
                new[] { request.Id },
                "Delete"
            ), cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to delete TempCustomer. Id: {TempCustomerId}", request.Id);
            return Result.Failure(_localizer.Get("Customers.DeleteTempCustomer.Failed"));
        }
    }
}
