using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.TempCustomers.ExportTempCustomer;

public class ExportTempCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/management/temp/export", async (
            [FromBody] ExportTempCustomerQuery request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(request, cancellationToken);

              return result.Match(
                data => Results.File(
                    data.Value,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "TempCustomers.xlsx"
                ),
                CustomResults.Problem
            ); 
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management")
        .WithSummary("Export temp customers to Excel")
        .WithDescription("Exports all temporary customers to an Excel file.");
    }
}
