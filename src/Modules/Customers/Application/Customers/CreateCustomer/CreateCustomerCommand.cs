using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.CreateCustomer;

public record CreateCustomerCommand(
    string Name,
    string? Surname,
    string? Email,
    string Phone,
    string PhonePrefix,
    CustomerType Type,
    string TaxOffice,
    string TaxNumber,
    string IdentificationNumber,
    string Country,
    string MainLanguage,
    string AvailableLanguage,
    string Description,
    string MailBcc,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    Guid? NotificationWayId,
    List<Guid>? ClassificationIds,
    Guid? SectorId,
    Guid? ProfessionId,
    Guid[]? AdvisorIds,
    Dictionary<string, string>? AttributeData
) : IRequest<Result<Guid>>;
