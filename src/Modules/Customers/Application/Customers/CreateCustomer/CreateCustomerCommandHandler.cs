using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.CreateCustomer;

internal sealed class CreateCustomerCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<CreateCustomerCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateCustomerCommand request, CancellationToken cancellationToken)
    {
        string email = !string.IsNullOrWhiteSpace(request.Email)
    ? request.Email.Trim()
    : EmailGenerator.GenerateRandomEmail();

        var customer = Customer.Create(
            request.Name,
            request.Surname,
            email,
            request.Phone,
            request.PhonePrefix,
            request.Type);

        customer.TaxOffice = request.TaxOffice;
        customer.TaxNumber = request.TaxNumber;
        customer.IdentificationNumber = request.IdentificationNumber;
        customer.Country = request.Country;
        customer.MainLanguage = request.MainLanguage;
        customer.AvailableLanguage = request.AvailableLanguage;
        customer.Description = request.Description;
        customer.MailBcc = request.MailBcc;
        customer.Kind = request.Kind;
        customer.Status = request.Status;
        customer.CustomerSourceId = request.CustomerSourceId;
        customer.NotificationWayId = request.NotificationWayId;
        customer.ProfessionId = request.ProfessionId;
        customer.SectorId = request.SectorId;
        customer.AdvisorIds = request.AdvisorIds?.ToList();
        customer.AttributeData = request.AttributeData;
        if (request.ClassificationIds != null && request.ClassificationIds.Any())
        {
            customer.UpdateClassifications(request.ClassificationIds);
        }
        context.Customers.Add(customer);
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(customer.Id);
    }

    public static class EmailGenerator
    {
        private static readonly string[] Domains = { "generated.local" };
        private static readonly Random Random = new();

        public static string GenerateRandomEmail()
        {
            string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
            int nameLength = Random.Next(6, 12);
            var localPart = new string(Enumerable.Repeat(chars, nameLength)
                .Select(s => s[Random.Next(s.Length)]).ToArray());

            string domain = Domains[Random.Next(Domains.Length)];
            return $"{localPart}@{domain}";
        }
    }

}
