using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.ConvertToCustomer;

public record ConvertToCustomerQuery(Guid Id) : IRequest<Result<GetCustomerResponse>>;

public record GetCustomerResponse(
    Guid Id,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string? PhonePrefix,
    string TaxOffice,
    string TaxNumber,
    string IdentificationNumber,
    string Country,
    string MainLanguage,
    string AvailableLanguage,
    string Description,
    string MailBcc,
    CustomerType? Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    string CustomerSourceName,
    Guid? NotificationWayId,
    string? NotificationWayName,
    List<ClassificationDto>? Classifications,
    Guid? SectorId,
    Guid? ProfessionId,
    List<Guid>? AdvisorIds,
    Dictionary<string, string>? AttributeData,
    IReadOnlyList<ContactDto> Contacts);

public record ClassificationDto(
    Guid Id,
    string Name);

public record ContactDto(
    Guid Id,
    string Contact,
    bool IsDefault,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string? PhonePrefix,
    string Language,
    string Type,
    bool? IsNotification,
    bool? IsSms,
    bool? IsEmail);
