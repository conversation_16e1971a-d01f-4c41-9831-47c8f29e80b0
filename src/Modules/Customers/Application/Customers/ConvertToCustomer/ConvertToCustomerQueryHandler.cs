using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.ConvertToCustomer;

internal sealed class ConvertToCustomerQueryHandler(ICustomersDbContext context)
   : IRequestHandler<ConvertToCustomerQuery, Result<GetCustomerResponse>>
{
    public async Task<Result<GetCustomerResponse>> Handle(ConvertToCustomerQuery request, CancellationToken cancellationToken)
    {
        var tempCustomer = await context.TempCustomers
           .Include(x => x.TempCustomerClassifications)
               .ThenInclude(tc => tc.Classification)
           .Include(x => x.CustomerSource)
           .Include(x => x.NotificationWay)
           .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (tempCustomer == null)
        {
            return Result.Failure<GetCustomerResponse>("TempCustomer not found");
        }

        // 2. Customer oluştur
        var customer = Customer.Create(
            tempCustomer.Name,
            tempCustomer.Surname,
            tempCustomer.Email,
            tempCustomer.Phone ?? "",
            tempCustomer.PhonePrefix ?? "",
            tempCustomer.Type ?? CustomerType.Individual
        );

        customer.TaxOffice = tempCustomer.TaxOffice;
        customer.TaxNumber = tempCustomer.TaxNumber;
        customer.IdentificationNumber = tempCustomer.IdentificationNumber;
        customer.Country = tempCustomer.Country;
        customer.MainLanguage = tempCustomer.MainLanguage;
        customer.AvailableLanguage = tempCustomer.AvailableLanguage;
        customer.Description = tempCustomer.Description;
        customer.MailBcc = tempCustomer.MailBcc;
        customer.Kind = tempCustomer.Kind;
        customer.Status = tempCustomer.Status;
        customer.CustomerSourceId = tempCustomer.CustomerSourceId;
        customer.NotificationWayId = tempCustomer.NotificationWayId;
        customer.AdvisorIds = tempCustomer.AdvisorId.HasValue
            ? new List<Guid> { tempCustomer.AdvisorId.Value }
            : new List<Guid>();
        customer.SectorId = tempCustomer.SectorId;
        customer.ProfessionId = tempCustomer.ProfessionId;


        if (tempCustomer.TempCustomerClassifications != null)
        {
            foreach (var tempClassification in tempCustomer.TempCustomerClassifications)
            {
                customer.AddClassification(tempClassification.ClassificationId);
            }
        }


        await context.Customers.AddAsync(customer, cancellationToken);

        tempCustomer.IsDeleted = true;
        tempCustomer.Status = CustomerStatus.Inactive;

        await context.SaveChangesAsync(cancellationToken);


        var response = new GetCustomerResponse(
            customer.Id,
            customer.Name,
            customer.Surname ?? "",
            customer.Email ?? "",
            customer.Phone,
            customer.PhonePrefix,
            customer.TaxOffice ?? "",
            customer.TaxNumber ?? "",
            customer.IdentificationNumber ?? "",
            customer.Country ?? "",
            customer.MainLanguage ?? "",
            customer.AvailableLanguage ?? "",
            customer.Description ?? "",
            customer.MailBcc ?? "",
            customer.Type,
            customer.Kind,
            customer.Status,
            customer.CustomerSourceId,
            customer.CustomerSource?.Name ?? "",
            customer.NotificationWayId,
            customer.NotificationWay?.Name ?? "",
            tempCustomer.TempCustomerClassifications.Select(c => new ClassificationDto(
                c.Classification.Id,
                c.Classification.Name
            )).ToList(),
            customer.SectorId,
            customer.ProfessionId,
            customer.AdvisorIds,
            customer.AttributeData,
            []
        );

        return Result.Success(response);
    }
}
