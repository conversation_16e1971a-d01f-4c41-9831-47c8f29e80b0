using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.GetCustomerAdvisor;

internal sealed class GetCustomerAdvisorEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/customers/management/{phone}/advisor", async (
            string phone,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetCustomerAdvisorQuery(phone);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .AllowAnonymous();
        //.RequireAuthorization("Customers.Management");
    }
}
