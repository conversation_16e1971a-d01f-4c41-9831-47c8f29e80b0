using Customers.Application.Abstractions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace Customers.Application.Customers.UpdateTempCustomer;

public class UpdateTempCustomerCommandValidator : AbstractValidator<UpdateTempCustomerCommand>
{
    public UpdateTempCustomerCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);

        RuleFor(x => x.Surname)
            .NotEmpty()
            .MaximumLength(100);

        RuleFor(x => x.Email)
            .NotEmpty()
            .EmailAddress()
            .MaximumLength(255)
            .MustAsync(async (command, email, cancellationToken) =>
                !await context.TempCustomers.AnyAsync(
                    c => c.Email == email && c.Id != command.Id && !c.IsDeleted,
                    cancellationToken))
            .WithMessage("A customer with this email already exists.");

        RuleFor(x => x.Phone)
            .NotEmpty()
            .MaximumLength(20)
            .Matches(@"^\+?[1-9]\d{1,14}$")
            .WithMessage("Please enter a valid phone number.");
    }
}
