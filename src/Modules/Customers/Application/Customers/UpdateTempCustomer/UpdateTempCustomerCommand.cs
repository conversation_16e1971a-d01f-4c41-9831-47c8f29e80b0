using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.UpdateTempCustomer;

public record UpdateTempCustomerCommand(
    Guid Id,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string PhonePrefix,
    string TaxOffice,
    string TaxNumber,
    string IdentificationNumber,
    string Country,
    string State,
    string City,
    string Province,
    string PostCode,
    string Address,
    string MainLanguage,
    string AvailableLanguage,
    string Description,
    string MailBcc,
    CustomerType Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    Guid? ClassificationId,
    Guid? NotificationWayId,
    List<Guid>? ClassificationIds,
    Guid? ProfessionId,
    Guid? AdvisorId,
    Dictionary<string, string>? AttributeData
) : IRequest<Result>;
