using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.Validation;
using Shared.Infrastructure.Localization;

namespace Customers.Application.Customers.UpdateTempCustomer;

internal sealed class UpdateTempCustomerCommandHandler : IRequestHandler<UpdateTempCustomerCommand, Result>
{
    private readonly ICustomersDbContext _context;
    private readonly ILocalizer _localizer;

    public UpdateTempCustomerCommandHandler(ICustomersDbContext context, ILocalizer localizer)
    {
        _context = context;
        _localizer = localizer;
    }

    public async Task<Result> Handle(UpdateTempCustomerCommand request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Name) || string.IsNullOrWhiteSpace(request.Surname))
        {
            return Result.Failure(Error.Validation(
                "TempCustomer.Validation",
                _localizer.Get("TempCustomer.Validation.NameSurnameRequired")));
        }

        if (!string.IsNullOrWhiteSpace(request.Email) && !request.Email.IsValidEmail())
        {
            return Result.Failure(Error.Validation(
                "TempCustomer.Validation",
                _localizer.Get("TempCustomer.Validation.EmailInvalid", new { Email = request.Email })));
        }

        if (!string.IsNullOrWhiteSpace(request.Phone) && !request.Phone.All(char.IsDigit))
        {
            return Result.Failure(Error.Validation(
                "TempCustomer.Validation",
                _localizer.Get("TempCustomer.Validation.PhoneInvalid")));
        }

        var tempCustomer = await _context.TempCustomers
            .Include(x => x.TempCustomerClassifications)
            .FirstOrDefaultAsync(x => x.Id == request.Id && !x.IsDeleted, cancellationToken);

        if (tempCustomer is null)
        {
            return Result.Failure(CustomerErrors.CustomerNotFound(request.Id));
        }

        tempCustomer.Name = request.Name;
        tempCustomer.Surname = request.Surname;
        tempCustomer.Email = request.Email;
        tempCustomer.Phone = request.Phone;
        tempCustomer.Type = request.Type;
        tempCustomer.TaxOffice = request.TaxOffice;
        tempCustomer.TaxNumber = request.TaxNumber;
        tempCustomer.IdentificationNumber = request.IdentificationNumber;
        tempCustomer.NotificationWayId = request.NotificationWayId;
        tempCustomer.Country = request.Country;
        tempCustomer.MainLanguage = request.MainLanguage;
        tempCustomer.AvailableLanguage = request.AvailableLanguage;
        tempCustomer.Description = request.Description;
        tempCustomer.MailBcc = request.MailBcc;
        tempCustomer.Kind = request.Kind;
        tempCustomer.Status = request.Status;
        tempCustomer.CustomerSourceId = request.CustomerSourceId;
        tempCustomer.AdvisorId = request.AdvisorId;
        tempCustomer.PhonePrefix = request.PhonePrefix;
        tempCustomer.Address = request.Address;
        tempCustomer.State = request.State;
        tempCustomer.City = request.City;
        tempCustomer.Province = request.Province;
        tempCustomer.PostCode = request.PostCode;

        if (request.ClassificationIds is not null && request.ClassificationIds.Any())
        {
            tempCustomer.UpdateClassifications(request.ClassificationIds);
        }

        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
