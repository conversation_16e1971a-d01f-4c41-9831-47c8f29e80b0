using FluentValidation;

namespace Customers.Application.Customers.BulkAssignAdvisorList;

public class BulkAssignAdvisorListCommandValidator : AbstractValidator<BulkAssignAdvisorListCommand>
{
    public BulkAssignAdvisorListCommandValidator()
    {
        RuleFor(x => x.CustomerIds)
            .NotNull()
            .NotEmpty()
            .WithMessage("Customer IDs cannot be empty");

        RuleFor(x => x.AdvisorIds)
            .NotNull()
            .NotEmpty()
            .WithMessage("Advisor IDs cannot be empty");

        RuleForEach(x => x.CustomerIds)
            .NotEmpty()
            .WithMessage("Customer ID cannot be empty");

        RuleForEach(x => x.AdvisorIds)
            .NotEmpty()
            .WithMessage("Advisor ID cannot be empty");
    }
}
