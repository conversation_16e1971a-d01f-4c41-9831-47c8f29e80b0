using MediatR;
using Shared.Application;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization; 

namespace Customers.Application.Customers.ImportCustomer;

public class StartTCustomerExcelCommandHandler : IRequestHandler<StartTCustomerExcelCommand, Result<StartTCustomerExcelCommandResponse>>
{
    private readonly ExcelHelper _excelHelper;
    private readonly ILocalizer _localizer;

    public StartTCustomerExcelCommandHandler(ExcelHelper excelHelper, ILocalizer localizer)
    {
        _excelHelper = excelHelper;
        _localizer = localizer;
    }

    public async Task<Result<StartTCustomerExcelCommandResponse>> Handle(StartTCustomerExcelCommand request, CancellationToken cancellationToken)
    {
        if (request.File == null || request.File.Length == 0)
            return Result.Failure<StartTCustomerExcelCommandResponse>(_localizer.Get("FileEmpty"));

        var result = await _excelHelper.SaveTempAndGetStructureAsync(request.File, new ImportTCustomerExcelDto());
        if (!result.IsSuccess)
        {
            return Result.Failure<StartTCustomerExcelCommandResponse>(result.Error);
        }

        var response = new StartTCustomerExcelCommandResponse
        {
            TempFileName = result.Value.FilePath,
            SheetNames = result.Value.Sheets.Select(s => s.SheetName).ToList(),
            ModelFields = result.Value.ModelColumns,
            Sheets = result.Value.Sheets.Select(x => new StartSheetStructure
            {
                SheetName = x.SheetName,
                Columns = x.Columns
            }).ToList(),
            DefaultSheetName = result.Value.Sheets.FirstOrDefault()?.SheetName ?? string.Empty
        };

        return Result.Success(response);
    }
}
