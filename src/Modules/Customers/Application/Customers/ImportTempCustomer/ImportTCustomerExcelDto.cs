using System;
using Shared.Infrastructure.Excel;

namespace Customers.Application.Customers.ImportCustomer;

public class ImportTCustomerExcelDto
{
    public int RowNumber { get; set; }


    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Name")]
    public string Name { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Surname")]
    public string Surname { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Email")]
    public string Email { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Phone")]
    public string Phone { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.PhonePrefix")]
    public string PhonePrefix { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Type")]
    public string Type { get; set; } // enum string olarak gelir: "Individual", "Corporate"

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Status")]
    public string? Status { get; set; } // enum: "Active", "Inactive", "Suspended"

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Kind")]
    public string? Kind { get; set; } // enum: "Customer", "PotentialCustomer", "Renew"

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Address")]
    public string? Address { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Country")]
    public string? Country { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.State")]
    public string? State { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.City")]
    public string? City { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Province")]
    public string? Province { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.PostCode")]
    public string? PostCode { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Language")]
    public string? MainLanguage { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.AvailableLanguage")]
    public string? AvailableLanguage { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Description")]
    public string? Description { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.TaxOffice")]
    public string? TaxOffice { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.TaxNumber")]
    public string? TaxNumber { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.IdentificationNumber")]
    public string? IdentificationNumber { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.MailBcc")]
    public string? MailBcc { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.Classification")]
    public string[]? ClassificationName { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.CustomerSource")]
    public string? CustomerSourceName { get; set; }

    [ExcelImportable(localizerKey: "ExcelCustomerColumns.NotificationWay")]
    public string? NotificationWay { get; set; }

}
public class ExportTCustomerExcelDto
{

    public int RowNumber { get; set; }


    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Name")]
    public string Name { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Surname")]
    public string Surname { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Email")]
    public string Email { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Phone")]
    public string Phone { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.PhonePrefix")]
    public string PhonePrefix { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Type")]
    public string Type { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Status")]
    public string? Status { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Kind")]
    public string? Kind { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Address")]
    public string? Address { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Country")]
    public string? Country { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.State")]
    public string? State { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.City")]
    public string? City { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Province")]
    public string? Province { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.PostCode")]
    public string? PostCode { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Language")]
    public string? MainLanguage { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.AvailableLanguage")]
    public string? AvailableLanguage { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Description")]
    public string? Description { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.TaxOffice")]
    public string? TaxOffice { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.TaxNumber")]
    public string? TaxNumber { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.IdentificationNumber")]
    public string? IdentificationNumber { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.MailBcc")]
    public string? MailBcc { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.Classification")]
    public string? ClassificationName { get; set; }

    [ExcelExportable(localizerKey: "ExcelCustomerColumns.CustomerSource")]
    public string? CustomerSourceName { get; set; }
    
    [ExcelExportable(localizerKey: "ExcelCustomerColumns.NotificationWay")]
    public string? NotificationWay { get; set; }
}
