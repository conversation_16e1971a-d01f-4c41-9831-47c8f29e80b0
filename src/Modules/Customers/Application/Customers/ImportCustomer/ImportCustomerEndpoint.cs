using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Customers.ImportCustomer;

internal sealed class ImportCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/management/importCustomer/import",
        async (ImportCustomerCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);
            return result.Match(
                Results.Ok,
                CustomResults.Problem
            );
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management")
        .WithSummary("Import eşlemesi ile müşteri verilerini içeri aktarır.")
        .WithDescription("Excel sheet içeriği eşleşmeler doğrultusunda doğrulanır ve veritabanına yazılır.");
    }
}
