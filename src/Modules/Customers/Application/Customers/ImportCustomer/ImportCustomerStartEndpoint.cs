using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Customers.Application.Customers.ImportCustomer;

internal sealed class ImportCustomerStartEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/management/importCustomer/start",
        async (IFormFile file, ISender sender, CancellationToken cancellationToken) =>
        {
            var command = new StartCustomerExcelCommand(file);
            var result = await sender.Send(command, cancellationToken);
            return result.Match(
                Results.Ok,
                CustomResults.Problem
            );
        })
        .DisableAntiforgery()
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management")
        .WithSummary("Müşteri verisi içe aktarma işlemini başlatır.")
        .WithDescription("Excel dosyasındaki müşteri verileri ile eşleştirmek için model sağlar.");
    }
}
