using MediatR;
using Shared.Application;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization; 

namespace Customers.Application.Customers.ImportCustomer;

public class StartCustomerExcelCommandHandler : IRequestHandler<StartCustomerExcelCommand, Result<StartCustomerExcelCommandResponse>>
{
    private readonly ExcelHelper _excelHelper;
    private readonly ILocalizer _localizer;

    public StartCustomerExcelCommandHandler(ExcelHelper excelHelper, ILocalizer localizer)
    {
        _excelHelper = excelHelper;
        _localizer = localizer;
    }

    public async Task<Result<StartCustomerExcelCommandResponse>> Handle(StartCustomerExcelCommand request, CancellationToken cancellationToken)
    {
        if (request.File == null || request.File.Length == 0)
            return Result.Failure<StartCustomerExcelCommandResponse>(_localizer.Get("FileEmpty"));

        var result = await _excelHelper.SaveTempAndGetStructureAsync(request.File, new ImportCustomerExcelDto());
        if (!result.IsSuccess)
        {
            return Result.Failure<StartCustomerExcelCommandResponse>(result.Error);
        }

        var response = new StartCustomerExcelCommandResponse
        {
            TempFileName = result.Value.FilePath,
            SheetNames = result.Value.Sheets.Select(s => s.SheetName).ToList(),
            ModelFields = result.Value.ModelColumns,
            Sheets = result.Value.Sheets.Select(x => new StartSheetStructure
            {
                SheetName = x.SheetName,
                Columns = x.Columns
            }).ToList(),
            DefaultSheetName = result.Value.Sheets.FirstOrDefault()?.SheetName ?? string.Empty
        };

        return Result.Success(response);
    }
}
