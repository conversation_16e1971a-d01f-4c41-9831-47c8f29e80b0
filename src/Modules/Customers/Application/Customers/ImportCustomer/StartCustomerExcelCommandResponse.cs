using MediatR;
using Microsoft.AspNetCore.Http;
using Shared.Application;
using Shared.Infrastructure.Excel;


namespace Customers.Application.Customers.ImportCustomer;

public class StartCustomerExcelCommandResponse
{
    public string TempFileName { get; set; } = string.Empty;
    public List<string> SheetNames { get; set; } = new();
    public List<string> ModelFields { get; set; } = new();
    public List<StartSheetStructure> Sheets { get; set; } = new();
    public string DefaultSheetName { get; set; } = string.Empty;

}


