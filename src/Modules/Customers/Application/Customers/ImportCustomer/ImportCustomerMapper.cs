using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Shared.Infrastructure.Excel;
using Customers.Application.Customers.ImportCustomer;
using Microsoft.Extensions.Localization;
using Shared.Infrastructure.Localization;
using Customers.Domain;

namespace Customers.Application.Customers.ImportCustomer;

public static class ImportCustomerExcelMapper
{
    private static ILocalizer _localizer;

    public static List<ImportCustomerExcelDto> Map(List<Dictionary<string, object>> excelData, List<ColumnMapping> mappings, ILocalizer localizer)
    {
        var result = new List<ImportCustomerExcelDto>();
        _localizer = localizer;

        var propertyMap = typeof(ImportCustomerExcelDto).GetProperties()
            .Where(p => Attribute.IsDefined(p, typeof(ExcelImportableAttribute)))
            .Select(p => new
            {
                Property = p,
                Localized = localizer.Get(p.GetCustomAttribute<ExcelImportableAttribute>()?.LocalizerKey ?? p.Name),
                ActualName = p.Name
            })
            .ToDictionary(x => x.Localized, x => x.ActualName);

        int rowIndex = 2;
        foreach (var row in excelData)
        {
            var dto = new ImportCustomerExcelDto
            {
                RowNumber = rowIndex++
            };

            foreach (var mapping in mappings)
            {
                if (row.TryGetValue(mapping.ExcelColumnName, out var value))
                {
                    if (propertyMap.TryGetValue(mapping.ModelPropertyName, out var actualPropertyName))
                    {
                        var prop = typeof(ImportCustomerExcelDto).GetProperty(actualPropertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (prop != null && prop.CanWrite)
                        {
                            try
                            {
                                
                                if (prop.PropertyType == typeof(bool?) && value is string strVal)
                                {
                                    strVal = strVal.Trim();
                                    if (strVal == "1")
                                        value = true;
                                    else if (strVal == "0")
                                        value = false;
                                    else if (bool.TryParse(strVal, out var parsedBool))
                                        value = parsedBool;
                                    else
                                        value = null;
                                }

                                if (prop.PropertyType == typeof(CustomerType))
                                {
                                    if (value != null)
                                    {
                                        var enumValue = Enum.TryParse<CustomerType>(value.ToString(), true, out var parsedEnum);
                                        if (enumValue)
                                        {
                                            value = parsedEnum;
                                        }
                                        else
                                        {
                                            value = null;  
                                        }
                                    }
                                }
                                else if (prop.PropertyType == typeof(CustomerKind))
                                {
                                    if (value != null)
                                    {
                                        var enumValue = Enum.TryParse<CustomerKind>(value.ToString(), true, out var parsedEnum);
                                        if (enumValue)
                                        {
                                            value = parsedEnum;
                                        }
                                        else
                                        {
                                            value = null;  
                                        }
                                    }
                                }
                                else if (prop.PropertyType == typeof(CustomerStatus))
                                {
                                    if (value != null)
                                    {
                                        var enumValue = Enum.TryParse<CustomerStatus>(value.ToString(), true, out var parsedEnum);
                                        if (enumValue)
                                        {
                                            value = parsedEnum;
                                        }
                                        else
                                        {
                                            value = null;  
                                        }
                                    }
                                }
 
                                 prop.SetValue(dto, Convert.ChangeType(value, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType));
                            }
                            catch (Exception ex)
                            {
                                throw new FormatException(
                                    _localizer.Get("ExcelMappingError", new
                                    {
                                        Row = dto.RowNumber,
                                        Column = mapping.ExcelColumnName,
                                        Value = value?.ToString(),
                                        Property = prop.Name
                                    }),
                                    ex
                                );
                            }
                        }
                    }
                }
            }

            result.Add(dto);
        }

        return result;
    }
}
