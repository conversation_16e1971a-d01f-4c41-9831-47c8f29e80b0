using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.BulkDeleteTempCustomer;

internal sealed class BulkDeleteTempCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("/api/v1/customers/management/temp/bulk-delete", async (
            Guid[] ids,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new BulkDeleteTempCustomerCommand(ids);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management");
    }
}
