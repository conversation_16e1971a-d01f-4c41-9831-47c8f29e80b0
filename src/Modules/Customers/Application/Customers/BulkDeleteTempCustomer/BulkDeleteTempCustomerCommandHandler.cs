using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Localization;
using Shared.Application.EventDbLogger;

namespace Customers.Application.Customers.BulkDeleteTempCustomer;

internal sealed class BulkDeleteTempCustomerCommandHandler : IRequestHandler<BulkDeleteTempCustomerCommand, Result>
{
    private readonly ICustomersDbContext _customersDbContext;
    private readonly ILocalizer _localizer;
    private readonly ILogger<BulkDeleteTempCustomerCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public BulkDeleteTempCustomerCommandHandler(
        ICustomersDbContext customersDbContext,
        ILocalizer localizer,
        ILogger<BulkDeleteTempCustomerCommandHandler> logger,
        IWorkContext workContext,
        IEventBus eventBus)
    {
        _customersDbContext = customersDbContext;
        _localizer = localizer;
        _logger = logger;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result> Handle(BulkDeleteTempCustomerCommand request, CancellationToken cancellationToken)
    {
        if (request.Ids == null || !request.Ids.Any())
        {
            return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.NoIdsProvided"));
        }

        using var transaction = await _customersDbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            int totalDeleted = 0;
            var tempCustomers = await _customersDbContext.TempCustomers
                .Where(x => request.Ids.Contains(x.Id))
                .ToListAsync(cancellationToken);

            var foundIds = tempCustomers.Select(x => x.Id).ToHashSet();
            var missingIds = request.Ids.Where(id => !foundIds.Contains(id)).ToList();

            if (missingIds.Any())
            {
                _logger.LogWarning("Bulk delete failed. Missing TempCustomer IDs: {MissingIds}", string.Join(", ", missingIds));
                return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.SomeIdsNotFound"));
            }

            foreach (var customer in tempCustomers)
            {
                _customersDbContext.TempCustomers.Remove(customer);
                totalDeleted++;
            }

            await _customersDbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "TempCustomer",
                request.Ids.Count(),
                false,
                totalDeleted,
                 "",
                request.Ids,
                "Delete"
            ), cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Bulk delete of TempCustomers failed.");
            return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.Failed"));
        }
    }
}
