using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Infrastructure.Localization;

namespace Customers.Application.Customers.DeleteCustomer;

internal sealed class DeleteCustomerCommandHandler : IRequestHandler<DeleteCustomerCommand, Result>
{
    private readonly ICustomersDbContext _customersDbContext;
    private readonly ILocalizer _localizer;
    private readonly ILogger<DeleteCustomerCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public DeleteCustomerCommandHandler(
        ICustomersDbContext customersDbContext,
        ILocalizer localizer,
        ILogger<DeleteCustomerCommandHandler> logger,
        IWorkContext workContext,
        IEventBus eventBus)
    {
        _customersDbContext = customersDbContext;
        _localizer = localizer;
        _logger = logger;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result> Handle(DeleteCustomerCommand request, CancellationToken cancellationToken)
    {
        using var transaction = await _customersDbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var customer = await _customersDbContext.Customers
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (customer is null)
            {
                _logger.LogWarning("Customer not found for deletion. Id: {CustomerId}", request.Id);
                return Result.Failure(CustomerErrors.CustomerNotFound(request.Id));
            }

            customer.Delete(); // Soft delete assumed
            await _customersDbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "Customer",
                1,
                false,
                1,
                "",
                new[] { request.Id },
                "Delete"
            ), cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to delete customer. Id: {CustomerId}", request.Id);
            return Result.Failure(_localizer.Get("Customers.DeleteCustomer.Failed"));
        }
    }
}
