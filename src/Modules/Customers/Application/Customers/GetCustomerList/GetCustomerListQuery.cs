using Customers.Domain;
using MediatR;
using Shared.Application;

namespace Customers.Application.Customers.GetCustomerList;

public record GetCustomerListQuery(
    int PageNumber = 1,
    int PageSize = 10,
    CustomerKind? Kind = null,
    CustomerStatus? Status = null,
    CustomerType? Type = null,
    Guid? SectorId = null,
    Guid? ProfessionId = null,
    Guid? CustomerSourceId = null,
    Guid? NotificationWayId = null,
    Guid? TopCustomerId = null,
    Guid? AdvisorId = null,
    Guid[]? ClassificationIds = null,
    string? SearchTerm = null,
    string? Name = null,
    string? Surname = null,
    string? Phone = null,
    string? Email = null,
    string? Country = null,
    string? State = null,
    string? City = null,
    string? IdentificationNumberOrTaxNumber = null
) : IRequest<PagedResult<CustomerListItemDto>>;

public record CustomerListItemDto(
    Guid Id,
    string Name,
    string? Surname,
    string? Email,
    string Phone,
    string? PhonePrefix,
    string? TaxOffice,
    string? TaxNumber,
    string? IdentificationNumber,
    string? Country,
    string? MainLanguage,
    string? AvailableLanguage,
    string? Description,
    string? MailBcc,
    CustomerType Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    string? CustomerSource,
    Guid? SectorId,
    string? Sector,
    Guid? ProfessionId,
    string? Profession,
    string[] Classifications,
    List<Guid>? AdvisorIds,
    DateTime InsertDate
);
