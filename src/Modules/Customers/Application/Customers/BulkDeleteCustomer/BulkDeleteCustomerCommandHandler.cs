using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Infrastructure.Localization;

namespace Customers.Application.Customers.BulkDeleteCustomer;

public class BulkDeleteCustomerCommandHandler : IRequestHandler<BulkDeleteCustomerCommand, Result>
{
    private readonly ICustomersDbContext _customersDbContext;
    private readonly ILocalizer _localizer;
    private readonly ILogger<BulkDeleteCustomerCommandHandler> _logger;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;

    public BulkDeleteCustomerCommandHandler(
        ICustomersDbContext customersDbContext,
        ILocalizer localizer,
        ILogger<BulkDeleteCustomerCommandHandler> logger,
        IWorkContext workContext,
        IEventBus eventBus)
    {
        _customersDbContext = customersDbContext;
        _localizer = localizer;
        _logger = logger;
        _workContext = workContext;
        _eventBus = eventBus;
    }

    public async Task<Result> Handle(BulkDeleteCustomerCommand request, CancellationToken cancellationToken)
    {
        if (request.Ids == null || !request.Ids.Any())
        {
            return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.NoIdsProvided"));
        }

        using var transaction = await _customersDbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            int totalDeleted = 0;
            var customers = await _customersDbContext.Customers
                .Where(x => request.Ids.Contains(x.Id))
                .ToListAsync(cancellationToken);

            var foundIds = customers.Select(c => c.Id).ToHashSet();
            var missingIds = request.Ids.Where(id => !foundIds.Contains(id)).ToList();

            if (missingIds.Any())
            {
                _logger.LogWarning("Bulk delete failed. Missing Customer IDs: {MissingIds}", string.Join(", ", missingIds));
                return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.SomeIdsNotFound"));
            }

            foreach (var customer in customers)
            {
                customer.Delete();  
                totalDeleted++;
            }

            await _customersDbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "Customer",
                request.Ids.Count(),
                false,
                totalDeleted,
                 "",
                request.Ids,
                "Delete"
            ), cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Bulk delete of Customers failed.");
            return Result.Failure(_localizer.Get("Customers.BulkDeleteCustomer.Failed"));
        }
    }
}
