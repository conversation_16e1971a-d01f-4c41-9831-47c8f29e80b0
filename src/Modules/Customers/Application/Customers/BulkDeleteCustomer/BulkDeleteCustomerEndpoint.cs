using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.BulkDeleteCustomer;

internal sealed class BulkDeleteCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("/api/v1/customers/management/bulk-delete", async (
            Guid[] ids,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new BulkDeleteCustomerCommand(ids);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management");
    }
}
