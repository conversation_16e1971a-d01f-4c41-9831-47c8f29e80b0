using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Customers.Application.Customers.ExportCustomer;

public class ExportCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/customers/management/export", async (
            [FromBody] ExportCustomerQuery request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(request, cancellationToken);

            return result.Match(
                data => Results.File(
                    data.Value,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "Customers.xlsx"
                ),
                CustomResults.Problem
            );
        })
        .WithTags("Customers.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Customers.Management")
        .WithSummary("Export customers to Excel")
        .WithDescription("Exports filtered or selected customers into an Excel file.");
    }

}
