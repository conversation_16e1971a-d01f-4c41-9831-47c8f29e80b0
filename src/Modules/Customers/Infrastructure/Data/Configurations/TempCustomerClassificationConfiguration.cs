using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Customers.Infrastructure.Data.Configurations;

public class TempCustomerClassificationConfiguration : IEntityTypeConfiguration<TempCustomerClassification>
{
    public void Configure(EntityTypeBuilder<TempCustomerClassification> builder)
    {
        builder.HasKey(x => new { x.TempCustomerId, x.ClassificationId });
    }
}
