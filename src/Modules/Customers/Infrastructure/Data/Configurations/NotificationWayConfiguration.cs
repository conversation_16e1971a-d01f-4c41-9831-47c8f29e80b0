using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Customers.Infrastructure.Data.Configurations;

public class NotificationWayConfiguration : IEntityTypeConfiguration<NotificationWay>
{
    public void Configure(EntityTypeBuilder<NotificationWay> builder)
    {
        builder.ToTable("NotificationWay", "Customers");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id);

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.Name)
            .IsUnique()
            .HasDatabaseName("UQ_NotificationWay_Name");
    }
}
