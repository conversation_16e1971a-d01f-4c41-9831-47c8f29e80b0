using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Customers.Infrastructure.Data.Configurations;

public class CustomerClassificationConfiguration : IEntityTypeConfiguration<CustomerClassification>
{
    public void Configure(EntityTypeBuilder<CustomerClassification> builder)
    {
        builder.HasKey(x => new { x.CustomerId, x.ClassificationId });
    }
}
