﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData007 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TempCustomers_CustomerSource_CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.AlterColumn<int>(
                name: "Type",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "TaxOffice",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "TaxNumber",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "Phone",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "MainLanguage",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "MailBcc",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<int>(
                name: "Kind",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "IdentificationNumber",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<string>(
                name: "Country",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "AvailableLanguage",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AddColumn<Guid>(
                name: "ProfessionId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "SectorId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TopCustomerId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CustomerClassification",
                schema: "Customers",
                columns: table => new
                {
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ClassificationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerClassification", x => new { x.CustomerId, x.ClassificationId });
                    table.ForeignKey(
                        name: "FK_CustomerClassification_Classification_ClassificationId",
                        column: x => x.ClassificationId,
                        principalSchema: "Customers",
                        principalTable: "Classification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerClassification_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "Customers",
                        principalTable: "Customer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Profession",
                schema: "Customers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Profession", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Sector",
                schema: "Customers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sector", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Customer_ProfessionId",
                schema: "Customers",
                table: "Customer",
                column: "ProfessionId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_SectorId",
                schema: "Customers",
                table: "Customer",
                column: "SectorId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_TopCustomerId",
                schema: "Customers",
                table: "Customer",
                column: "TopCustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerClassification_ClassificationId",
                schema: "Customers",
                table: "CustomerClassification",
                column: "ClassificationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Customer_TopCustomerId",
                schema: "Customers",
                table: "Customer",
                column: "TopCustomerId",
                principalSchema: "Customers",
                principalTable: "Customer",
                principalColumn: "Id",
                onDelete: ReferentialAction.NoAction);

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Profession_ProfessionId",
                schema: "Customers",
                table: "Customer",
                column: "ProfessionId",
                principalSchema: "Customers",
                principalTable: "Profession",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Sector_SectorId",
                schema: "Customers",
                table: "Customer",
                column: "SectorId",
                principalSchema: "Customers",
                principalTable: "Sector",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TempCustomers_CustomerSource_CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers",
                column: "CustomerSourceId",
                principalSchema: "Customers",
                principalTable: "CustomerSource",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Customer_TopCustomerId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Profession_ProfessionId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Sector_SectorId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_TempCustomers_CustomerSource_CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropTable(
                name: "CustomerClassification",
                schema: "Customers");

            migrationBuilder.DropTable(
                name: "Profession",
                schema: "Customers");

            migrationBuilder.DropTable(
                name: "Sector",
                schema: "Customers");

            migrationBuilder.DropIndex(
                name: "IX_Customer_ProfessionId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_Customer_SectorId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_Customer_TopCustomerId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "ProfessionId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "SectorId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "TopCustomerId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.AlterColumn<int>(
                name: "Type",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TaxOffice",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TaxNumber",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Phone",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MainLanguage",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MailBcc",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Kind",
                schema: "Customers",
                table: "TempCustomers",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IdentificationNumber",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Country",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AvailableLanguage",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_TempCustomers_CustomerSource_CustomerSourceId",
                schema: "Customers",
                table: "TempCustomers",
                column: "CustomerSourceId",
                principalSchema: "Customers",
                principalTable: "CustomerSource",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
