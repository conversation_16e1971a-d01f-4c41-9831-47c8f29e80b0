﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData015 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdvisorId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.AddColumn<string>(
                name: "AdvisorIds",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdvisorIds",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.AddColumn<Guid>(
                name: "AdvisorId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);
        }
    }
}
