﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData012 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "NotificationWayId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "NotificationWay",
                schema: "Customers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationWay", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Customer_NotificationWayId",
                schema: "Customers",
                table: "Customer",
                column: "NotificationWayId");

            migrationBuilder.CreateIndex(
                name: "UQ_NotificationWay_Name",
                schema: "Customers",
                table: "NotificationWay",
                column: "Name",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_NotificationWay_NotificationWayId",
                schema: "Customers",
                table: "Customer",
                column: "NotificationWayId",
                principalSchema: "Customers",
                principalTable: "NotificationWay",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customer_NotificationWay_NotificationWayId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropTable(
                name: "NotificationWay",
                schema: "Customers");

            migrationBuilder.DropIndex(
                name: "IX_Customer_NotificationWayId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "NotificationWayId",
                schema: "Customers",
                table: "Customer");
        }
    }
}
