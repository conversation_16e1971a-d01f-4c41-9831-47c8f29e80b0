﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData008 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Classification_ClassificationId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_TempCustomers_Classification_ClassificationId",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropIndex(
                name: "IX_TempCustomers_ClassificationId",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropIndex(
                name: "IX_Customer_ClassificationId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "ClassificationId",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropColumn(
                name: "ClassificationId",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.AlterColumn<string>(
                name: "TaxOffice",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "TaxNumber",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "MainLanguage",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "MailBcc",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "IdentificationNumber",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "Country",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AlterColumn<string>(
                name: "AvailableLanguage",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024);

            migrationBuilder.AddColumn<string>(
                name: "PhonePrefix",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "TempCustomerClassification",
                schema: "Customers",
                columns: table => new
                {
                    TempCustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ClassificationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TempCustomerClassification", x => new { x.TempCustomerId, x.ClassificationId });
                    table.ForeignKey(
                        name: "FK_TempCustomerClassification_Classification_ClassificationId",
                        column: x => x.ClassificationId,
                        principalSchema: "Customers",
                        principalTable: "Classification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TempCustomerClassification_TempCustomers_TempCustomerId",
                        column: x => x.TempCustomerId,
                        principalSchema: "Customers",
                        principalTable: "TempCustomers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TempCustomerClassification_ClassificationId",
                schema: "Customers",
                table: "TempCustomerClassification",
                column: "ClassificationId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TempCustomerClassification",
                schema: "Customers");

            migrationBuilder.DropColumn(
                name: "PhonePrefix",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.AddColumn<Guid>(
                name: "ClassificationId",
                schema: "Customers",
                table: "TempCustomers",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TaxOffice",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TaxNumber",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MainLanguage",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "MailBcc",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IdentificationNumber",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Country",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AvailableLanguage",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ClassificationId",
                schema: "Customers",
                table: "Customer",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TempCustomers_ClassificationId",
                schema: "Customers",
                table: "TempCustomers",
                column: "ClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_ClassificationId",
                schema: "Customers",
                table: "Customer",
                column: "ClassificationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Classification_ClassificationId",
                schema: "Customers",
                table: "Customer",
                column: "ClassificationId",
                principalSchema: "Customers",
                principalTable: "Classification",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TempCustomers_Classification_ClassificationId",
                schema: "Customers",
                table: "TempCustomers",
                column: "ClassificationId",
                principalSchema: "Customers",
                principalTable: "Classification",
                principalColumn: "Id");
        }
    }
}
