﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData016 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PostCode",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Province",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "State",
                schema: "Customers",
                table: "TempCustomers",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Address",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropColumn(
                name: "City",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropColumn(
                name: "PostCode",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropColumn(
                name: "Province",
                schema: "Customers",
                table: "TempCustomers");

            migrationBuilder.DropColumn(
                name: "State",
                schema: "Customers",
                table: "TempCustomers");
        }
    }
}
