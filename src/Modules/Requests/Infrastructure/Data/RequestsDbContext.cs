using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;

namespace Requests.Infrastructure.Data;

public class RequestsDbContext(
    DbContextOptions<RequestsDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), IRequestsDbContext
{
    public DbSet<Ticket> Tickets { get; set; }
    public DbSet<TicketSubject> TicketSubjects { get; set; }
    public DbSet<TicketComment> TicketComments { get; set; }
    public DbSet<TicketFile> TicketFiles { get; set; }
    public DbSet<TicketDepartment> TicketDepartments { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("Requests");
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
