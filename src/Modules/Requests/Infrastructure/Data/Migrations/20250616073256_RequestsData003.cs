﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class RequestsData003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DepartmentIds",
                schema: "Requests",
                table: "Tickets");

            migrationBuilder.CreateTable(
                name: "TicketDepartments",
                schema: "Requests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TicketId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DepartmentName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TicketDepartments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TicketDepartments_Tickets_TicketId",
                        column: x => x.TicketId,
                        principalSchema: "Requests",
                        principalTable: "Tickets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TicketDepartments_TicketId",
                schema: "Requests",
                table: "TicketDepartments",
                column: "TicketId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TicketDepartments",
                schema: "Requests");

            migrationBuilder.AddColumn<string>(
                name: "DepartmentIds",
                schema: "Requests",
                table: "Tickets",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
