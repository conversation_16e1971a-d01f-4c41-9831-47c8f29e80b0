using System.Text;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Shared.Domain;
using Shared.Endpoints;

namespace Conversations.Application.Channels.WhatsApp;

internal sealed class WhatsAppWebhookVerifyEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/webhooks/whatsapp", (
            [FromQuery(Name = "hub.mode")] string mode,
            [FromQuery(Name = "hub.verify_token")] string token,
            [FromQuery(Name = "hub.challenge")] string challenge,
            AppSettings settings,
            ILogger<WhatsAppWebhookEndpoint> logger)
            =>
        {
            if (mode == "subscribe" && token == settings.WhatsAppWebhookSecret)
            {
                return Results.Text(challenge);
            }
            else
            {
                return Results.StatusCode(400);
            }
        })
        .WithTags("Conversations.Chats")
        .WithGroupName("apiv1")
        .AllowAnonymous();
    }

}
