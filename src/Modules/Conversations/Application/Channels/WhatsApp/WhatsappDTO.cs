using System.Text.Json.Serialization;

namespace Conversations.Application.Channels.WhatsApp;

public record WhatsAppDTO
{
    public string Object { get; set; }
    public WhatsappEntryDTO[] Entry { get; set; }
}

public record WhatsappEntryDTO
{
    public string Id { get; set; }
    public WhatsappChangeDTO[] Changes { get; set; }
}

public record WhatsappChangeDTO
{
    public string Field { get; set; }
    public WhatsappValueDTO Value { get; set; }
}

public record WhatsappValueDTO
{
    [JsonPropertyName("messaging_product")]
    public string MessagingProduct { get; set; }
    public WhatsappMetadataDTO Metadata { get; set; }
    public WhatsappContactDTO[] Contacts { get; set; }
    public WhatsappMessageDTO[] Messages { get; set; }
}

public record WhatsappMessageDTO
{
    public string From { get; set; }
    public string To { get; set; }
    public string Id { get; set; }
    public string Timestamp { get; set; }
    public string Type { get; set; }
    public WhatsappTextDTO Text { get; set; }
}

public record WhatsappTextDTO
{
    public string Body { get; set; }
}

public record WhatsappContactDTO
{
    [JsonPropertyName("wa_id")]
    public string WaId { get; set; }
    public WhatsappProfileDTO Profile { get; set; }
}

public record WhatsappProfileDTO
{
    public string Name { get; set; }
}

public record WhatsappMetadataDTO
{
    [JsonPropertyName("display_phone_number")]
    public string DisplayPhoneNumber { get; set; }
    [JsonPropertyName("phone_number_id")]
    public string PhoneNumberId { get; set; }
}
