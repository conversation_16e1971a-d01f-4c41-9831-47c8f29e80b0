using Conversations.Domain.Calls;
using Shared.Application;

namespace Conversations.Application.Abstractions;

public interface IThreeCXService
{
    Task<CallInfo> InitiateCallAsync(string phoneNumber, Guid agentId);
    Task<bool> EndCallAsync(string callId);
    Task<string> GetRecordingUrlAsync(string callId);
    Task<CallStatus> GetCallStatusAsync(string callId);
    Task<string> GetTranscriptionAsync(string callId);
    Task<CallStatus> CheckCallStatus(string callId);
    Task<Result> StartAutoDialer(Guid autoDialerId, string queueNumber, List<string> targetNumbers);
    Task<Result> CancelAutoDialer(Guid autoDialerId);
    Task<Result<List<QueueDto>>> GetAllQueues();
    Task<Result<List<CallHistoryDTO>?>> GetCallHistory(string historyOfTheCall);
    Task<Result<List<CallReportDTO>?>> GetCallReport(
        string[]? extensionNumbers,
        DateTime startDate,
        DateTime endDate,
        string? direction,
        bool? isAnswered,
        string? caller,
        string? callee,
        int? pageNumber,
        int? pageSize);
}

public class CallReportDTO
{
    public string? HistoryOfTheCall { get; set; }
    public string? Caller { get; set; }
    public string? Callee { get; set; }
    public string? Direction { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? AnsweredTime { get; set; }
    public DateTime? EndTime { get; set; }
    public int? TalkDurationInSeconds { get; set; }
    public int? TotalDurationInSeconds { get; set; }
    public bool? IsAnswered { get; set; }
    public string? Extension { get; set; }
    public string? RecordingUrl { get; set; }
    public string? Transcription { get; set; }
}

public record CallHistoryDTO
{
    public string? CallHistoryId { get; set; }
    public string? SourceEntityType { get; set; }
    public string? SourceParticipantPhoneNumber { get; set; }
    public string? SourceParticipantName { get; set; }
    public string? SourceParticipantTrunkDid { get; set; }
    public string? CreationMethod { get; set; }
    public string? RecordingUrl { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? Transcription { get; set; }
    public string? Summary { get; set; }
}

public record CallInfo(string CallId, string RecordingUrl);

public record QueueDto(string QueueNumber, string QueueName, List<string> Agents);
