using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.CompleteAutoDialer;

public class CompleteAutoDialerCommandHandler(
    IConversationDbContext dbContext
) : IRequestHandler<CompleteAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<bool>> Handle(CompleteAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        try
        {
            entity.Complete();
            await _dbContext.SaveChangesAsync(cancellationToken);
            return Result.Success(true);
        }
        catch (InvalidOperationException ex)
        {
            return Result.Failure<bool>("500", ex.Message);
        }
        catch (Exception ex)
        {
            return Result.Failure<bool>("500", $"Error completing auto dialer: {ex.Message}");
        }
    }
}
