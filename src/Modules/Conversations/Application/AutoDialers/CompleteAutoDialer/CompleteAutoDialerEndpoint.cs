using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.AutoDialers.CompleteAutoDialer;

internal sealed class CompleteAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/autodialers/{id}/complete", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new CompleteAutoDialerCommand(id);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .AllowAnonymous();
        //.RequireAuthorization("Conversations.Autodialers");
    }
}
