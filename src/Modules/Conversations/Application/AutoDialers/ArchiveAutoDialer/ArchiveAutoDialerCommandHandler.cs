using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.ArchiveAutoDialer;

public class ArchiveAutoDialerCommandHandler(
    IConversationDbContext dbContext
) : IRequestHandler<ArchiveAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<bool>> Handle(ArchiveAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        entity.Archive();
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(true);
    }
}
