using Conversations.Application.Abstractions;
using MediatR;
using Shared.Application;

namespace Conversations.Application.AutoDialers.ListQueues;

public class ListQueuesQueryHandler(
    IThreeCXService threeCXService
) : IRequestHandler<ListQueuesQuery, Result<List<QueueDto>>>
{
    private readonly IThreeCXService _threeCXService = threeCXService;


    public async Task<Result<List<QueueDto>>> Handle(ListQueuesQuery request, CancellationToken cancellationToken)
    {
        var queues = await _threeCXService.GetAllQueues();
        return queues.Value.Select(x => new QueueDto(x.QueueNumber, x.QueueName, x.Agents)).ToList();
    }

}
