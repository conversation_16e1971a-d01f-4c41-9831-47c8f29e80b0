using Conversations.Application.Abstractions;
using Conversations.Domain.AutoDialers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.DeleteAutoDialer;

public class DeleteAutoDialerCommandHandler(
    IConversationDbContext dbContext
) : IRequestHandler<DeleteAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<bool>> Handle(DeleteAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        if (entity.Status == AutoDialerStatus.InProgress ||
            entity.Status == AutoDialerStatus.Completed ||
            entity.Status == AutoDialerStatus.Cancelled ||
            _dbContext.Call.Any(x => x.AutoDialerId == request.Id))
        {
            return Result.Failure<bool>("400", "Sadece işlem görmemiş olan otomatik arama kayıtları silinebilir.");
        }
        _dbContext.AutoDialers.Remove(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(true);
    }
}
