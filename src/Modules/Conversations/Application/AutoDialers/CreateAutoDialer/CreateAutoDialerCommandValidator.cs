using FluentValidation;

namespace Conversations.Application.AutoDialers.CreateAutoDialer;

public class CreateAutoDialerCommandValidator : AbstractValidator<CreateAutoDialerCommand>
{
    public CreateAutoDialerCommandValidator()
    {
        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name must not exceed 100 characters.");

        RuleFor(v => v.QueueNumber)
            .NotEmpty().WithMessage("Queue Number is required.")
            .MaximumLength(20).WithMessage("Queue Number must not exceed 20 characters.");

        RuleFor(v => v.StartDate)
            .NotEmpty().WithMessage("Start Date is required.")
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Start Date must be today or in the future.");

        RuleFor(v => v.TargetNumbers)
            .NotEmpty().WithMessage("Target Numbers is required.")
            .Must(x => x != null && x.Count > 0).WithMessage("At least one target number is required.");
    }
}
