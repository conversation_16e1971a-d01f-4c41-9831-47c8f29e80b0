using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.AutoDialers.CreateAutoDialer;

internal sealed class CreateAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/autodialers", async (
            CreateAutoDialerCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/conversations/autodialers/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.Autodialers");
    }
}
