using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.AutoDialers.UpdateAutoDialer;

internal sealed class UpdateAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/conversations/autodialers/{id}", async (
            Guid id,
            UpdateAutoDialerCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("ID in the URL must match the ID in the request body");
            }
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.Autodialers");
    }
}
