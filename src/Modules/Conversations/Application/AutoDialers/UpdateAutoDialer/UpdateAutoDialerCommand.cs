using MediatR;
using Shared.Application;

namespace Conversations.Application.AutoDialers.UpdateAutoDialer;

public record UpdateAutoDialerCommand : IRequest<Result<bool>>
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public bool Active { get; init; }
    public string QueueNumber { get; init; }
    public DateTime StartDate { get; init; }
    public List<string> TargetNumbers { get; init; }
}
