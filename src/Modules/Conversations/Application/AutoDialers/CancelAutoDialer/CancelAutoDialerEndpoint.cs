using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.AutoDialers.CancelAutoDialer;

internal sealed class CancelAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/autodialers/{id}/cancel", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new CancelAutoDialerCommand(id);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                _ => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.Autodialers");
    }
}
