using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.CancelAutoDialer;

public class CancelAutoDialerCommandHandler(
    IConversationDbContext dbContext,
    IThreeCXService threeCXService
) : IRequestHandler<CancelAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IThreeCXService _threeCXService = threeCXService;

    public async Task<Result<bool>> Handle(CancelAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        try
        {
            var result = await _threeCXService.CancelAutoDialer(
                entity.Id);
            if (!result.IsSuccess)
            {
                return Result.Failure<bool>(result.Error.Code, result.Error.Description);
            }
            entity.Cancel();
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success(true);
        }
        catch (InvalidOperationException ex)
        {
            return Result.Failure<bool>("500", ex.Message);
        }
        catch (Exception ex)
        {
            return Result.Failure<bool>("500", $"Error cancelling auto dialer: {ex.Message}");
        }
    }
}
