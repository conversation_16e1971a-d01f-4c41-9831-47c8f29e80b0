using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.AutoDialers.GetAutoDialer;

internal sealed class GetAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/autodialers/{id}", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetAutoDialerQuery(id);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.Autodialers");
    }
}
