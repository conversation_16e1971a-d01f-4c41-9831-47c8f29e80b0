using Conversations.Application.Abstractions;
using Conversations.Domain.Calls;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.GetAutoDialer;

public class GetAutoDialerQueryHandler : IRequestHandler<GetAutoDialerQuery, Result<AutoDialerDto>>
{
    private readonly IConversationDbContext _dbContext;

    public GetAutoDialerQueryHandler(IConversationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<AutoDialerDto>> Handle(GetAutoDialerQuery request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (entity == null)
        {
            return Result.Failure<AutoDialerDto>("AutoDialer not found.");
        }
        var dto = entity.ToDto();
        dto.TotalCount = entity.TargetNumbers.Count;
        dto.DoneCount = _dbContext.Call.Count(c => c.AutoDialerId == entity.Id);
        dto.EndedCount = _dbContext.Call.Count(c => c.AutoDialerId == entity.Id && c.Status == CallStatus.Ended);
        dto.MissedCaount = _dbContext.Call.Count(c => c.AutoDialerId == entity.Id && c.Status == CallStatus.Missed);
        return Result.Success(dto);
    }
}
