using Conversations.Application.Abstractions;
using Conversations.Domain.AutoDialers;
using Conversations.Infrastructure.External.ThreeCXIntegration;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.StartAutoDialer;

public class StartAutoDialerCommandHandler(
    IConversationDbContext dbContext,
    IThreeCXService threeCXService
) : IRequestHandler<StartAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IThreeCXService _threeCXService = threeCXService;

    public async Task<Result<bool>> Handle(StartAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        if (entity.Status != AutoDialerStatus.Pending)
        {
            return Result.Failure<bool>("400", "Only pending auto dialers can be started.");
        }
        if (!entity.Active)
        {
            return Result.Failure<bool>("400", "Auto dialer must be active to start.");
        }
        try
        {
            var result = await _threeCXService.StartAutoDialer(
                entity.Id,
                entity.QueueNumber,
                entity.TargetNumbers);
            if (!result.IsSuccess)
            {
                return Result.Failure<bool>(result.Error.Code, result.Error.Description);
            }
            entity.Start();
            await _dbContext.SaveChangesAsync(cancellationToken);
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            return Result.Failure<bool>("500", $"Error starting auto dialer: {ex.Message}");
        }
    }
}
