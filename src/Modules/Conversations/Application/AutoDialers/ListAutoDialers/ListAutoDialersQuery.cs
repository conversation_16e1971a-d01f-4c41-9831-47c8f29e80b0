using Conversations.Domain.AutoDialers;
using MediatR;
using Shared.Application;

namespace Conversations.Application.AutoDialers.ListAutoDialers;

public record ListAutoDialersQuery : IRequest<PagedResult<AutoDialerDto>>
{
    public bool? Active { get; init; }
    public AutoDialerStatus? Status { get; init; }
    public string? SearchTerm { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? IsArchive { get; set; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 20;
    public string? SortProperty { get; init; }
    public string? SortType { get; init; }
}
