using Conversations.Application.Abstractions;
using Conversations.Domain.Calls;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.ListAutoDialers;

public class ListAutoDialersQueryHandler(
    IConversationDbContext dbContext
) : IRequestHandler<ListAutoDialersQuery, PagedResult<AutoDialerDto>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<PagedResult<AutoDialerDto>> Handle(ListAutoDialersQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.AutoDialers.AsQueryable();
        if (request.Active.HasValue)
        {
            query = query.Where(x => x.Active == request.Active.Value);
        }
        if (request.Status.HasValue)
        {
            query = query.Where(x => x.Status == request.Status.Value);
        }
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(x => x.Name.ToLower().Contains(searchTerm));
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(x => x.StartDate.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(x => x.StartDate.Date <= request.EndDate.Value.Date);
        }
        if (request.IsArchive.HasValue)
        {
            query = query.Where(x => x.IsArchive == request.IsArchive.Value);
        }
        var totalCount = await query.CountAsync(cancellationToken);
        query = !string.IsNullOrEmpty(request.SortProperty)
            ? request.SortProperty.ToLower() switch
            {
                "name" => request.SortType?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Name)
                    : query.OrderBy(x => x.Name),
                "startdate" => request.SortType?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.StartDate)
                    : query.OrderBy(x => x.StartDate),
                "status" => request.SortType?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Status)
                    : query.OrderBy(x => x.Status),
                "insertdate" => request.SortType?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.InsertDate)
                    : query.OrderBy(x => x.InsertDate),
                _ => query.OrderByDescending(x => x.InsertDate)
            }
            : query.OrderByDescending(x => x.InsertDate);
        var items = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new AutoDialerDto
            {
                Id = x.Id,
                Name = x.Name,
                Active = x.Active,
                QueueNumber = x.QueueNumber,
                Status = x.Status,
                StartDate = x.StartDate,
                InsertDate = x.InsertDate,
                UpdateDate = x.UpdateDate,
                TotalCount = x.TargetNumbers.Count,
                DoneCount = x.Call.Count,
                EndedCount = x.Call.Count(c => c.Status == CallStatus.Ended),
                MissedCaount = x.Call.Count(c => c.Status == CallStatus.Missed)
            })
            .ToListAsync(cancellationToken);
        var result = new PagedResult<AutoDialerDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount,
            SortProperty = request.SortProperty ?? "insertdate",
            SortType = request.SortType ?? "desc",
        };
        return result;
    }
}
