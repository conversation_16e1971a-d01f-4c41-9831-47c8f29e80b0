using Conversations.Domain.AutoDialers;

namespace Conversations.Application.AutoDialers;

public record AutoDialerDto
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public bool Active { get; init; }
    public string QueueNumber { get; init; }
    public int DoneCount { get; set; }
    public int TotalCount { get; set; }
    public int EndedCount { get; internal set; }
    public int MissedCaount { get; internal set; }
    public AutoDialerStatus Status { get; init; }
    public DateTime StartDate { get; init; }
    public List<string> TargetNumbers { get; init; }
    public DateTime InsertDate { get; init; }
    public DateTime? UpdateDate { get; init; }
}

public static class AutoDialerExtensions
{
    public static AutoDialerDto ToDto(this AutoDialer autoDialer)
    {
        return new AutoDialerDto
        {
            Id = autoDialer.Id,
            Name = autoDialer.Name,
            Active = autoDialer.Active,
            QueueNumber = autoDialer.QueueNumber,
            Status = autoDialer.Status,
            StartDate = autoDialer.StartDate,
            TargetNumbers = autoDialer.TargetNumbers,
            InsertDate = autoDialer.InsertDate,
            UpdateDate = autoDialer.UpdateDate
        };
    }
}
