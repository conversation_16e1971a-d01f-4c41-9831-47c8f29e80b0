using Conversations.Application.Abstractions;
using MediatR;
using Shared.Application;

namespace Conversations.Application.ThreeCX.GetCallHistory;

public class GetCallHistoryQueryHandler(
    IThreeCXService threeCXService
) : IRequestHandler<GetCallHistoryQuery, Result<List<CallHistoryResponse>>>
{
    private readonly IThreeCXService _threeCXService = threeCXService;

    public async Task<Result<List<CallHistoryResponse>>> Handle(GetCallHistoryQuery request, CancellationToken cancellationToken)
    {
        var result = await _threeCXService.GetCallHistory(request.HistoryOfTheCall);
        if (!result.IsSuccess)
        {
            return Result.Failure<List<CallHistoryResponse>>(result.Error.Code, result.Error.Description);
        }
        var response = result.Value.Select(x => new CallHistoryResponse
        {
            CallHistoryId = x.CallHistoryId,
            SourceEntityType = x.SourceEntityType,
            SourceParticipantPhoneNumber = x.SourceParticipantPhoneNumber,
            SourceParticipantName = x.SourceParticipantName,
            SourceParticipantTrunkDid = x.SourceParticipantTrunkDid,
            CreationMethod = x.CreationMethod,
            RecordingUrl = x.RecordingUrl,
            StartTime = x.StartTime,
            EndTime = x.EndTime,
            Transcription = x.Transcription,
            Summary = x.Summary

        }).ToList();
        return Result.Success(response);
    }
}
