using MediatR;
using Shared.Application;

namespace Conversations.Application.ThreeCX.GetCallHistory;

public record GetCallHistoryQuery(
    string HistoryOfTheCall
) : IRequest<Result<List<CallHistoryResponse>>>;

public class CallHistoryResponse
{
    public string? CallHistoryId { get; internal set; }
    public string? SourceEntityType { get; internal set; }
    public string? SourceParticipantPhoneNumber { get; internal set; }
    public string? SourceParticipantName { get; internal set; }
    public string? SourceParticipantTrunkDid { get; internal set; }
    public string? CreationMethod { get; internal set; }
    public string? RecordingUrl { get; internal set; }
    public DateTime? StartTime { get; internal set; }
    public DateTime? EndTime { get; internal set; }
    public string? Transcription { get; internal set; }
    public string? Summary { get; internal set; }
}
