namespace Conversations.Application.Calls;

public class CallInfo
{
    public string Id { get; set; }
    public string ExternalParty { get; set; }
    public string Extension { get; set; }
    public string Direction { get; set; }
    public string Initiator { get; set; }
    public string HistoryOfTheCall { get; set; }
    public Guid? AutoDialerId { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? AnsweredAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public CallState State { get; set; }
}

public enum CallState
{
    None,
    Talking,
    Answered,
    Missed,
    Ended
}
