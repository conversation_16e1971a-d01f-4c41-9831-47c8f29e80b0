using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.Calls.CallEvents;

internal sealed class CallEventEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/calls/event", async (
            CallInfo callInfo,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new CallEventCommand(
                callInfo.Id,
                callInfo.HistoryOfTheCall,
                callInfo.ExternalParty,
                callInfo.Extension,
                callInfo.Direction,
                callInfo.Initiator,
                callInfo.StartedAt,
                callInfo.AnsweredAt,
                callInfo.EndedAt,
                callInfo.State,
                callInfo.AutoDialerId
            );
            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Conversations.CallEvents")
        .WithGroupName("apiv1")
        .AllowAnonymous();
    }
}
