using MediatR;
using Shared.Application;

namespace Conversations.Application.Calls.CallEvents;

public record CallEventCommand(
    string Id,
    string HistoryOfTheCall,
    string ExternalParty,
    string Extension,
    string Direction,
    string Initiator,
    DateTime? StartedAt,
    DateTime? AnsweredAt,
    DateTime? EndedAt,
    CallState State,
    Guid? AutoDialerId
) : IRequest<Result>;
