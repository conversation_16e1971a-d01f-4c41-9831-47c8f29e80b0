using Conversations.Application.Abstractions;
using Conversations.Application.Calls.CallHubs;
using Conversations.Domain.Calls;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using PhoneNumbers;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;

namespace Conversations.Application.Calls.CallEvents;

public class CallEventCommandHandler(
    IConversationDbContext dbContext,
    IHubContext<CallHub> hubContext,
    ISharedUserService sharedUserService,
    ISharedCustomerService sharedCustomerService,
    AppSettings appSettings
) : IRequestHandler<CallEventCommand, Result>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IHubContext<CallHub> _hubContext = hubContext;
    private readonly ISharedUserService _sharedUserService = sharedUserService;
    private readonly ISharedCustomerService _sharedCustomerService = sharedCustomerService;
    private readonly AppSettings _appSettings = appSettings;

    public async Task<Result> Handle(CallEventCommand command, CancellationToken cancellationToken)
    {
        var extension = command.Extension;
        var phoneUtil = PhoneNumberUtil.GetInstance();
        var parsedNumber = phoneUtil.Parse(command.ExternalParty, _appSettings.DefaultRegion);
        var customerPhone = parsedNumber.NationalNumber.ToString();
        var customerPhonePrefix = parsedNumber.CountryCode.ToString();
        var direction = command.Direction == "Inbound" ? CallDirection.Inbound : CallDirection.Outbound;
        var userResult = await _sharedUserService.GetUserAsync(extension);
        if (!userResult.IsSuccess)
        {
            return Result.Failure("404", "User not found");
        }
        var customerResult = await _sharedCustomerService.GetCustomerAsync(customerPhone);
        var customerName = customerResult.IsSuccess ? customerResult.Value?.Name + " " + customerResult.Value?.Surname : "";
        var customerId = customerResult.IsSuccess ? customerResult.Value?.Id : null;
        Guid? tempCustomerId = null;
        var tempCustomerName = string.Empty;
        if (!customerResult.IsSuccess)
        {
            var tempCustomerResult = await _sharedCustomerService.GetTempCustomerAsync(customerPhone);
            if (tempCustomerResult.IsSuccess)
            {
                tempCustomerId = tempCustomerResult.Value?.Id;
                tempCustomerName = tempCustomerResult.Value?.Name + " " + tempCustomerResult.Value?.Surname;
            }
        }
        var call = await _dbContext.Call.FirstOrDefaultAsync(x => x.CallId == command.Id, cancellationToken);
        if (call is null)
        {
            var newcall = Call.Create(
                command.Id,
                command.HistoryOfTheCall,
                customerPhone,
                customerPhonePrefix,
                userResult.Value.Id,
                direction,
                customerId
            );
            newcall.AutoDialerId = command.AutoDialerId;
            newcall.Direction = direction;
            newcall.Status = (CallStatus)(int)command.State;
            newcall.StartTime = command.StartedAt ?? DateTime.Now;
            newcall.EndTime = command.EndedAt ?? DateTime.Now;
            var entity = _dbContext.Call.Add(newcall);
            await _dbContext.SaveChangesAsync(cancellationToken);
            call = entity.Entity;
        }
        else
        {
            call.SetCustomer(customerId);
            call.AutoDialerId = command.AutoDialerId;
            call.Direction = direction;
            call.Status = (CallStatus)(int)command.State;
            call.StartTime = command.StartedAt ?? DateTime.Now;
            call.EndTime = command.EndedAt ?? ((int)command.State == 4 ? DateTime.Now : null);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        if (customerPhone.Length > 6)
        {
            await _hubContext.Clients.User(userResult.Value.Id.ToString()).SendAsync("CallEvent", new
            {
                id = call.Id,
                callId = command.Id,
                externalParty = customerPhone,
                phonePrefix = customerPhonePrefix,
                extension = command.Extension,
                direction = command.Direction,
                initiator = command.Initiator,
                historyOfTheCall = command.HistoryOfTheCall,
                startedAt = command.StartedAt,
                answeredAt = command.AnsweredAt,
                endedAt = command.EndedAt,
                state = command.State,
                customer = customerName,
                customerId,
                tempCustomerId,
                tempCustomerName,
            }, cancellationToken: cancellationToken);
        }
        return Result.Success();
    }
}
