﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Conversations.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class ConversationData004 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AutoDialerId",
                schema: "Conversations",
                table: "Call",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Call_AutoDialerId",
                schema: "Conversations",
                table: "Call",
                column: "AutoDialerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Call_AutoDialer_AutoDialerId",
                schema: "Conversations",
                table: "Call",
                column: "AutoDialerId",
                principalSchema: "Conversations",
                principalTable: "AutoDialer",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Call_AutoDialer_AutoDialerId",
                schema: "Conversations",
                table: "Call");

            migrationBuilder.DropIndex(
                name: "IX_Call_AutoDialerId",
                schema: "Conversations",
                table: "Call");

            migrationBuilder.DropColumn(
                name: "AutoDialerId",
                schema: "Conversations",
                table: "Call");
        }
    }
}
