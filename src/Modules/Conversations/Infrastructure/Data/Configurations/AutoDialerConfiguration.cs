using Conversations.Domain.AutoDialers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Conversations.Infrastructure.Data.Configurations;

public class AutoDialerConfiguration : IEntityTypeConfiguration<AutoDialer>
{
    public void Configure(EntityTypeBuilder<AutoDialer> builder)
    {
        builder.ToTable("AutoDialer", "Conversations");

        builder.<PERSON><PERSON><PERSON>(e => e.Id);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.QueueNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.StartDate)
            .IsRequired();

        builder.Property(e => e.Active)
            .IsRequired();

        builder.Property(e => e.Status)
            .IsRequired();

        // JSON kolon yapılandırması
        builder.Property(e => e.TargetNumbers)
            .HasColumnType("nvarchar(max)")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions()),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, new System.Text.Json.JsonSerializerOptions()) ?? new List<string>());
    }
}
