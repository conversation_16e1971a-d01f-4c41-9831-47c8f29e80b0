using Shared.Domain;

namespace Conversations.Domain.AutoDialers;

public record AutoDialerCreatedEvent(Guid AutoDialerId) : BaseDomainEvent, IDomainEvent;

public record AutoDialerUpdatedEvent(Guid AutoDialerId) : BaseDomainEvent, IDomainEvent;

public record AutoDialerStatusChangedEvent(Guid AutoDialerId, AutoDialerStatus Status) : BaseDomainEvent, IDomainEvent;

public record AutoDialerStartedEvent(Guid AutoDialerId) : BaseDomainEvent, IDomainEvent;

public record AutoDialerCompletedEvent(Guid AutoDialerId) : BaseDomainEvent, IDomainEvent;

public record AutoDialerCancelledEvent(Guid AutoDialerId) : BaseDomainEvent, IDomainEvent;
