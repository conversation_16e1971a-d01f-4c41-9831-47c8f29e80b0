using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.ListPermissionRules;

public class ListPermissionRuleEndpoint : IEndpoint
{
    public sealed record ListPermissionRuleRequest(Guid? UserId, Guid? RoleId);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/users/permissions/listPermissionRule", async (
            [AsParameters] ListPermissionRuleRequest request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListPermissionRuleQuery(request.UserId, request.RoleId);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
