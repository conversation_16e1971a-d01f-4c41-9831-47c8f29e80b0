using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Permissions.ListPermissionRules;

public class ListPermissionRuleQueryHandler(IUserDbContext context) : IRequestHandler<ListPermissionRuleQuery, PagedResult<PermissionRuleDTO>>
{
    private readonly IUserDbContext _context = context;

    public async Task<PagedResult<PermissionRuleDTO>> Handle(ListPermissionRuleQuery request, CancellationToken cancellationToken)
    {
        var query = _context.PermissionRule.Include(x => x.Permission).AsQueryable();
        if (request.UserId.HasValue)
        {
            query = query.Where(x => x.UserId == request.UserId.Value);
        }
        if (request.RoleId.HasValue)
        {
            query = query.Where(x => x.RoleId == request.RoleId.Value);
        }
        var items = await query.Select(x =>
        new PermissionRuleDTO(
            x.PermissionId,
            x.UserId, x.RoleId,
            x.Permission.Key,
            x.Permission.Url)).ToListAsync(cancellationToken);
        return new PagedResult<PermissionRuleDTO>(items, true, null);
    }
}
