using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.ListPermissions;

internal sealed class ListPermissionEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/users/permissions/list", async (
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListPermissionQuery();
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Permissions");
    }
}
