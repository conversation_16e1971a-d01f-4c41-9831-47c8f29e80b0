using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using Shared.Application;

namespace Users.Application.Permissions.ListPermissions;

public record ListPermissionQuery()
: IRequest<PagedResult<PermissionsResponse>>;

public record PermissionsResponse
{
    public Guid Id { get; set; }
    public string Key { get; set; }
    public string Name { get; set; }
    public bool IsMenu { get; set; }
    public Guid? TopPermissionId { get; set; }
}
