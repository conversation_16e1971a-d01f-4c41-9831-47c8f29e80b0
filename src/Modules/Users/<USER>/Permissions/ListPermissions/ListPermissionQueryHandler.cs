using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Permissions.ListPermissions;

public class ListPermissionQueryHandler(
    IUserDbContext dbContext
) : IRequestHandler<ListPermissionQuery, PagedResult<PermissionsResponse>>
{
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<PagedResult<PermissionsResponse>> Handle(ListPermissionQuery request, CancellationToken cancellationToken)
    {
        var permissions = await _dbContext.Permission
            .Select(x => new PermissionsResponse
            {
                Id = x.Id,
                Key = x.Key,
                Name = x.Name,
                IsMenu = x.IsMenu,
                TopPermissionId = x.TopPermissionId
            })
            .ToListAsync(cancellationToken: cancellationToken);
        return permissions;
    }
}
