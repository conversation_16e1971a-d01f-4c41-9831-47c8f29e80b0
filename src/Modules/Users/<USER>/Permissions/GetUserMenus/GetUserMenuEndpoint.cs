using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.GetUserMenus;

public class GetUserMenuEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/users/permissions/menu", async (
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new GetUserMenusQuery();
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
