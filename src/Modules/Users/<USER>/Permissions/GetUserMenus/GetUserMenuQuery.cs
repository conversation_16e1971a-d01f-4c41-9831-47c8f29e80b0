

using MediatR;
using Shared.Application;

namespace Users.Application.Permissions.GetUserMenus;

public record GetUserMenusQuery() : IRequest<Result<List<PermissionResponse>>>;

public record PermissionResponse()
{
    public Guid Id { get; set; }
    public string Key { get; set; }
    public string Name { get; set; }
    public bool IsMenu { get; set; }
    public string? Icon { get; set; }
    public string? Url { get; set; }
    public int Order { get; set; } = 0;
}
