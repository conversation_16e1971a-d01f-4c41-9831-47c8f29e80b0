using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.Permissions.GetUserMenus;

public class GetUserMenuQueryHandler(
    IWorkContext workContext,
    IUserDbContext dbContext
) : IRequestHandler<GetUserMenusQuery, Result<List<PermissionResponse>>>
{
    private readonly IWorkContext _workContext = workContext;
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<Result<List<PermissionResponse>>> Handle(GetUserMenusQuery request, CancellationToken cancellationToken)
    {
        if (_workContext.HasRole("Admin"))
        {
            var menus = await _dbContext.Permission
                .Where(x => x.IsMenu == true)
                .Select(x => new PermissionResponse
                {
                    Id = x.Id,
                    Key = x.Key,
                    Name = x.Name,
                    IsMenu = x.IsMenu,
                    Icon = x.Icon,
                    Url = x.Url,
                    Order = x.Order
                })
                .OrderBy(x => x.Order)
                .ToListAsync(cancellationToken: cancellationToken);
            return Result.Success(menus);
        }
        else
        {
            var user = await _workContext.GetUserAsync();
            var userId = user.Id;
            var roleIds = user.RoleIds;
            var menus = await _dbContext.PermissionRule
                .Where(x => x.UserId == userId || roleIds.Contains(x.RoleId.Value))
                .Select(x => x.Permission)
                .Select(x => new PermissionResponse
                {
                    Id = x.Id,
                    Key = x.Key,
                    Name = x.Name,
                    IsMenu = x.IsMenu,
                    Icon = x.Icon,
                    Url = x.Url,
                    Order = x.Order
                })
                .OrderBy(x => x.Order)
                .ToListAsync(cancellationToken: cancellationToken);
            return Result.Success(menus);
        }
    }
}
