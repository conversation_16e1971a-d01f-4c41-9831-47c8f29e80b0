using System.Linq;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Auth;

namespace Users.Application.Permissions.SetUserPermissionRules;

public class SetUserPermissionRuleCommandHandler(
    IUserDbContext context,
    HybridCache cache
    ) : IRequestHandler<SetUserPermissionRuleCommand, Result>
{
    private readonly IUserDbContext _context = context;
    private readonly HybridCache _cache = cache;

    public async Task<Result> Handle(SetUserPermissionRuleCommand request, CancellationToken cancellationToken)
    {
        await _context.PermissionRule
            .Where(x => x.UserId == request.UserId)
            .ExecuteDeleteAsync(cancellationToken: cancellationToken);
        var permissionRules = request.SetUserPermissionRuleIds.Select(x => new PermissionRule
        {
            PermissionId = x,
            UserId = request.UserId,
            RoleId = null
        }).ToList();
        _context.PermissionRule.AddRange(permissionRules);
        await _context.SaveChangesAsync(cancellationToken);
        await _cache.RemoveAsync("PermissionRules");
        return Result.Success();
    }
}
