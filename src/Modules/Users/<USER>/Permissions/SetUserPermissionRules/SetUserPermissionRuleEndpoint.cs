using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.SetUserPermissionRules;

public class SetUserPermissionRuleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("api/v1/users/permissions/setuserpermissions/{UserId}", async (
            Guid UserId,
            List<Guid> SetUserPermissionRuleIds,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new SetUserPermissionRuleCommand(UserId, SetUserPermissionRuleIds);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Permissions");
    }
}
