using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Auth;

namespace Users.Application.Permissions.SetPermissionRules;

public class SetPermissionRuleCommandHandler(
    IUserDbContext context,
    HybridCache cache
    ) : IRequestHandler<SetPermissionRuleCommand, Result>
{
    private readonly IUserDbContext _context = context;
    private readonly HybridCache _cache = cache;

    public async Task<Result> Handle(SetPermissionRuleCommand request, CancellationToken cancellationToken)
    {
        var permissionRules = request.SetPermissionRules.Select(x => new PermissionRule
        {
            PermissionId = x.PermissionId,
            UserId = x.UserId,
            RoleId = x.RoleId
        }).ToList();
        _context.PermissionRule.AddRange(permissionRules);
        await _context.SaveChangesAsync(cancellationToken);
        await _cache.RemoveAsync("PermissionRules");
        return Result.Success();
    }
}
