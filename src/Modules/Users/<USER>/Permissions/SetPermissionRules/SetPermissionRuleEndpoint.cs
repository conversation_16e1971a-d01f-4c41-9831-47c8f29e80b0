using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.SetPermissionRules;

public class SetPermissionRuleEndpoint : IEndpoint
{
    public sealed record SetPermissionRuleRequest(List<PermissionRuleDTO> SetPermissionRules);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("api/v1/users/permissions/list", async (
            SetPermissionRuleRequest request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new SetPermissionRuleCommand(request.SetPermissionRules);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Permissions");
    }
}
