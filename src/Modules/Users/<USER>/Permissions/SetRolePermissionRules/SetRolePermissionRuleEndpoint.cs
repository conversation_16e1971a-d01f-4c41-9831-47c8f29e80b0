using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Permissions.SetRolePermissionRules;

public class SetRolePermissionRuleEndpoint : IEndpoint
{

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("api/v1/users/permissions/setrolepermissions/{RoleId}", async (
            Guid RoleId,
            List<Guid> SetRolePermissionRuleIds,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new SetRolePermissionRuleCommand(RoleId, SetRolePermissionRuleIds);
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Users.Permissions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Permissions");
    }
}
