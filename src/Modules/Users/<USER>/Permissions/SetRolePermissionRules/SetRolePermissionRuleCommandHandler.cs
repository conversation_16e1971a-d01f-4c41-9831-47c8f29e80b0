using System.Linq;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Auth;

namespace Users.Application.Permissions.SetRolePermissionRules;

public class SetRolePermissionRuleCommandHandler(
    IUserDbContext context,
    HybridCache cache
    ) : IRequestHandler<SetRolePermissionRuleCommand, Result>
{
    private readonly IUserDbContext _context = context;
    private readonly HybridCache _cache = cache;

    public async Task<Result> Handle(SetRolePermissionRuleCommand request, CancellationToken cancellationToken)
    {
        await _context.PermissionRule
            .Where(x => x.RoleId == request.RoleId)
            .ExecuteDeleteAsync(cancellationToken: cancellationToken);
        var permissionRules = request.SetRolePermissionRuleIds.Select(x => new PermissionRule
        {
            PermissionId = x,
            UserId = null,
            RoleId = request.RoleId
        }).ToList();
        _context.PermissionRule.AddRange(permissionRules);
        await _context.SaveChangesAsync(cancellationToken);
        await _cache.RemoveAsync("PermissionRules");
        return Result.Success();
    }
}
