using Microsoft.EntityFrameworkCore;
using Shared.Contracts;
using Users.Application.Abstractions;

namespace Users.Application.Services;

public class SharedDepartmentService(
    IUserDbContext dbContext
) : ISharedDepartmentService
{
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<List<SharedDepartmentDTO>> GetDepartmentsByIdsAsync(List<Guid> departmentIds)
    {
        var departments = await _dbContext.Department
            .Where(d => departmentIds.Contains(d.Id))
            .ToListAsync();

        return departments.Select(department => new SharedDepartmentDTO
        {
            Id = department.Id,
            Name = department.Name
        }).ToList();
    }
}
