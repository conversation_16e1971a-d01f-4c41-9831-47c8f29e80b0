using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.PauseTypes.GetPauseType;

public class GetPauseTypeEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/users/pausetypes/{id:guid}", async (
            Guid id,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new GetPauseTypeQuery(id), cancellationToken);
            return result.Match(
                pauseType => Results.Ok(pauseType),
                CustomResults.Problem);
        })
        .WithTags("Users.PauseTypes")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.PauseTypes");
    }
}
