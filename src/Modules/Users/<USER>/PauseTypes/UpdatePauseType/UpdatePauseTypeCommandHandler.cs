using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.PauseTypes.UpdatePauseType;

public class UpdatePauseTypeCommandHandler(
    IUserDbContext context
) : IRequestHandler<UpdatePauseTypeCommand, Result>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result> Handle(UpdatePauseTypeCommand request, CancellationToken cancellationToken)
    {
        var pauseType = await _context.PauseType
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (pauseType == null)
        {
            return Result.Failure("<PERSON>la türü bulunamadı.");
        }
        var existingPauseType = await _context.PauseType
            .FirstOrDefaultAsync(x => x.Name == request.Name && x.Id != request.Id, cancellationToken);
        if (existingPauseType != null)
        {
            return Result.Failure("Bu isimde başka bir mola türü zaten mevcut.");
        }
        pauseType.Update(request.Name, request.MaxDuration, request.Active);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
