using FluentValidation;

namespace Users.Application.PauseTypes.UpdatePauseType;

public class UpdatePauseTypeCommandValidator : AbstractValidator<UpdatePauseTypeCommand>
{
    public UpdatePauseTypeCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Mola türü ID'si zorunludur.");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Mola türü adı zorunludur.")
            .MaximumLength(100)
            .WithMessage("Mola türü adı en fazla 100 karakter olabilir.");

        RuleFor(x => x.MaxDuration)
            .GreaterThan(0)
            .WithMessage("Maksimum süre 0'dan büyük olmalıdır.")
            .LessThanOrEqualTo(28800) // 8 saat
            .WithMessage("Maksimum süre 8 saati geçemez.");
    }
}
