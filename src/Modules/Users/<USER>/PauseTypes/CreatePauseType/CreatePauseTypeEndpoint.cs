using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.PauseTypes.CreatePauseType;

public class CreatePauseTypeEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("api/v1/users/pausetypes", async (
            [FromBody] CreatePauseTypeCommand command,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/users/pausetypes/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Users.PauseTypes")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.PauseTypes");
    }
}
