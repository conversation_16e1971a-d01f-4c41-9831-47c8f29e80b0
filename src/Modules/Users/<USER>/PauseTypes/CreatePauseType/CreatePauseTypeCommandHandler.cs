using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Pauses;

namespace Users.Application.PauseTypes.CreatePauseType;

public class CreatePauseTypeCommandHandler(
    IUserDbContext context
) : IRequestHandler<CreatePauseTypeCommand, Result<Guid>>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result<Guid>> <PERSON>le(CreatePauseTypeCommand request, CancellationToken cancellationToken)
    {
        // Aynı isimde mola türü var mı kontrol et
        var existingPauseType = await _context.PauseType
            .FirstOrDefaultAsync(x => x.Name == request.Name, cancellationToken);

        if (existingPauseType != null)
        {
            return Result.Failure<Guid>("Bu isimde bir mola türü zaten mevcut.");
        }

        var pauseType = new PauseType(request.Name, request.MaxDuration);
        
        if (!request.Active)
        {
            pauseType.Deactivate();
        }

        await _context.PauseType.AddAsync(pauseType, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success(pauseType.Id);
    }
}
