using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.PauseTypes.DeletePauseType;

public class DeletePauseTypeCommandHandler(
    IUserDbContext context
) : IRequestHandler<DeletePauseTypeCommand, Result>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result> Handle(DeletePauseTypeCommand request, CancellationToken cancellationToken)
    {
        var pauseType = await _context.PauseType
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (pauseType == null)
        {
            return Result.Failure("Mola türü bulunamadı.");
        }
        var isInUse = await _context.Pause
            .AnyAsync(x => x.TypeId == request.Id, cancellationToken);
        if (isInUse)
        {
            pauseType.Deactivate();
            await _context.SaveChangesAsync(cancellationToken);
            return Result.Success();
        }
        _context.PauseType.Remove(pauseType);
        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
