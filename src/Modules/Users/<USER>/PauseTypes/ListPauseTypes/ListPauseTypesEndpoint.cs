using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.PauseTypes.ListPauseTypes;

public class ListPauseTypesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("api/v1/users/pausetypes", async (
            [AsParameters] ListPauseTypesQuery query,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(query, cancellationToken);
            return result.Match(
                pauseTypes => Results.Ok(pauseTypes),
                CustomResults.Problem);
        })
        .WithTags("Users.PauseTypes")
        .WithGroupName("apiv1")
        .RequireAuthorization();
    }
}
