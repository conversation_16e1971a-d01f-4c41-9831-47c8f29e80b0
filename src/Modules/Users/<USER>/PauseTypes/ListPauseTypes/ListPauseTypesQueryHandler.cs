using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;

namespace Users.Application.PauseTypes.ListPauseTypes;

public class ListPauseTypesQueryHandler(
    IUserDbContext context
) : IRequestHandler<ListPauseTypesQuery, PagedResult<ListPauseTypesResponse>>
{
    private readonly IUserDbContext _context = context;

    public async Task<PagedResult<ListPauseTypesResponse>> Handle(ListPauseTypesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.PauseType.AsQueryable();

        // Filtreleme
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(x => x.Name.Contains(request.SearchTerm));
        }

        if (request.Active.HasValue)
        {
            query = query.Where(x => x.Active == request.Active.Value);
        }

        // Toplam kayıt sayısı
        var totalCount = await _context.PauseType.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        // Say<PERSON>lama ve sıralama
        var pauseTypes = await query
            .OrderBy(x => x.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new ListPauseTypesResponse(
                x.Id,
                x.Name,
                x.MaxDuration,
                x.Active
            ))
            .ToListAsync(cancellationToken);

        return new PagedResult<ListPauseTypesResponse>(pauseTypes)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = filteredCount
        };
    }
}
