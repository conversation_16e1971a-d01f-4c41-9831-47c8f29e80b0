using FluentValidation;
using Microsoft.AspNetCore.Identity;
using Users.Domain.Account;

namespace Users.Application.Roles.CreateRole;

public class CreateRoleCommandValidator : AbstractValidator<CreateRoleCommand>
{
    private readonly RoleManager<Role> _roleManager;

    public CreateRoleCommandValidator(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Rol adı boş olamaz.")
            .MaximumLength(100).WithMessage("Rol adı en fazla 100 karakter olabilir.")
            .MustAsync(BeUniqueName).WithMessage("Bu rol adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
    {
        return await _roleManager.FindByNameAsync(name) == null;
    }
}
