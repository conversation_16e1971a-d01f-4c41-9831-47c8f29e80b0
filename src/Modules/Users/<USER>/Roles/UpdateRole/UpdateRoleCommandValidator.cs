using FluentValidation;
using Microsoft.AspNetCore.Identity;
using Users.Domain.Account;

namespace Users.Application.Roles.UpdateRole;

public class UpdateRoleCommandValidator : AbstractValidator<UpdateRoleCommand>
{
    private readonly RoleManager<Role> _roleManager;

    public UpdateRoleCommandValidator(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Rol ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Rol adı boş olamaz.")
            .MaximumLength(100).WithMessage("Rol adı en fazla 100 karakter olabilir.")
            .MustAsync(BeUniqueNameForOtherRoles).WithMessage("Bu rol adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueNameForOtherRoles(UpdateRoleCommand command, string name, CancellationToken cancellationToken)
    {
        var existingRole = await _roleManager.FindByNameAsync(name);
        return existingRole == null || existingRole.Id.ToString() == command.Id.ToString();
    }
}
