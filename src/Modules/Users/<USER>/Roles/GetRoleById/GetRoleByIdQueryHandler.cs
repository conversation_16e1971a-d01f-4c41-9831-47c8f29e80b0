using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;

namespace Users.Application.Roles.GetRoleById;

public class GetRoleByIdQueryHandler(
    RoleManager<Role> roleManager,
    IUserDbContext dbContext
) : IRequestHandler<GetRoleByIdQuery, Result<RoleDto>>
{
    private readonly RoleManager<Role> _roleManager = roleManager;
    private readonly IUserDbContext _dbContext = dbContext;

    public async Task<Result<RoleDto>> Handle(GetRoleByIdQuery request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());
        if (role == null)
        {
            return Result.Failure<RoleDto>("Rol bulunamadı.");
        }
        var userCount = await _dbContext.UserRoles
            .CountAsync(ur => ur.RoleId == role.Id, cancellationToken);
        var roleDto = new RoleDto
        {
            Id = role.Id,
            Name = role.Name ?? "",
            UserCount = userCount,
            IsBase = role.IsBase,
            IsAssignable = role.IsAssignable
        };
        return Result.Success(roleDto);
    }
}
