using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Management.BulkDeleteUser;

public class BulkDeleteUserEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("api/v1/users/management/bulk-delete", async (
            Guid[] ids,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var command = new BulkDeleteUserCommand(ids);
            var result = await sender.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Users.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}
