using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;
using Users.Domain.Account.Events;

namespace Users.Application.Management.BulkDeleteUser;

public class BulkDeleteUserCommandHandler(
    UserManager<User> userManager,
    IUserDbContext context
) : IRequestHandler<BulkDeleteUserCommand, Result>
{
    private readonly UserManager<User> _userManager = userManager;
    private readonly IUserDbContext _context = context;

    public async Task<Result> Handle(BulkDeleteUserCommand request, CancellationToken cancellationToken)
    {
        foreach (var id in request.Ids)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
            if (user == null)
            {
                return Result.Failure(UserErrors.NotFoundError);
            }
            user.IsDeleted = true;
            user.Active = false;
            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                return Result.Failure(new ValidationError(result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
            }
            user.Raise(new UserDeletedDomainEvent(user.Id));
        }

        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
