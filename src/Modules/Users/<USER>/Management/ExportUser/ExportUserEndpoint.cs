using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Users.Application.Users.ExportUser;

public class ExportUserEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/users/export", async (
            [FromBody] ExportUserQuery request,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(request, cancellationToken);
            return result.Match(
                data => Results.File(data.Value, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Users.xlsx"),
                CustomResults.Problem
            );
        })
        .WithTags("Users.Management")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management") 
        .WithSummary("Export users to Excel")
        .WithDescription("Exports all users to an Excel file.");
    }
}
