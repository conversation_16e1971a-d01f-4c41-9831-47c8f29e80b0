using System.Text.Json;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization;
using Users.Application.Abstractions;
using Users.Application.Management.ImportUser;
using Users.Domain.Account;

namespace Users.Application.Users.ExportUser;

public class ExportUserQueryHandler : IRequestHandler<ExportUserQuery, Result<byte[]>>
{
    private readonly IUserDbContext _dbContext;
    private readonly ExcelHelper _excelHelper;
    private readonly IWorkContext _workContext;
    private readonly ILogger<ExportUserQueryHandler> _logger;
    private readonly ILocalizer _localizer;
    private readonly IEventBus _eventBus;

    public ExportUserQueryHandler(IUserDbContext dbContext, ExcelHelper excelHelper, IWorkContext workContext, ILogger<ExportUserQueryHandler> logger, ILocalizer localizer, IEventBus eventBus)
    {
        _dbContext = dbContext;
        _excelHelper = excelHelper;
        _logger = logger;
        _eventBus = eventBus;
        _logger = logger;
        _localizer = localizer;
        _workContext = workContext;
    }

    public async Task<Result<byte[]>> Handle(ExportUserQuery request, CancellationToken cancellationToken)
    {
        IQueryable<User> query = _dbContext.Users.AsNoTracking();

        if (request.SelectedIds is { Count: > 0 })
        {
            query = query.Where(u => request.SelectedIds.Contains(u.Id));
        }
        else if (request.Filter is not null)
        {
            query = ApplyUserFilters(query, request.Filter);
        }

        query = query.OrderBy(u => u.Name).ThenBy(u => u.Surname);

        var users = await query.ToListAsync(cancellationToken);

        if (users == null || !users.Any())
        {
            return Result.Failure<byte[]>(Error.NotFound("Users.NotFound", _localizer.Get("User.NotFound")));
        }
        if (users.Count > 10000)
        {
            return Result.Failure<byte[]>(Error.Validation("Export.LimitExceeded", _localizer.Get("Export.LimitExceeded")));
        }

        var dtos = users.Select(u => new ExportUserExcelDto
        {
            Name = u.Name,
            Surname = u.Surname,
            Email = u.Email,
            Phone = u.PhoneNumber,
            Group = u.UserRole?.FirstOrDefault()?.Role?.Name ?? null,
            DepartmanName = u.UserDepartment?.FirstOrDefault()?.Department?.Name ?? null,
            Status = u.Active
                ? _localizer.Get("Users.Status.Active")
                : _localizer.Get("Users.Status.Inactive"),
            ExtentionNo = u.ThreeCXExtension,
            InsertDate = u.InsertDate.ToString("dd/MM/yyyy"),
        }).ToList();

        var file = _excelHelper.ExportToExcel(dtos, request.SheetName);

        _logger.LogInformation(
            "Export requested. Module: {Module}, TotalExported: {Total}, Filter: {@Filter}, UserId: {UserId}, TimestampUtc: {@Timestamp}",
            "User",
            users.Count,
            request.Filter,
            _workContext.UserId,
            DateTime.Now
        );

        await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
            _workContext.UserId,
            "User",
            users.Count,
            request.Filter != null,
            request.SelectedIds?.Count,
            $"{request.SheetName}_{DateTime.Now:yyyyMMdd_HHmm}.xlsx",
            request.Filter
        ), cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(file);
    }
    private IQueryable<User> ApplyUserFilters(IQueryable<User> query, UserFilterRequest filter)
    {
        if (!string.IsNullOrWhiteSpace(filter.Name))
            query = query.Where(u => u.Name.Contains(filter.Name));

        if (!string.IsNullOrWhiteSpace(filter.Surname))
            query = query.Where(u => u.Surname.Contains(filter.Surname));

        if (!string.IsNullOrWhiteSpace(filter.Email))
            query = query.Where(u => u.Email != null && u.Email.Contains(filter.Email));

        if (!string.IsNullOrWhiteSpace(filter.ThreeCXExtension))
            query = query.Where(u => u.ThreeCXExtension == filter.ThreeCXExtension);

        if (!string.IsNullOrWhiteSpace(filter.PhonePrefix))
            query = query.Where(u => u.PhonePrefix == filter.PhonePrefix);

        if (filter.Active.HasValue)
            query = query.Where(u => u.Active == filter.Active.Value);


        return query;
    }

}
