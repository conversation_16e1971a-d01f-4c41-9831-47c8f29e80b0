﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData014 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "PauseType",
                keyColumn: "Id",
                keyValue: new Guid("71dcde3c-225a-4c9d-85f1-bf6d9a9c42b6"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "PauseType",
                keyColumn: "Id",
                keyValue: new Guid("82a5b1c4-3f3b-4c7d-96e1-df7a0c9d48c8"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "PauseType",
                keyColumn: "Id",
                keyValue: new Guid("93b6c2d5-4f4c-5d8e-a7f2-ef8b0da59d9d"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "PauseType",
                keyColumn: "Id",
                keyValue: new Guid("a4c7d3e6-5e5d-6f9f-b8f3-f9c1e1f6ae0e"));

            migrationBuilder.AlterColumn<bool>(
                name: "Active",
                schema: "Users",
                table: "PauseType",
                type: "bit",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.CreateIndex(
                name: "UK_PauseType_Name",
                schema: "Users",
                table: "PauseType",
                column: "Name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "UK_PauseType_Name",
                schema: "Users",
                table: "PauseType");

            migrationBuilder.AlterColumn<bool>(
                name: "Active",
                schema: "Users",
                table: "PauseType",
                type: "bit",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldDefaultValue: true);

            migrationBuilder.InsertData(
                schema: "Users",
                table: "PauseType",
                columns: new[] { "Id", "Active", "MaxDuration", "Name" },
                values: new object[,]
                {
                    { new Guid("71dcde3c-225a-4c9d-85f1-bf6d9a9c42b6"), true, 60, "Öğle Yemeği" },
                    { new Guid("82a5b1c4-3f3b-4c7d-96e1-df7a0c9d48c8"), true, 15, "Kahve Molası" },
                    { new Guid("93b6c2d5-4f4c-5d8e-a7f2-ef8b0da59d9d"), true, 120, "Toplantı" },
                    { new Guid("a4c7d3e6-5e5d-6f9f-b8f3-f9c1e1f6ae0e"), true, 240, "Eğitim" }
                });
        }
    }
}
