﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData013 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("4e9488aa-ea89-471d-9c3c-e1888df0192a"),
                column: "NormalizedName",
                value: "AGENT");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5"),
                columns: new[] { "ConcurrencyStamp", "NormalizedName" },
                values: new object[] { "7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5", "USER" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("4e9488aa-ea89-471d-9c3c-e1888df0192a"),
                column: "NormalizedName",
                value: "Agent");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5"),
                columns: new[] { "ConcurrencyStamp", "NormalizedName" },
                values: new object[] { "cdb7ce66-3bfa-4f57-916c-199b4d1e60aa", "User" });
        }
    }
}
