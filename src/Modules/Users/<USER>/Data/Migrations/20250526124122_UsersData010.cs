﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData010 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Icon",
                schema: "Users",
                table: "Permission",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Url",
                schema: "Users",
                table: "Permission",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1ae83117-1a56-45c8-a80c-51634fea7a70"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1d76f6a9-5d91-4d78-92b9-231e5c3c7b67"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("24d7e4f0-c446-486b-b45f-9d2e02012a3b"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3b387098-cdf4-4a51-888e-1f4c8cb78187"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3ce23abe-e9d0-4a4a-9af3-961e0c35c07d"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("41142ba2-98f6-4fc4-8cf0-a01c48ae3f68"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4d0176c7-fc7c-47b1-94a0-3e29a6ebd4b4"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("58e2bf4c-b6a7-4acf-8c98-65214e35492f"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5eba7ab2-3157-4f1a-a33c-e4d9425c5c0b"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ebc3fa3-4705-41e0-a9b4-0fa92e717a21"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("68a6c7c5-213e-48b0-b85f-4d6f59a9d294"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("78f1e171-e9f5-4daa-b5c8-31e42b7c3d0d"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7f39ce9d-918c-4caa-bc3d-056da79f9320"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8bc4c493-7707-43f5-b32d-01a4e162192a"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("93a7df0c-1d63-4b40-89c8-dc25b83cb438"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9bb53de9-8256-4be8-b91e-fd7913232fbc"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9e5c0144-0d94-4d69-9893-39ef56fbd3fa"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a0928d14-d790-447b-8a2a-4f3bcd8660dd"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a5cc8622-e79d-4d76-9842-017f364d4d67"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("adc6418a-fa24-4918-b308-bb8765a31dbd"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c1dfca5c-6d4a-4d13-9a63-5524b537ef5c"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c3ef5138-a30a-4ce7-9eb6-b7363fd56821"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c70c8ae2-c58e-4e21-8f7a-fd6e077be9a9"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c9f2f786-9e0a-4e37-b18a-6b17e4e3d5c4"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cff5e347-2696-48a8-96f6-42f32b644233"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("dc42c9df-8541-4636-a55c-628b0910bf97"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("df0e33eb-a47c-4a30-bc66-d9e892b82355"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("df6d8a28-1154-4eda-81f8-4875d44706cf"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e3be97f7-d78c-4d7a-aee7-d58c1db10b0d"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("eea6af3b-c9a0-4c26-a587-31f4b0dc6686"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f3c93e7b-2b1b-48a4-a0dd-e8bb4fe49e89"),
                columns: new[] { "Icon", "Url" },
                values: new object[] { null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Icon",
                schema: "Users",
                table: "Permission");

            migrationBuilder.DropColumn(
                name: "Url",
                schema: "Users",
                table: "Permission");
        }
    }
}
