﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Users.Infrastructure.Data;

#nullable disable

namespace Users.Infrastructure.Data.Migrations
{
    [DbContext(typeof(UserDbContext))]
    [Migration("20250526124122_UsersData010")]
    partial class UsersData010
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("Users")
                .HasAnnotation("ProductVersion", "9.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Users.Domain.Account.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("TopDepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TopDepartmentId");

                    b.ToTable("Department", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("Role", "Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("cdb7ce66-3bfa-4f57-916c-199b4d1e60aa"),
                            ConcurrencyStamp = "cdb7ce66-3bfa-4f57-916c-199b4d1e60aa",
                            Name = "Admin",
                            NormalizedName = "ADMIN"
                        },
                        new
                        {
                            Id = new Guid("7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5"),
                            ConcurrencyStamp = "cdb7ce66-3bfa-4f57-916c-199b4d1e60aa",
                            Name = "User",
                            NormalizedName = "User"
                        });
                });

            modelBuilder.Entity("Users.Domain.Account.RoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("RoleClaim", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("AttributeData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChatURL")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("PhonePrefix")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("SecurityStamp")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Surname")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ThreeCXEnabled")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ThreeCXExtension")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ThreeCXExternal")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ThreeCXRecording")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex("ThreeCXExtension")
                        .IsUnique();

                    b.ToTable("User", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.UserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserClaim", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.UserDepartment", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("UserDepartment", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.UserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("UserLogin", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRole", "Users");
                });

            modelBuilder.Entity("Users.Domain.Account.UserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Value")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserToken", "Users");
                });

            modelBuilder.Entity("Users.Domain.Auth.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Icon")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("IsMenu")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("TopPermissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Url")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.HasIndex("TopPermissionId");

                    b.ToTable("Permission", "Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("3b387098-cdf4-4a51-888e-1f4c8cb78187"),
                            IsMenu = false,
                            Key = "Users.Management",
                            Name = "Kullanıcı Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("1ae83117-1a56-45c8-a80c-51634fea7a70"),
                            IsMenu = false,
                            Key = "Users.Departments",
                            Name = "Departman Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("adc6418a-fa24-4918-b308-bb8765a31dbd"),
                            IsMenu = false,
                            Key = "Users.Roles",
                            Name = "Rol Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("1d76f6a9-5d91-4d78-92b9-231e5c3c7b67"),
                            IsMenu = false,
                            Key = "Users.Permissions",
                            Name = "İzin Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("24d7e4f0-c446-486b-b45f-9d2e02012a3b"),
                            IsMenu = false,
                            Key = "Users.Pauses",
                            Name = "Mola Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("a5cc8622-e79d-4d76-9842-017f364d4d67"),
                            IsMenu = false,
                            Key = "Customers.Management",
                            Name = "Müşteri Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("78f1e171-e9f5-4daa-b5c8-31e42b7c3d0d"),
                            IsMenu = false,
                            Key = "Customers.Addresses",
                            Name = "Müşteri Adres Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("df0e33eb-a47c-4a30-bc66-d9e892b82355"),
                            IsMenu = false,
                            Key = "Customers.Classifications",
                            Name = "Müşteri Sınıflandırma Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("5eba7ab2-3157-4f1a-a33c-e4d9425c5c0b"),
                            IsMenu = false,
                            Key = "Customers.Contacts",
                            Name = "Müşteri İletişim Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("3ce23abe-e9d0-4a4a-9af3-961e0c35c07d"),
                            IsMenu = false,
                            Key = "Customers.Sources",
                            Name = "Müşteri Kaynağı Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("c3ef5138-a30a-4ce7-9eb6-b7363fd56821"),
                            IsMenu = false,
                            Key = "Customers.Professions",
                            Name = "Müşteri Mesleği Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("eea6af3b-c9a0-4c26-a587-31f4b0dc6686"),
                            IsMenu = false,
                            Key = "Customers.Sectors",
                            Name = "Müşteri Sektör Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("68a6c7c5-213e-48b0-b85f-4d6f59a9d294"),
                            IsMenu = false,
                            Key = "Conversations.Autodialers",
                            Name = "Otomatik Çağrı Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("41142ba2-98f6-4fc4-8cf0-a01c48ae3f68"),
                            IsMenu = false,
                            Key = "Conversations.Calls",
                            Name = "Çağrı Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("9e5c0144-0d94-4d69-9893-39ef56fbd3fa"),
                            IsMenu = false,
                            Key = "Conversations.Chats",
                            Name = "Sohbet Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("9bb53de9-8256-4be8-b91e-fd7913232fbc"),
                            IsMenu = false,
                            Key = "Conversations.CallNotes",
                            Name = "Çağrı Notları Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("e3be97f7-d78c-4d7a-aee7-d58c1db10b0d"),
                            IsMenu = false,
                            Key = "Requests.Tickets",
                            Name = "Ticket Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("7f39ce9d-918c-4caa-bc3d-056da79f9320"),
                            IsMenu = false,
                            Key = "Requests.TicketComments",
                            Name = "Ticket Yorum Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("dc42c9df-8541-4636-a55c-628b0910bf97"),
                            IsMenu = false,
                            Key = "Requests.TicketSubjects",
                            Name = "Ticket Subject Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("4d0176c7-fc7c-47b1-94a0-3e29a6ebd4b4"),
                            IsMenu = false,
                            Key = "Tasks.Management",
                            Name = "Görev Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("c70c8ae2-c58e-4e21-8f7a-fd6e077be9a9"),
                            IsMenu = false,
                            Key = "Tasks.Comments",
                            Name = "Görev Yorum Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("8bc4c493-7707-43f5-b32d-01a4e162192a"),
                            IsMenu = false,
                            Key = "Tasks.Reports",
                            Name = "Görev Raporlama"
                        },
                        new
                        {
                            Id = new Guid("93a7df0c-1d63-4b40-89c8-dc25b83cb438"),
                            IsMenu = false,
                            Key = "DynamicForms.Management",
                            Name = "Dinamik Form Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("c1dfca5c-6d4a-4d13-9a63-5524b537ef5c"),
                            IsMenu = false,
                            Key = "DynamicForms.Attributes",
                            Name = "Dinamik Form Alan Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("58e2bf4c-b6a7-4acf-8c98-65214e35492f"),
                            IsMenu = false,
                            Key = "DynamicForms.Records",
                            Name = "Dinamik Form Veri Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("df6d8a28-1154-4eda-81f8-4875d44706cf"),
                            IsMenu = false,
                            Key = "General.Cities",
                            Name = "Şehir/İlçe Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("5ebc3fa3-4705-41e0-a9b4-0fa92e717a21"),
                            IsMenu = false,
                            Key = "General.Countries",
                            Name = "Ülke Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("cff5e347-2696-48a8-96f6-42f32b644233"),
                            IsMenu = false,
                            Key = "General.States",
                            Name = "Eyalet/İl Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("f3c93e7b-2b1b-48a4-a0dd-e8bb4fe49e89"),
                            IsMenu = false,
                            Key = "General.Files",
                            Name = "Dosya Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("c9f2f786-9e0a-4e37-b18a-6b17e4e3d5c4"),
                            IsMenu = false,
                            Key = "General.Folders",
                            Name = "Klasör Yönetimi"
                        },
                        new
                        {
                            Id = new Guid("a0928d14-d790-447b-8a2a-4f3bcd8660dd"),
                            IsMenu = false,
                            Key = "General.Language",
                            Name = "Dil Yönetimi"
                        });
                });

            modelBuilder.Entity("Users.Domain.Auth.PermissionRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.HasIndex("PermissionId", "RoleId")
                        .IsUnique()
                        .HasFilter("[RoleId] IS NOT NULL");

                    b.HasIndex("PermissionId", "UserId")
                        .IsUnique()
                        .HasFilter("[UserId] IS NOT NULL");

                    b.ToTable("PermissionRule", "Users", t =>
                        {
                            t.HasCheckConstraint("CK_PageRule_UserIdOrRoleId", "([UserId] IS NULL AND [RoleId] IS NOT NULL) OR ([UserId] IS NOT NULL AND [RoleId] IS NULL)");
                        });
                });

            modelBuilder.Entity("Users.Domain.Notifications.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime?>("ReadDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Notification", "Users");
                });

            modelBuilder.Entity("Users.Domain.Pauses.Pause", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActualStartTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("AdministratorDescription")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int>("Duration")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PersonalDescription")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TypeId");

                    b.HasIndex("UserId");

                    b.ToTable("Pause", "Users");
                });

            modelBuilder.Entity("Users.Domain.Pauses.PauseType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("MaxDuration")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("PauseType", "Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("71dcde3c-225a-4c9d-85f1-bf6d9a9c42b6"),
                            Active = true,
                            MaxDuration = 60,
                            Name = "Öğle Yemeği"
                        },
                        new
                        {
                            Id = new Guid("82a5b1c4-3f3b-4c7d-96e1-df7a0c9d48c8"),
                            Active = true,
                            MaxDuration = 15,
                            Name = "Kahve Molası"
                        },
                        new
                        {
                            Id = new Guid("93b6c2d5-4f4c-5d8e-a7f2-ef8b0da59d9d"),
                            Active = true,
                            MaxDuration = 120,
                            Name = "Toplantı"
                        },
                        new
                        {
                            Id = new Guid("a4c7d3e6-5e5d-6f9f-b8f3-f9c1e1f6ae0e"),
                            Active = true,
                            MaxDuration = 240,
                            Name = "Eğitim"
                        });
                });

            modelBuilder.Entity("Users.Domain.Account.Department", b =>
                {
                    b.HasOne("Users.Domain.Account.Department", "TopDepartment")
                        .WithMany("SubDepartment")
                        .HasForeignKey("TopDepartmentId");

                    b.Navigation("TopDepartment");
                });

            modelBuilder.Entity("Users.Domain.Account.RoleClaim", b =>
                {
                    b.HasOne("Users.Domain.Account.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Users.Domain.Account.UserClaim", b =>
                {
                    b.HasOne("Users.Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Users.Domain.Account.UserDepartment", b =>
                {
                    b.HasOne("Users.Domain.Account.Department", "Department")
                        .WithMany("UserDepartment")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Users.Domain.Account.User", "User")
                        .WithMany("UserDepartment")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Users.Domain.Account.UserLogin", b =>
                {
                    b.HasOne("Users.Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Users.Domain.Account.UserRole", b =>
                {
                    b.HasOne("Users.Domain.Account.Role", "Role")
                        .WithMany("UserRole")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Users.Domain.Account.User", "User")
                        .WithMany("UserRole")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Users.Domain.Account.UserToken", b =>
                {
                    b.HasOne("Users.Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Users.Domain.Auth.Permission", b =>
                {
                    b.HasOne("Users.Domain.Auth.Permission", "TopPermission")
                        .WithMany()
                        .HasForeignKey("TopPermissionId");

                    b.Navigation("TopPermission");
                });

            modelBuilder.Entity("Users.Domain.Auth.PermissionRule", b =>
                {
                    b.HasOne("Users.Domain.Auth.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Users.Domain.Account.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId");

                    b.HasOne("Users.Domain.Account.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Permission");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Users.Domain.Notifications.Notification", b =>
                {
                    b.HasOne("Users.Domain.Account.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Users.Domain.Pauses.Pause", b =>
                {
                    b.HasOne("Users.Domain.Pauses.PauseType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Users.Domain.Account.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Type");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Users.Domain.Account.Department", b =>
                {
                    b.Navigation("SubDepartment");

                    b.Navigation("UserDepartment");
                });

            modelBuilder.Entity("Users.Domain.Account.Role", b =>
                {
                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Users.Domain.Account.User", b =>
                {
                    b.Navigation("UserDepartment");

                    b.Navigation("UserRole");
                });
#pragma warning restore 612, 618
        }
    }
}
