﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData008 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "History",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.DropColumn(
                name: "InsertUserId",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.DropColumn(
                name: "UpdateDate",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.DropColumn(
                name: "UpdateUserId",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                schema: "Users",
                table: "PermissionRule",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddPrimaryKey(
                name: "PK_PermissionRule",
                schema: "Users",
                table: "PermissionRule",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_PermissionRule",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.DropColumn(
                name: "Id",
                schema: "Users",
                table: "PermissionRule");

            migrationBuilder.AddColumn<string>(
                name: "History",
                schema: "Users",
                table: "PermissionRule",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "Users",
                table: "PermissionRule",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "InsertUserId",
                schema: "Users",
                table: "PermissionRule",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdateDate",
                schema: "Users",
                table: "PermissionRule",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UpdateUserId",
                schema: "Users",
                table: "PermissionRule",
                type: "uniqueidentifier",
                nullable: true);
        }
    }
}
