﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData012 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAssignable",
                schema: "Users",
                table: "Role",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsBase",
                schema: "Users",
                table: "Role",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5"),
                columns: new[] { "IsAssignable", "IsBase" },
                values: new object[] { false, true });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("cdb7ce66-3bfa-4f57-916c-199b4d1e60aa"),
                columns: new[] { "IsAssignable", "IsBase" },
                values: new object[] { false, true });

            migrationBuilder.InsertData(
                schema: "Users",
                table: "Role",
                columns: new[] { "Id", "ConcurrencyStamp", "IsAssignable", "IsBase", "Name", "NormalizedName" },
                values: new object[,]
                {
                    { new Guid("4e9488aa-ea89-471d-9c3c-e1888df0192a"), "4e9488aa-ea89-471d-9c3c-e1888df0192a", true, true, "Call Center", "Agent" },
                    { new Guid("ff44197b-4f31-441d-a35f-a0314ad30234"), "ff44197b-4f31-441d-a35f-a0314ad30234", true, true, "Departmen Görevlisi", "LEADER" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("4e9488aa-ea89-471d-9c3c-e1888df0192a"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("ff44197b-4f31-441d-a35f-a0314ad30234"));

            migrationBuilder.DropColumn(
                name: "IsAssignable",
                schema: "Users",
                table: "Role");

            migrationBuilder.DropColumn(
                name: "IsBase",
                schema: "Users",
                table: "Role");
        }
    }
}
