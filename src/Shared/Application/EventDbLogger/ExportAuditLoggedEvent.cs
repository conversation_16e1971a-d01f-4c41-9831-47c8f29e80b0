
using MediatR;
using Shared.Domain;

namespace Shared.Application.EventDbLogger;

public class ExportAuditLoggedEvent : IDomainEvent
{
    public Guid UserId { get; }
    public string Module { get; }
    public int ExportedCount { get; }
    public bool FilterUsed { get; }
    public int? SelectedCount { get; }
    public string? FileName { get; }
    public object? Filter { get; }
    public string? FunctionName { get; } = null;

    public DateTime OccurredOn => DateTime.Now;

    public Guid EventId => throw new NotImplementedException();

    public ExportAuditLoggedEvent(
        Guid userId,
        string module,
        int exportedCount,
        bool filterUsed,
        int? selectedCount,
        string? fileName,
        object? filter,
        string? functionName = null)
    {
        UserId = userId;
        Module = module;
        ExportedCount = exportedCount;
        FilterUsed = filterUsed;
        SelectedCount = selectedCount;
        FileName = fileName;
        Filter = filter;
        FunctionName = functionName;
    }
}
