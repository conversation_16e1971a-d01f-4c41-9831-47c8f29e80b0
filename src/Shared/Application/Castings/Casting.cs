

namespace Shared.Application.Castings;
public static class CastingHelper
{
    public static object? TryConvert(object? value, Type targetType, out bool success)
    {
        success = true;

        if (value == null || value is DBNull)
        {
            return targetType.IsValueType && Nullable.GetUnderlyingType(targetType) == null
                ? Activator.CreateInstance(targetType)
                : null;
        }

        var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

        try
        {
            if (underlyingType == typeof(bool) && value is string s)
            {
                s = s.Trim();
                if (s == "1")
                    return true;
                if (s == "0")
                    return false;
                if (string.IsNullOrWhiteSpace(s))
                    return null;
                if (bool.TryParse(s, out var parsedBool))
                    return parsedBool;
                success = false;
                return null;
            }

            if (underlyingType.IsEnum)
            {
                if (value is string enumStr && Enum.TryParse(underlyingType, enumStr, ignoreCase: true, out var enumParsed))
                    return enumParsed;

                if (int.TryParse(value.ToString(), out var enumInt))
                    return Enum.ToObject(underlyingType, enumInt);

                success = false;
                return null;
            }

            if (underlyingType == typeof(Guid))
            {
                if (Guid.TryParse(value.ToString(), out var guid))
                    return guid;
                success = false;
                return null;
            }

            if (underlyingType == typeof(DateTime))
            {
                if (DateTime.TryParse(value.ToString(), out var dt))
                    return dt;
                success = false;
                return null;
            }

            if (underlyingType == typeof(int))
            {
                if (int.TryParse(value.ToString(), out var i))
                    return i;
                success = false;
                return null;
            }

            if (underlyingType == typeof(double))
            {
                if (double.TryParse(value.ToString(), out var d))
                    return d;
                success = false;
                return null;
            }

            // Default: ChangeType
            return Convert.ChangeType(value, underlyingType);
        }
        catch
        {
            success = false;
            return null;
        }
    }
}
