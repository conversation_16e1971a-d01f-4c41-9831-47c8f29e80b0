export const determineTicketNotificationWay = (notificationWay:number,t:any)=>{
    switch(notificationWay)
        {
          case 1:
            return t("ticket.list.email")
           case 2:
            return "SMS"
         
            case 3:
            return  t("ticket.list.all")
         case 4:
            return  t("ticket.list.notificationWayNone")
            default:
              return""
        }

}

export const determineTicketPriority = (priority:number,t:any)=>{
    switch(priority)
        {
          case 1:
            return t("dashboard.low")
           case 2:
            return  t("dashboard.medium")
           case 3:
            return t("dashboard.high")
            case 4:
            return t("dashboard.critical")
            default:
              return""
        
        }

}