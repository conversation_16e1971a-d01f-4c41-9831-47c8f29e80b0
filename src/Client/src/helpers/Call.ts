export const determineCallStatus = (type:"color" |"value"|"select",statusId:number,t:any)=>{
    if(type==="color")
    {
     
        switch(statusId)
        {
          case 0:
           
            return "#58666e"
           case 1:
            return "#ff9a0b"
           case 2:
            return "#ff9a0b"
        case 3:
            return "#e05b4a"
            case 4:
            return "#35b214"
       
          
        }
    }
    else if(type==="value"){
        switch(statusId)
        {
          case 0:
            return t("customers.add.noneCallStatus")
           case 1:
            return  t("customers.add.talking")
           case 2:
            return t("customers.add.answered")
         case 3:
            return t("customers.add.missed")
         case 4:
            return t("customers.add.answered")
          
        }
    }
    else if(type==="select")
    {
        return [
            {label:t("customers.add.noneCallStatus"),value:0},
            {label:t("customers.add.talking"),value:1},
            // {label:t("customers.add.answered"),value:2},
            {label:t("customers.add.missed"),value:3},
            {label:t("customers.add.answered"),value:4},
        ]
    }

}