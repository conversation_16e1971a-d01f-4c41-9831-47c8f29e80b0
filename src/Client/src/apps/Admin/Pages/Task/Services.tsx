import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers  from '@/services/Headers.json';
import { deleteRequest, get, post, put, } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";


export const getTaksListFilter = async (filter:any): Promise<DataResponse<any>> => {
  const query = filter?CreateUrlFilter(filter):null
  const url = `${endpoints.getTaksListFilter}?${query||""}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url,config);
};

export const getAllTaskStatus = async (): Promise<DataResponse<any>> => {
 
  const url = `${endpoints.getAllTaskStatus}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url,config);
};

export const createTask = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createTask}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateTaskWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateTaskWithUrl}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteTask = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteTask}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};





