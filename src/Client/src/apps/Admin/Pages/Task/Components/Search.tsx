import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetTaskFilter } from "../ClientSideStates";



const Search = () => {
  const { filter } = useSelector((state: RootState) => state.ticket);


  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetTaskFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
