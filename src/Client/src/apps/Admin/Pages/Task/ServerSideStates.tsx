import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getAllTaskStatus, getTaksListFilter } from "./Services";




export const useGetTasks= (filter:any) => {
  const query = useQuery(
    [endpoints.getTaksListFilter,filter],
    () => {
      return getTaksListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetTasksStatus= () => {
  const query = useQuery(
    [endpoints.getTaksListFilter],
    () => {
      return getAllTaskStatus();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
