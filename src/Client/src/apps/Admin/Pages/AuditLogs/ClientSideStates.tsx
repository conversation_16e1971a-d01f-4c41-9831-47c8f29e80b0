import { createSlice } from "@reduxjs/toolkit";

const InitialState: {filter:any,} = {
 
  filter: {
    PageNumber: 1,
    PageSize: 30,
   

  },
};

const auditLogSlice = createSlice({
  name: "AuditLogSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetAuditLogFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
   
  
    handleResetAllFieldsAuditLog: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterAuditLog: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 30,
      }
      },
  },
});

export const { handleResetAllFieldsAuditLog,handleResetFilterAuditLog,hanldleSetAuditLogFilter } = auditLogSlice.actions;
export default auditLogSlice;
