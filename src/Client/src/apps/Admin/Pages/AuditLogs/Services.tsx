import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { get } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getAuditLogListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getAuditLogListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
