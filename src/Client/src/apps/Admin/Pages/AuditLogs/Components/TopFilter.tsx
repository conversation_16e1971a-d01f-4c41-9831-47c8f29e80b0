import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import dayjs from "dayjs";
import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import { hanldleSetAuditLogFilter } from "../ClientSideStates";
import GeneralSearch from "@/apps/Common/GeneralSearch";

const TopFilters = () => {
  const { t } = useTranslation();
  const { filter } = useSelector((state: RootState) => state.auditLog);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const handleRangeDateTime = (value: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    const currentFilters = { ...filter };

    if (value && value[0] && value[1]) {
      currentFilters["StartDate"] = value[0].format("YYYY-MM-DD");
      currentFilters["EndDate"] = value[1].format("YYYY-MM-DD");
    } else {
      delete currentFilters["StartDate"];
      delete currentFilters["EndDate"];
    }

    dispatch(hanldleSetAuditLogFilter({ filter: currentFilters }));
  };

  //   const handlePausesStatus = (value: number) => {
  //     const currentFilters = { ...filter };

  //     if (value && value !== 7) {
  //       currentFilters["Status"] = value;
  //     } else {
  //       delete currentFilters["Status"];
  //     }
  //     dispatch(hanldleSetPauseFilter({ filter: currentFilters }));
  //   };

  //   const handlePausesType = (value: string) => {
  //     const currentFilters = { ...filter };
  //     if (value) {
  //       currentFilters["TypeId"] = value;
  //     } else {
  //       delete currentFilters["TypeId"];
  //     }
  //     dispatch(hanldleSetPauseFilter({ filter: currentFilters }));
  //   };

  const handleChangeUser = (value: string) => {
    const currentFilters = { ...filter };
    if (value) {
      currentFilters["UserId"] = value;
    } else {
      delete currentFilters["UserId"];
    }
    dispatch(hanldleSetAuditLogFilter({ filter: currentFilters }));
  };
  useEffect(() => {
     const startDate = filter?.StartDate;
        const endDate = filter?.EndDate;
        const dateRange = [];
        if (startDate) {
          dateRange.push(dayjs(startDate));
        }
        if (endDate) {
          dateRange.push(dayjs(endDate));
        }
    form.setFieldsValue({
      FunctionName: filter?.FunctionName,
      Module: filter?.Module,
      UserId: filter?.UserId,
      Date:dateRange
       
    });
  }, [filter]);

  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <MazakaRangePicker
            xs={24}
            xl={4}
          
            onChange={handleRangeDateTime}
            name="Date"
          />

          <GeneralUsers
            placeholder={t("users.users")}
            name="UserId"
            xs={24}
            xl={4}
            onChange={handleChangeUser}
            allowClear
          />
          <Col xs={24} xl={4}>
            <GeneralSearch
              filter={filter}
              searchFieldName="FunctionName"
              placeholder={t("auditLog.functionName")}
              filterActionFunc={hanldleSetAuditLogFilter}
              filterKey="filter"
              isShowSearchIcon={false}
              isShowBorder={true}
            />
          </Col>
          <Col xs={24} xl={4}>
            <GeneralSearch
              filter={filter}
              searchFieldName="Module"
              placeholder={t("auditLog.module")}
              filterActionFunc={hanldleSetAuditLogFilter}
              filterKey="filter"
              isShowSearchIcon={false}
              isShowBorder={true}
            />
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default TopFilters;
