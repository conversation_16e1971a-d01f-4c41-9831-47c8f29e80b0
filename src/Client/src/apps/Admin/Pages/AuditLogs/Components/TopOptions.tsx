import { Col, Divide<PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import TopFilters from "./TopFilter";

const TopOptions = () => {

  const {t} = useTranslation()
  return (
    <>
      <Row>
        <>
          <Col xs={24} >
            <TopFilters/>
          </Col>
          <Col xs={24}>
            <Divider className="!m-0 !text-gray-400" />
          </Col>
        </>

        
      </Row>
    
    </>
  );
};

export default TopOptions;
