

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { useGetAuditLogs } from "../ServerSideStates";
import { hanldleSetAuditLogFilter } from "../ClientSideStates";
import { Table, Tag, Typography } from "antd";
import dayjs from "dayjs";



const ListItems = () => {
  const {Text} = Typography
  const {filter} = useSelector((state:RootState)=>state.auditLog)
  const logs= useGetAuditLogs(filter);
  const dispatch = useDispatch()
  const {t} = useTranslation()

  const columns = [
    {
      title: t("users.add.name"),
      dataIndex: "UserName",
      key: "UsrName",
   
      sorter: (a: any, b: any) => a?.UserName.localeCompare(b?.UserName),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
    {
        title: t("auditLog.module"),
        dataIndex: "Module",
        key: "Module",
      
        sorter: (a: any, b: any) => a?.Module.localeCompare(b?.Module),
        render:(value:string)=>{
          return(
            <>
            <Text className="!text-xs" >{value}</Text>
            </>
          )
        },
      },
  
      {
        title: t("auditLog.functionName"),
        dataIndex: "FunctionName",
        key: "FunctionName",
      
        sorter: (a: any, b: any) => a?.FunctionName.localeCompare(b?.FunctionName),
        render:(value:string)=>{
          return(
            <>
            {
              value&&
            <Tag color="#0096d1" className="!text-xs" >{value}</Tag>
            }
            </>
          )
        },
      },

      {
        title: t("auditLog.selectedCount"),
        dataIndex: "SelectedCount",
        key: "SelectedCount",
      
        sorter: (a: any, b: any) => a?.SelectedCount.localeCompare(b?.SelectedCount),
        render:(value:string)=>{
          return(
            <>
            <Text className="!text-xs" >{value}</Text>
            </>
          )
        },
      },
      {
        title: t("auditLog.exportedCount"),
        dataIndex: "ExportedCount",
        key: "ExportedCount",
      
        sorter: (a: any, b: any) => a?.ExportedCount.localeCompare(b?.ExportedCount),
        render:(value:string)=>{
          return(
            <>
            <Text className="!text-xs" >{value}</Text>
            </>
          )
        },
      },
       {
            title: t("ticket.list.insertDate"),
            dataIndex: "InsertDate",
            key: "InsertDate",
      
            render: (value: number) => {
              return (
                <>
                {
                  value&&
                  <Text className="!text-xs">
                    {dayjs(value).format("YYYY-DD-MM HH:mm")}
                  </Text>
                }
                </>
              );
            },
          },

  
   
  ];

 

 
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetAuditLogFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={logs?.data?.Value}
        loading={logs.isLoading||logs.isFetching}
       
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: logs.data?.FilteredCount || 0,
            current: logs.data?.PageNumber,
            pageSize: logs.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        rowKey={"Id"}
      />
     
      
    </>
  );
};

export default ListItems;
