import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getAuditLogListFilter } from "./Services";





export const useGetAuditLogs = (filter?: any) => {
  const query = useQuery(
    [endpoints. getAuditLogListFilter, filter],
    () => {
      return getAuditLogListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



