import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getPauseTypeListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getPauseTypeListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getPauseTypeDetails = async (pauseTypeId:string): Promise<DataResponse<any>> => {
  const url = `${endpoints.createPauseType}/${pauseTypeId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createPauseType = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createPauseType}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updatePauseTypeWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updatePauseTypeWithUrl}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deletePauseType = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deletePauseType}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
