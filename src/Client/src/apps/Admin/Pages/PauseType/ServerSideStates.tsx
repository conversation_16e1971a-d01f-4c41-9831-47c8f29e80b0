import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getPauseTypeListFilter } from "./Services";





export const useGetPauseTypes = (filter?: any) => {
  const query = useQuery(
    [endpoints. getPauseTypeListFilter, filter],
    () => {
      return getPauseTypeListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



