import { Col, Row } from "antd";
import Title from "./Components/Title";
import TopOptions from "./Components/TopOptions";
import ListItems from "./Components/ListItem";
import { useQueryClient } from "react-query";
import { useEffect } from "react";
import endPoints from "./EndPoints"





const PauseTypeIndex = () => {
      const queryClient = useQueryClient()
            useEffect(()=>{
                queryClient.resetQueries({
                  queryKey: endPoints.getPauseTypeListFilter,
                  exact: false,
                });
              },[])
   
    return ( <>
     <Col xs={24} >
        <Row gutter={[0,0]} >
            <Col xs={24}  >
              <Title/>
            </Col>
            <Col xs={24}  >
               <TopOptions/>
            </Col>
            <Col xs={24} className="!px-2"  >
                <ListItems/>
            </Col>
            
        </Row>
    </Col>
    </> );
}
 
export default PauseTypeIndex;