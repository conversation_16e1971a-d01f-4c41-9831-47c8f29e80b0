import { createSlice } from "@reduxjs/toolkit";

const InitialState: {filter:any,} = {
 
  filter: {
    PageNumber: 1,
    PageSize: 30,
   

  },
};

const PauseTypeSlice = createSlice({
  name: "PauseTypeSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetPauseTypeFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
   
  
    handleResetAllFieldsPauseType: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterPauseType: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 30,
      }
      },
  },
});

export const { handleResetAllFieldsPauseType,handleResetFilterPauseType,hanldleSetPauseTypeFilter } = PauseTypeSlice.actions;
export default PauseTypeSlice;
