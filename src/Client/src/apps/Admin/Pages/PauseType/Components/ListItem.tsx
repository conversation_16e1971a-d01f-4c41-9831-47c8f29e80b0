import {
  DeleteOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer,  Modal, Table, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {  useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useGetPauseTypes } from "../ServerSideStates";
import { deletePauseType } from "../Services";
import { hanldleSetPauseTypeFilter } from "../ClientSideStates";
import AddOrUpdatePauseType from "./AddOrUpdatePauseType";



const ListItems = () => {
  const {Text} = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const {filter} = useSelector((state:RootState)=>state.pauseType)
  const pauseTypes= useGetPauseTypes(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  const {t} = useTranslation()

  const columns = [
    {
      title: t("pauseType.name"),
      dataIndex: "Name",
      key: "Name",
      width:"30%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render:(value:string,record:any)=>{
        return(
          <div className="!flex gap-1">
             <div
              className={`w-[12px] !h-[12px] ${
                record?.Active ? "!bg-[#35b214]" : "!bg-gray-300"
              }`}
            ></div>
          <Text className="!text-xs" >{value}</Text>
          </div>
        )
      },
    },
    {
      title: t("pauseType.allowedMin"),
      dataIndex: "MaxDuration",
      key: "MaxDuration",
      width:"30%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
  
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width:"40%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
        <Tooltip title={t("pauseType.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>

        <Tooltip title={t("pauseType.delete")}>
          <DeleteOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
      </Col>
      ),
    },
  ];

 

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("pauseType.warning"),
      icon: null,
      content: t("pauseType.deleteModalDesc"),
      okText:  t("pauseType.delete"),
      cancelText:  t("pauseType.cancel"),
      onOk: async () => {
        try {
          await deletePauseType(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getPauseTypeListFilter,
            exact: false,
          });
        } catch (error: any) {
         showErrorCatching(error,null,false,t)
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetPauseTypeFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={pauseTypes?.data?.Value}
        loading={pauseTypes.isLoading||pauseTypes.isFetching}
        onRow={(record) => {
          return {
            onClick: async(event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: pauseTypes.data?.FilteredCount || 0,
            current: pauseTypes.data?.PageNumber,
            pageSize: pauseTypes.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("pauseType.editPauseType")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdatePauseType
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
      
    </>
  );
};

export default ListItems;
