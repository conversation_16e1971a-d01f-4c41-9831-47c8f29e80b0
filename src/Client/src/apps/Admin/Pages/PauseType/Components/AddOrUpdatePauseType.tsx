import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { t } from "i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { createPauseType, updatePauseTypeWithPut } from "../Services";
import { MazakaSwitch } from "@/apps/Common/MazakaSwitch";

const AddOrUpdatePauseType: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updatePauseTypeWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createPauseType(formValues);
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getPauseTypeListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{Active:true}}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("pauseType.name")}
            placeholder={t("profession.name")}
            xs={24}
            name="Name"
            rules={[{ required: true, message: "" }]}
          />
          <MazakaInput
            type="Number"
            label={t("pauseType.allowedMin")}
            placeholder={t("pauseType.allowedMin")}
            xs={24}
            name="MaxDuration"
            rules={[{ required: true, message: "" }]}
          />
         <MazakaSwitch
              label={t("users.add.status")}
              xs={24}
             
              name={"Active"}
              checkedChildren={t("users.add.active")}
              unCheckedChildren={t("users.add.pasive")}
              onChange={(status: boolean) => {
                form.setFieldValue("Active", status);
              }}
            />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("pauseType.edit") : t("pauseType.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdatePauseType;
