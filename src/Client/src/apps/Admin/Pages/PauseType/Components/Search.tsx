import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetPauseTypeFilter } from "../ClientSideStates";




const Search = () => {
  const { filter } = useSelector((state: RootState) => state.pauseType);

  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetPauseTypeFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
