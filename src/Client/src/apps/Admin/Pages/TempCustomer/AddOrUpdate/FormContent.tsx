import ImportFileIndex from "@/apps/Import/ImportIndex";
import { saveTempCustomerFile, startImportTempCustomerFile } from "../Services";
import { useQueryClient } from "react-query";
import tempCustoemrEndPoints from "../EndPoints"
import autoDialerEndPoints from "@/apps/Admin/Pages/AutoDialer/EndPoints"
import { FC } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";


const FormContent:FC<{isAutoDialer?:boolean}> = ({isAutoDialer}) => {
  const queryClient = useQueryClient()
  const{t} = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
 
  return (
    <>
    <ImportFileIndex
          type={isAutoDialer?"autoDialer":"tempCustomerList"}
          uploaderTitle={t("customers.import.uploaderFileTitle")}
          uploaderDesc={t("customers.import.uploaderFileDesc")}
          startImportOperationService={startImportTempCustomerFile}
          saveImportedFileService={saveTempCustomerFile}
          onFinish={() => {
            queryClient.resetQueries({
              queryKey: tempCustoemrEndPoints.getTempCustomerListFilter,
              exact: false,
            });
            if(!location.pathname?.includes("dialer"))
            {

              navigate("/panel/import-data")
            }
          
           
          }}
        />
    </>
  );
};

export default FormContent;
