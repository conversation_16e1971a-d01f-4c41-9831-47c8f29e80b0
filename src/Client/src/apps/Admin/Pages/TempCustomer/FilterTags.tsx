import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "../Users/<USER>/GeneralFilterTags";
import { hanldleSetCustomerFilter } from "../Customers/ClientSideStates";


const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.customer)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={2}
    filter={filter}
    actionFunc={hanldleSetCustomerFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize"]}
    />
    </> );
}
 
export default FilterTags;