import { useQuery } from "react-query";
import tempCustomerEndPoints from "./EndPoints";
import customerEndpoints from "@/apps/Admin/Pages/Customers/EndPoints";
import { getTempCustomerDetails, getTempCustomerListFilter } from "./Services";
import { getCustomerDetails } from "../Customers/Services";




export const useGetTempCustomers = (filter: any) => {

  const query = useQuery(
    [tempCustomerEndPoints.getTempCustomerListFilter, filter],
    () => {
      return getTempCustomerListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetTempCustomerDetails = (customerId:number|string|null) => {
 
  const query = useQuery(
    [tempCustomerEndPoints.getTempCustomerDetails,customerId],
    () => {
      return getTempCustomerDetails(customerId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: !!customerId
    }
  );

  return query;
}

export const useGetCustomerDetailsByType = (customerId:number|string|null,type:"customer"|"tempCustomer") => {
 
 
  const query = useQuery(
    [type==="tempCustomer"?tempCustomerEndPoints.getTempCustomerDetails:customerEndpoints.getCustomerDetails,customerId,type],
    () => {
      return type==="tempCustomer"?getTempCustomerDetails(customerId):getCustomerDetails(customerId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: !!type
    }
  );

  return query;
}

