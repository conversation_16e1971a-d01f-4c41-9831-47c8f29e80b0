import PageTitle from "@/apps/Common/PageTitle";
import { Col, Divider, Row } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";

const Title:FC<{title:string}> = () => {
  const {t} = useTranslation()
    return (<>
       <Row gutter={[0, 0]} className="">
      <Col xs={24} className="menu-height">
        <PageTitle title={t("tempCustomer.list.tempCustomers")} count={2} />
      </Col>
      <Col xs={24}>
        <Divider className="!m-0  " />
      </Col>
    </Row>
    </>  );
}
 
export default Title;