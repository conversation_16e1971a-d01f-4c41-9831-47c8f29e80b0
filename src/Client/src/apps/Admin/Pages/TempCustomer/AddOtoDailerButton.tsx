import { MazakaButton } from "@/apps/Common/MazakaButton";
import { commonRoutePrefix } from "@/routes/Prefix";
import { PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const AddOtoDailerButton = () => {
  const navigate = useNavigate();
  const {t} = useTranslation()
  return (
    <>
      <MazakaButton
        status="save"
        icon={<PlusOutlined/>}
        onClick={() => {
          navigate(`${commonRoutePrefix}/add-import-data`);
        }}
      >
     {t("tempCustomer.list.addButton")}
      </MazakaButton>
    </>
  );
};

export default AddOtoDailerButton;
