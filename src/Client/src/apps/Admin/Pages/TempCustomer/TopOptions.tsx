import { <PERSON>, Divider, Drawer, Row } from "antd";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import { useState } from "react";
import DetailsFilter from "../Customers/Components/DetailsFilter";
import { bulkDeleteTempCustomer, exportTempCustomerData } from "./Services";
import GeneralExportButton from "@/apps/Common/GeneralExportButton";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import FilterTags from "./FilterTags";
import MazakaClearFilters from "@/apps/Common/MazakaClearFilters";
import {
  handleResetFilterCustomer,
  handleSetCustomersListSelectedItems,
} from "../Customers/ClientSideStates";
import GeneralDeleteButton from "@/apps/Common/GeneralDeleteButton";
import endPoints from "./EndPoints";
import { useNavigate } from "react-router-dom";
import GeneralFilterButton from "@/apps/Common/GeneralFilterButton";
import { commonRoutePrefix } from "@/routes/Prefix";

const TopOptions = () => {
  const [isShowFilterDrawer, setIsShowFilterDrawer] = useState(false);
  const { filter, customersListSelectedIds } = useSelector(
    (state: RootState) => state.customer
  );
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <>
      <Row>
        <Col xs={24} className="!flex gap-2 !py-1 !px-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              navigate(`${commonRoutePrefix}/add-import-data`);
            }}
            status={customersListSelectedIds.length > 0 ? "error" : "save"}
            disabled={customersListSelectedIds.length > 0}
          >
            {t("tempCustomer.list.addButton")}
          </MazakaButton>
          <GeneralDeleteButton
            selectedIds={customersListSelectedIds}
            descriptions=""
            serviceFunc={bulkDeleteTempCustomer}
            listEndPoint={endPoints.getTempCustomerListFilter}
            title={t("customers.deleteAllButton")}
            actionFunc={handleSetCustomersListSelectedItems}
          />

          <GeneralFilterButton
            selectedIds={customersListSelectedIds}
            onClick={() => {
              setIsShowFilterDrawer(true);
            }}
            title={t("customers.filter.filterButton")}
          />

          <GeneralExportButton
            filter={filter}
            title={t("tempCustomer.list.export")}
            serviceFunk={exportTempCustomerData}
            fileName="temp_customers"
            selectedIds={customersListSelectedIds}
          />
          <>
            {filter && Object.entries(filter).length > 2 && (
              <>
                <MazakaClearFilters resetFunc={handleResetFilterCustomer} />
              </>
            )}
          </>
        </Col>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
        <FilterTags />
      </Row>
      <Drawer
        title="Filter Data"
        open={isShowFilterDrawer}
        onClose={() => {
          setIsShowFilterDrawer(false);
        }}
      >
        <DetailsFilter
          type="tempCustomer"
          onFinish={() => {
            setIsShowFilterDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
