import { createSlice } from "@reduxjs/toolkit";

const InitialState: { filter: any } = {
  filter: {
    PageNumber: 1,
    PageSize: 20,
  },
};

const classificationSlice = createSlice({
  name: "ClassificationSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetClassificationFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },

    handleResetAllFieldsClassification: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterClassification: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 20,
      };
    },
  },
});

export const {
  handleResetAllFieldsClassification,
  handleResetFilterClassification,
  hanldleSetClassificationFilter,
} = classificationSlice.actions;
export default classificationSlice;
