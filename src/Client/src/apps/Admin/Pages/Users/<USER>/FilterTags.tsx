import { useSelector } from "react-redux";
import { hanldleSetUserFilter } from "../ClientSideStates";
import GeneralFilterTags from "./GeneralFilterTags";
import { RootState } from "@/store/Reducers";

const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.users)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={2}
    filter={filter}
    actionFunc={hanldleSetUserFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize"]}
    />
    </> );
}
 
export default FilterTags;