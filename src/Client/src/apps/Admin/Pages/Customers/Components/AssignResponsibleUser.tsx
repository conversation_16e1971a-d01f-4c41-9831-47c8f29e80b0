import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, message, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { FC } from "react";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import { assignResponsibleUser } from "../Services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { handleSetCustomersListSelectedItems } from "../ClientSideStates";

const AssignResponsibleuser: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { customersListSelectedIds } = useSelector(
    (state: RootState) => state.customer
  );
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let advisorIds = formValues["AdvisorIds"];
    const data = {
      CustomerIds: customersListSelectedIds,
      AdvisorIds: advisorIds,
    };
    try {
      await assignResponsibleUser(data);
     
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerListFilter,
        exact: false,
      });
      form.resetFields();
     

      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };
  return (
    <>
      <Col xs={24}>
        <MazakaForm
          submitButtonVisible={false}
          form={form}
          onFinish={handleOnFinish}
        >
          <Row gutter={[10, 10]}>
            <GeneralUsers
              label={t("customers.add.customerRepresentative")}
              placeholder={t("customers.add.customerRepresentative")}
              name="AdvisorIds"
              xs={24}
              rules={[{ required: true, message: "" }]}
              mode="multiple"
            />

            <Col xs={24}>
              <MazakaButton
                processType={formActions.submitProcessType}
                htmlType="submit"
                status="save"
              >
                {t("customers.list.edit")}
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AssignResponsibleuser;
