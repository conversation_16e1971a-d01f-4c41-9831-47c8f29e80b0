import { MazakaButton } from "@/apps/Common/MazakaButton";
import { RootState } from "@/store/Reducers";
import { UserAddOutlined } from "@ant-design/icons";
import { Drawer } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import AssignResponsibleuser from "./AssignResponsibleUser";
import { handleSetCustomersListSelectedItems } from "../ClientSideStates";

const AssignResponsibleUserButton = () => {
  const { t } = useTranslation();
  const [
    isShowAssingResponsibleUserDrawer,
    setIsShowAssignResponsibleUserDrawer,
  ] = useState(false);
  const { customersListSelectedIds } = useSelector(
    (state: RootState) => state.customer
  );
  const dispatch = useDispatch()

  return (
    <>
      <MazakaButton
        className="!text-xs"
        icon={<UserAddOutlined />}
        status={`${customersListSelectedIds?.length > 0 ? "save" : "error"}`}
        disabled={customersListSelectedIds?.length > 0 ? false : true}
        onClick={() => {
          setIsShowAssignResponsibleUserDrawer(true)
        }}
      >
        <span className=" !flex items-center gap-1 ">
          <span className="!text-xs ">
            {t("customers.add.assignCustomerRepresentative")}
          </span>
          {customersListSelectedIds?.length > 0 && (
            <span className="!text-[10px] ">
              ({customersListSelectedIds?.length})
            </span>
          )}
        </span>
      </MazakaButton>

      <Drawer
      title= {t("customers.add.assignCustomerRepresentative")}
        open={isShowAssingResponsibleUserDrawer}
        onClose={() => {
          setIsShowAssignResponsibleUserDrawer(false);
        }}
      >
        <AssignResponsibleuser
          onFinish={() => {
            setIsShowAssignResponsibleUserDrawer(false);
             dispatch(
                    handleSetCustomersListSelectedItems({
                      selectedIds: [],
                      selectedItems: [],
                    })
                  );
          }}
        />
      </Drawer>
    </>
  );
};

export default AssignResponsibleUserButton;
