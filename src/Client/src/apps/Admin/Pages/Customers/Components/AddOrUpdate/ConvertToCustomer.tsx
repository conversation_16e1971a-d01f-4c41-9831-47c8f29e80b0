import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { convertToCustomer } from "../../Services";
import customerEndPoints from "../../EndPoints";
import tempCustomerEndPoints from "@/apps/Admin/Pages/TempCustomer/EndPoints";
import { FC } from "react";
import { Modal, Tooltip } from "antd";
import { UserAddOutlined, UserOutlined } from "@ant-design/icons";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { hanldleSetAutoDialerSocketList } from "../../../AutoDialer/ClientSideStates";
import { useSearchParams } from "react-router-dom";

const ConvertToCustomer: FC<{ showingType: "icon" | "button",pageType:"tempCustomer"|"calling",record:any }> = ({
  showingType,pageType,record
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const {activeSocketList} = useSelector((state:RootState)=>state.autoDialer)
  const dispatch = useDispatch()
  const [searchParams,setSearchParams] =useSearchParams()

  const convertToCustomerConfirm = (record: any) => {
    Modal.confirm({
      title: t("customers.list.warning"),
      icon: null,
      content: "Bu oğe müşteriye dönüşülecektir.Onaylıyor musunuz?",
      okText: "Tamam",
      cancelText: t("customers.list.cancel"),
      onOk: async () => {
        try {
         const response = await convertToCustomer(record?.Id);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: tempCustomerEndPoints.getTempCustomerListFilter,
    
            exact: false,
          });
          queryClient.resetQueries({
            queryKey: customerEndPoints.getCustomerListFilter,
            exact: false,
          });
          if (pageType === "calling") {
            const findSocketItemIndex = activeSocketList?.findIndex((item: any) => item.customerId === record?.Id);
          
            if (findSocketItemIndex !== undefined && findSocketItemIndex !== -1 && activeSocketList) {
              const allSockets = [...activeSocketList];
              const findSocketItem = { ...allSockets[findSocketItemIndex] };
              findSocketItem["customerId"] = response.Value?.Id
              findSocketItem["customer"] =`${response?.Value?.Name||""}  ${response?.Value?.Surname||""}`
              delete findSocketItem.tempCustomerId;
              delete findSocketItem.tempCustomer;
              delete findSocketItem.type;
              allSockets[findSocketItemIndex] = findSocketItem;
              const params:any = {callId:searchParams.get("callId"),customerId:response.Value?.Id}
              setSearchParams(params)
          
              dispatch(hanldleSetAutoDialerSocketList({socketList:allSockets}))
            }
          }
          
          
        } catch (error) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
  return (
    <>
      {showingType === "icon" ? (
        <>
          <Tooltip title={"Müşteriyi Dönüştür"}>
            <UserOutlined
              className=" !text-[#9da3af] !text-sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                convertToCustomerConfirm(record);
              }}
            />
          </Tooltip>
        </>
      ) : (
        <>
        
        <MazakaButton
        icon={<UserAddOutlined/>}
        status="error"
        onClick={()=>{
          convertToCustomerConfirm(record);
        }}
        >
          Müşteriyi Dönüştür
        </MazakaButton>
        </>
      )}
    </>
  );
};

export default ConvertToCustomer;
