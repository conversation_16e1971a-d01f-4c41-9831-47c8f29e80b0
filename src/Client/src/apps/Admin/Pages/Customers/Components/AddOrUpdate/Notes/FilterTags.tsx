import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "@/apps/Admin/Pages/Users/<USER>/GeneralFilterTags";
import { hanldleSetNotesFilter } from "@/apps/Admin/Pages/Notes/ClientSideStates";



const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.notes)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={2}
    filter={filter}
    actionFunc={hanldleSetNotesFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize"]}
    />
    </> );
}
 
export default FilterTags;