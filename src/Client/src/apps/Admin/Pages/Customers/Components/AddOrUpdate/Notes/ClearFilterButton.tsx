import { handleResetFilterNotes } from "@/apps/Admin/Pages/Notes/ClientSideStates";
import MazakaClearFilters from "@/apps/Common/MazakaClearFilters";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";



const ClearFilterButton = () => {
    const {filter} = useSelector((state:RootState)=>state.notes)
    return ( <>
    {filter && Object.entries(filter).length > 3 && (
              <>
                <MazakaClearFilters resetFunc={handleResetFilterNotes} />
              </>
            )}
    </> );
}
 
export default ClearFilterButton;