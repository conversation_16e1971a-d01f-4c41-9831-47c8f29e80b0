import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { hanldleSetNotesFilter } from "@/apps/Admin/Pages/Notes/ClientSideStates";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import GeneralUsers from "@/apps/Common/GenerralUsers";

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter } = useSelector((state: RootState) => state.notes);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    let currentFilter = { ...filter };

    for (let key in formValues) {
      if (
        !formValues[key] ||
        (Array.isArray(formValues[key]) && formValues[key].length === 0)
      ) {
        delete formValues[key];
        delete currentFilter[key];
        if (key == "Date") {
          delete currentFilter["StartDate"];
          delete currentFilter["EndDate"];
        }
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DD");
      delete formValues["Date"];
    }

    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(
      hanldleSetNotesFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };

  useEffect(() => {
    const startDate = filter?.StartDate;
    const endDate = filter?.EndDate;
    const dateRange = [];
    if (startDate) {
      dateRange.push(dayjs(startDate));
    }
    if (endDate) {
      dateRange.push(dayjs(endDate));
    }

    form.setFieldsValue({
      SearchTerm: filter?.SearchTerm,
      Date: dateRange,
      InsertUserId: filter?.InsertUserId,
    });
  }, [filter]);

  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          <GeneralUsers
            label={t("users.users")}
            placeholder={t("users.users")}
            name="InsertUserId"
            xs={24}
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameInsertUserId", obj.label);
            }}
          />
          <MazakaTextArea
            autoSize
            name={"SearchTerm"}
            label={t("ticket.list.description")}
            placeholder={t("ticket.list.description")}
            xs={24}
            className="!m-0"
          />
          <MazakaRangePicker
            name={"Date"}
            label={t("ticket.list.dateRange")}
            xs={24}
            className="!m-0"
          />

          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>
            <Form.Item
            name={"customNameInsertUserId"}
            className="!hidden"
          ></Form.Item>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
