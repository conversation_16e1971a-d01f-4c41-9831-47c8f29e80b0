import { createSlice } from "@reduxjs/toolkit";

const InitialState: {filter:any,} = {
 
  filter: {
    PageNumber: 1,
    PageSize: 20,
   

  },
};

const notesSlice = createSlice({
  name: "NotesSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetNotesFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
   
  
    handleResetAllFieldsNotes: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterNotes: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 20,
      }
      },
  },
});

export const { handleResetAllFieldsNotes,handleResetFilterNotes,hanldleSetNotesFilter } = notesSlice.actions;
export default notesSlice;
