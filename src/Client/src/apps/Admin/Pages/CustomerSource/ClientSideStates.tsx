import { createSlice } from "@reduxjs/toolkit";

const InitialState: { filter: any } = {
  filter: {
    PageNumber: 1,
    PageSize: 30,
  },
};

const customerSourceSlice = createSlice({
  name: "CustomerSourceSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetCustomerSourceFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },

    handleResetAllFieldsCustomerSource: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterCustomerSource: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 30,
      };
    },
  },
});

export const {
  handleResetAllFieldsCustomerSource,
  handleResetFilterCustomerSource,
  hanldleSetCustomerSourceFilter,
} = customerSourceSlice.actions;
export default customerSourceSlice;
