import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getCustomerSourceListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getCustomerSourceListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getCustomerSourceDetails = async (professinoId:string): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerSource}/${professinoId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createCustomerSource = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createCustomerSource}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateCustomerSourceWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateCustomerSourceWithUrl}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteCustomerSource = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteCustomerSource}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
