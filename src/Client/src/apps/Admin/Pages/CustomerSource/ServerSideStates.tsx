import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getCustomerSourceListFilter } from "./Services";




export const useGetCustomerSources = (filter?: any) => {
  const query = useQuery(
    [endpoints. getCustomerSourceListFilter, filter],
    () => {
      return getCustomerSourceListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



