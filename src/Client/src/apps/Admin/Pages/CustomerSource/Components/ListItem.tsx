import {
  DeleteOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer,  Modal, Table, Tooltip, Typography } from "antd";

import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";

import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import {  useGetCustomerSources,  } from "../ServerSideStates";
import {  useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { deleteCustomerSource } from "../Services";
import { hanldleSetCustomerSourceFilter } from "../ClientSideStates";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import AddOrUpdateCustomerSource from "./AddOrUpdateCustomerSource";



const ListItems = () => {
  const {Text} = Typography
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const {filter} = useSelector((state:RootState)=>state.customerSource)
  const CustomerSources= useGetCustomerSources(filter);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  const {t} = useTranslation()

  const columns = [
    {
      title: t("CustomerSource.name"),
      dataIndex: "Name",
      key: "Name",
      width:"30%",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render:(value:string)=>{
        return(
          <>
          <Text className="!text-xs" >{value}</Text>
          </>
        )
      },
    },
  
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width:"70%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
        <Tooltip title={t("users.list.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>

        <Tooltip title={t("users.list.delete")}>
          <DeleteOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
      </Col>
      ),
    },
  ];

 

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("CustomerSource.warning"),
      icon: null,
      content: t("CustomerSource.deleteModalDesc"),
      okText:  t("CustomerSource.delete"),
      cancelText:  t("CustomerSource.cancel"),
      onOk: async () => {
        try {
          await deleteCustomerSource(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getCustomerSourceListFilter,
            exact: false,
          });
        } catch (error: any) {
         showErrorCatching(error,null,false,t)
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCustomerSourceFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={CustomerSources?.data?.Value}
        loading={CustomerSources.isLoading||CustomerSources.isFetching}
        onRow={(record) => {
          return {
            onClick: async(event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: CustomerSources.data?.FilteredCount || 0,
            current: CustomerSources.data?.PageNumber,
            pageSize: CustomerSources.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("CustomerSource.editCustomerSource")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateCustomerSource
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
      
    </>
  );
};

export default ListItems;
