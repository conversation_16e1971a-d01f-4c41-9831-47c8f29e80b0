import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import Search from "./Search";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import AddOrUpdateCustomerSource from "./AddOrUpdateCustomerSource";

const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const {t} = useTranslation()
  return (
    <>
      <Row>
        <>
          <Col xs={24}>
            <Search />
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
          <Col xs={24}>
            <Divider className="!m-0 !text-gray-400" />
          </Col>
        </>

        <div className=" !py-1 !px-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              setIsShowAddDrawer(true);
            }}
          >
            {t("profession.add")}
          </MazakaButton>
        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
      </Row>
      <Drawer
        title={t("profession.addProfession")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateCustomerSource
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
