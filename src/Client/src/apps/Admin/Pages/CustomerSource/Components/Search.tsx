import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetCustomerSourceFilter } from "../ClientSideStates";




const Search = () => {
  const { filter } = useSelector((state: RootState) => state.customerSource);

  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetCustomerSourceFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
