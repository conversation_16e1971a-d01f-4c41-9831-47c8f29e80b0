import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import { createCustomerSource,  updateCustomerSourceWithPut,  } from "../Services";
import endPoints from "../EndPoints"
import { t } from "i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";

const AddOrUpdateCustomerSource: FC<{
  onFinish: () => void;
  selectedRecord?: any;

}> = ({
  onFinish,
  selectedRecord,
 
}) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updateCustomerSourceWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createCustomerSource(formValues);
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getCustomerSourceListFilter,
        exact: false,
      });
    } catch (error) {
   
      showErrorCatching(error,null,false,t)
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("CustomerSource.name")}
            placeholder={t("CustomerSource.name")}
            xs={24}
            name="Name"
            rules={[{ required: true, message: "" }]}
          />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
           {selectedRecord?t("CustomerSource.edit"):t("CustomerSource.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateCustomerSource;
