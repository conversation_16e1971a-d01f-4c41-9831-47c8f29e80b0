import MazakaClearFilters from "@/apps/Common/MazakaClearFilters";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import { handleResetFilterDepartment } from "../ClientSideStates";

const ClearFilterButton = () => {
  const { filter } = useSelector((state: RootState) => state.department);
  return (
    <>
      {filter && Object.entries(filter).length > 1 && (
        <>
          <MazakaClearFilters resetFunc={handleResetFilterDepartment} />
        </>
      )}
    </>
  );
};

export default ClearFilterButton;
