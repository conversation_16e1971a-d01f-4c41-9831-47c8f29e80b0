import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "../../Users/<USER>/GeneralFilterTags";
import { handleSetDepartmentFilter } from "../ClientSideStates";



const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.department)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={1}
    filter={filter}
    actionFunc={handleSetDepartmentFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize","IncludeUsers"]}
    />
    </> );
}
 
export default FilterTags;