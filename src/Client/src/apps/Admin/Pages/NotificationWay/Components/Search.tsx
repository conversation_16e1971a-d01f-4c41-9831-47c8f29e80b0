import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetNotificationWayFilter } from "../ClientSideStates";




const Search = () => {
  const { filter } = useSelector((state: RootState) => state.notificationWay);

  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetNotificationWayFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
