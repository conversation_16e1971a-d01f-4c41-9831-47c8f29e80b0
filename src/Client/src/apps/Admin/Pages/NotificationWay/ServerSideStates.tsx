import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getNotificationWayListFilter } from "./Services";




export const useGetNotificationWays = (filter?: any) => {
  const query = useQuery(
    [endpoints. getNotificationWayListFilter, filter],
    () => {
      return getNotificationWayListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



