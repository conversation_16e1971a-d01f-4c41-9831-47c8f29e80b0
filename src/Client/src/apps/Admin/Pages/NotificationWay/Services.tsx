import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getNotificationWayListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getNotificationWayListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getNotificationWayDetails = async (professinoId:string): Promise<DataResponse<any>> => {
  const url = `${endpoints.createNotificationWay}/${professinoId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createNotificationWay = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createNotificationWay}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateNotificationWayWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateNotificationWayWithUrl}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteNotificationWay = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteNotificationWay}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
