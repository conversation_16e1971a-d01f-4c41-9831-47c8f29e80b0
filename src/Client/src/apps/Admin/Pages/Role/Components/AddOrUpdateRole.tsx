import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import { createRole, updateRoleWithPut } from "../Services";
import endPoints from "../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";

const AddOrUpdateRole: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
const {t} = useTranslation()
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updateRoleWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createRole(formValues);
      }
      openNotificationWithIcon("success",t("form.transactionSuccessful"))
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getAllRoles,
        exact: false,
      });
    } catch (error) {
     showErrorCatching(error,mazakaForm,true,t)
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("roles.name")}
            placeholder={t("roles.name")}
            xs={24}
            name="Name"
            rules={[{ required: true, message: "" }]}
          />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("roles.save") : t("roles.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateRole;
