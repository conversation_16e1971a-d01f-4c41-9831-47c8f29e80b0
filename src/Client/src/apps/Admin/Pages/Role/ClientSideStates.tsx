import { createSlice } from "@reduxjs/toolkit";

const InitialState: { filter: any } = {
  filter: {
    PageNumber: 1,
    PageSize: 30,
    IsAssignable:true,
   
  },
};

const roleSlice = createSlice({
  name: "RoleSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetRoleFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },

    handleResetAllFieldsRole: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterRole: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 30,
      };
    },
  },
});

export const {
  handleResetAllFieldsRole,
  handleResetFilterRole,
  hanldleSetRoleFilter,
} = roleSlice.actions;
export default roleSlice;
