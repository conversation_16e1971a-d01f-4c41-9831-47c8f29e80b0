import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { get3cxCallReportDetails, get3cxCallReportListFilter } from "./Services";

export const useGet3cxRecordings = (filter?: any) => {
  const query = useQuery(
    [endpoints.get3cxCallReportListFilter, filter],
    () => {
      return get3cxCallReportListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGet3cxRecordingDetails = (recordId:string) => {
  const query = useQuery(
    [endpoints.get3cxCallReportDetails, recordId],
    () => {
      return get3cxCallReportDetails(recordId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
