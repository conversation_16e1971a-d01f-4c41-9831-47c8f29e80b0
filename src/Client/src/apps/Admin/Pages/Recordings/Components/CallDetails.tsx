import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Col, Table, Tooltip, Typography } from "antd";
import { useGet3cxRecordingDetails } from "../ServerSideStates";
import dayjs from "dayjs";
import { DownloadOutlined, FormOutlined } from "@ant-design/icons";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import ExpandableText from "@/apps/Common/TruncatedDesc";

const CallDetails: FC<{ recordId: string }> = ({ recordId }) => {
  const { t } = useTranslation();

  const { Text } = Typography;
  const callDetails = useGet3cxRecordingDetails(
   recordId
  );
  const columns = [
    {
      title: t("recording.sourceParticipantName"),
      dataIndex: "SourceParticipantName",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("recording.sourceParticipantPhoneNumber"),
      dataIndex: "SourceParticipantPhoneNumber",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("recording.startTime"),
      dataIndex: "StartTime",
      key: "StartTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.endTime"),
      dataIndex: "EndTime",
      key: "EndTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.summary"),
      dataIndex: "Summary",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("recording.summary")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: t("recording.fullContent"),
      dataIndex: "Transcription",
      render: (value: string, record: any) => {
        return (
          <>
            <ExpandableText
              title={t("recording.fullContent")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex justify-end mr-4">
          <Tooltip title={t("recording.download")}>
            <DownloadOutlined
              className="!text-gray-500"
              onClick={() => {
                const link = record?.RecordingUrl;

                if (link) {
                  window.location.href = link;
                } else {
                  openNotificationWithIcon("error", t("recording.invalidLink"));
                }
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  

  return (
    <>
      <Table
        loading={callDetails.isLoading || callDetails.isFetching}
        dataSource={callDetails?.data?.Value || []}
        columns={columns}
        pagination={false}
      />
    </>
  );
};

export default CallDetails;
