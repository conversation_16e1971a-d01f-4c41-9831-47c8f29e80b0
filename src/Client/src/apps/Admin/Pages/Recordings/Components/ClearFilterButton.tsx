import MazakaClearFilters from "@/apps/Common/MazakaClearFilters";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import { handleResetFilterRecordings } from "../ClientSideStates";



const ClearFilterButton = () => {
    const {filter} = useSelector((state:RootState)=>state.recordings)
    return ( <>
    {filter && Object.entries(filter).length > 4 && (
              <>
                <MazakaClearFilters resetFunc={handleResetFilterRecordings} />
              </>
            )}
    </> );
}
 
export default ClearFilterButton;