import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getTicketCommentListFilter, getTicketListFilter } from "./Services";

export const useGetTickets = (filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketListFilter, filter],
    () => {
      return getTicketListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetTicketComments = ( filter?: any) => {
  const query = useQuery(
    [endpoints.getTicketComments, filter],
    () => {
      return getTicketCommentListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
