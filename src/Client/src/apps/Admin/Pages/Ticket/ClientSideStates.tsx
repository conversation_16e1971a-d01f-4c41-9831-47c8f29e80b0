import { createSlice } from "@reduxjs/toolkit";

const InitialState: { filter: any; pageType: "customerDetails" | "tickets",ticketDetails:null|any } = {
  pageType: "tickets",
 ticketDetails:null,
  filter: {
    PageNumber: 1,
    PageSize: 30,
  },
};

const ticketSlice = createSlice({
  name: "ticketSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetTicketFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },

    hanldleSetTicketDetails: (state, action) => {
      let data = action.payload;
      state.ticketDetails = data.data;
    },

    hanldleSetTicketPageType: (state, action) => {
      let data = action.payload;
      state.pageType = data.type;
    },

    handleResetAllFieldsTicket: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterTicket: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 30,
      };
    },
  },
});

export const {
  handleResetAllFieldsTicket,
  handleResetFilterTicket,
  hanldleSetTicketFilter,
  hanldleSetTicketDetails,
  hanldleSetTicketPageType
} = ticketSlice.actions;

export default ticketSlice;
