import { Col, Row } from "antd";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import TicketCommentIndex from "./Comments/TicketCommentIndex";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const AddOrUpdateIndex = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  console.log(ticketDetails)
  return (
    <>
      <Col xs={24}>
        <Row gutter={[40, 0]}>
          <Col xs={24} md={8}>
            <AddOrUpdateTicket />
          </Col>
          {ticketDetails && (
            <Col xs={24} md={16}>
              <TicketCommentIndex />
            </Col>
          )}
        </Row>
      </Col>
    </>
  );
};

export default AddOrUpdateIndex;
