import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row, Upload } from "antd";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { createTicket, updateTicketWithPut } from "../Services";
import GeneralSubjectTicket from "@/apps/Common/GeneralSubjectTicket";
import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { UploadProps } from "antd/lib";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";
import { hanldleSetTicketDetails } from "../ClientSideStates";

const AddOrUpdateTicket = () => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { Dragger } = Upload;
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { filter, pageType,ticketDetails } = useSelector((state: RootState) => state.ticket);
const dispatch = useDispatch()
  const [fileList, setFileList] = useState<any[]>([]);
  const { t } = useTranslation();

  const beforeUpload = (file: File) => {
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      openNotificationWithIcon("error", `${file.name} 2MB'tan büyük olamaz!`);
    }
    return isLt2M || Upload.LIST_IGNORE;
  };

  const handleRemoveFile = (file: any) => {
    setFileList((prev) => prev.filter((f) => f.uid !== file.uid));
  };

  const uploaderProps: UploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    fileList,
    // beforeUpload,
    onChange(info) {
      const newFileList = info.fileList;
      setFileList(newFileList);
    },
    onRemove: handleRemoveFile,
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };

  const notificationWayOptions = [
    { label: t("ticket.list.email"), value: 1 },
    { label: t("ticket.list.sms"), value: 2 },

    { label: t("ticket.list.all"), value: 3 },
    { label: t("ticket.list.none"), value: 4 },
  ];
  const priorityOptions = [
    { label: t("ticket.list.low"), value: 1 },
    { label: t("ticket.list.medium"), value: 2 },
    { label: t("ticket.list.high"), value: 3 },
    { label: t("ticket.list.critical"), value: 4 },
  ];

  useEffect(() => {
    if (ticketDetails) {
      const data = { ...ticketDetails };

      data["EndDate"] = dayjs(data["EndDate"]);
      data["StatusId"] = ticketDetails?.Status?.Id;
      data["DepartmentIds"] = ticketDetails?.Departments?.map(
        (item: any) => item?.DepartmentId
      );

      form.setFieldsValue(data);
    }
  }, [ticketDetails]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["TicketFiles"] = fileList;
    if (pageType === "customerDetails") {
      formValues["CustomerId"] = filter?.CustomerId;
    }

    try {
      if (ticketDetails) {
        await updateTicketWithPut({ ...ticketDetails, ...formValues });
      } else {
        const response = await createTicket(formValues);
        dispatch(hanldleSetTicketDetails({data:{Id:response?.Value}}))
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();
      setFileList([]);
      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });
    
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <MazakaForm
      form={form}
      onFinish={hangleOnFinish}
      submitButtonVisible={false}
      initialValues={{
        NotificationWay: 4,
        Priority: 2,
      }}
    >
      <Row gutter={[10, 10]}>
      
          <GeneralUsers
            label={t("task.list.assignedUser")}
            placeholder={t("task.list.assignedUser")}
            name="UserId"
            rules={[{ required: true, message: "" }]}
          
            xs={24}
          />
      
       
          <GeneralSubjectTicket
            label={t("ticket.list.subject")}
            placeholder={t("ticket.list.subject")}
            xs={24}
          
            name="SubjectId"
            rules={[{ required: true, message: "" }]}
          />
  

        {pageType === "tickets" && (
        
            <GeneralCustomer
              label={t("ticket.list.customer")}
              placeholder={t("ticket.list.customer")}
              xs={24}
             
              name="CustomerId"
              rules={[{ required: true, message: "" }]}
            />
     
        )}
       
          <MazakaInput
            label={t("ticket.list.title")}
            placeholder={t("ticket.list.title")}
            xs={24}
          
            name="Title"
            rules={[{ required: true, message: "" }]}
          />
      
    
          <MazakaTextArea
            xs={24}
          
            name="Description"
            label={t("ticket.list.description")}
            placeholder={t("ticket.list.description")}
          />
       
       
          <MazakaSelect
            label={t("ticket.list.priority")}
            placeholder={t("ticket.list.priority")}
            xs={24}
          
            name="Priority"
            options={priorityOptions}
            rules={[{ required: true, message: "" }]}
          />
     
      
          {/* <MazakaSelect
            label={t("ticket.list.notificationWay")}
            placeholder={t("ticket.list.notificationWay")}
            xs={24}
            md={8}
            name="NotificationWay"
            options={notificationWayOptions}
            rules={[{ required: true, message: "" }]}
          /> */}
          <GeneralNotificationWays
              label={t("task.list.notificationWay")}
              placeholder={t("task.list.notificationWay")}
              name="NotificationWayId"
              
             
             
              xs={24}
            />
     
      
          <MazakaDatePicker
           
            label={t("ticket.list.endDate")}
            xs={24}
            name="EndDate"
            disablePastDates
          />
       
     
          <GeneralDepartments
            label={t("ticket.list.departments")}
            placeholder={t("ticket.list.departments")}
            xs={24}
          
            name="DepartmentIds"
            multiple
            rules={[{ required: true, message: "" }]}
          />
       

        {/* <Col xs={24} md={8} className="!my-2">
          <Dragger {...uploaderProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text !text-xs">
              {t("ticket.list.uploadFile")}
            </p>
          </Dragger>
        </Col>

        <Col xs={24}>
          <Row gutter={[0, 5]}>
            {fileList.map((file) => (
              <Col
                key={file.uid}
                xs={24}
                className="!flex justify-between items-center"
              >
                <span className="!text-xs ">{file.name}</span>
                <DeleteOutlined
                  onClick={() => handleRemoveFile(file)}
                  className="!text-red-500"
                />
              </Col>
            ))}
          </Row>
        </Col> */}

        <Col xs={24}>
          <MazakaButton
            processType={formActions.submitProcessType}
            htmlType="submit"
            status="save"
          >
            {ticketDetails? t("ticket.list.edit") : t("ticket.list.add")}
          </MazakaButton>
        </Col>
      </Row>
    </MazakaForm>
  );
};

export default AddOrUpdateTicket;
