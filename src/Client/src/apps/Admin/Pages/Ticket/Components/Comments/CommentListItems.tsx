import { RootState } from "@/store/Reducers";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useGetTicketComments } from "../../ServerSideStates";

import { Timeline, Avatar, Space, Row, Col, Pagination, Skeleton } from "antd";
import { UserOutlined } from "@ant-design/icons";

import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";

const CommentListItems = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();
  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,
    TicketId: ticketDetails?.Id,
  });

  useEffect(()=>{
    setFilter({...filter, TicketId:ticketDetails?.Id,})
  },[ticketDetails])

  const comments = useGetTicketComments(filter);
  const commentData = comments?.data?.Value ?? [];
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    setFilter(newFilter);
  };

  return (
    <Row>
      <Skeleton loading={comments.isLoading || comments.isFetching}>
        {
          comments?.data?.Value?.length>0&&
       <>
        <Col xs={24}>
          <PageTitle title={t("ticket.list.comments")} isSubTitle={true} />
        </Col>
        <Col xs={24}>
          <Timeline
            className="!mt-4"
            mode="left"
            items={commentData.map((item: any) => ({
              children: (
                <Space align="start">
                  <Avatar
                    className="!w-[25px] !h-[25px]"
                    icon={<UserOutlined />}
                  />
                  <div>
                    <div className="!text-xs !text-gray-500">
                      {dayjs(item.InsertDate).format("DD.MM.YYYY HH:mm")}
                    </div>
                    <div className="!text-xs">{item.UserName}</div>
                    <div
                      className="!text-xs"
                      style={{ marginTop: 4 }}
                      dangerouslySetInnerHTML={{
                        __html: item.Comment,
                      }}
                    />
                  </div>
                </Space>
              ),
            }))}
          />
        </Col>
        <Col xs={24} className="!flex justify-end">
          <Pagination
            className="!px-0"
            onChange={handleChangePagination}
            total={comments.data?.FilteredCount || 0}
            current={comments.data?.PageNumber}
            pageSize={comments.data?.PageSize}
            showLessItems
            size="small"
            showSizeChanger
            locale={{ items_per_page: "" }}
            showTotal={(total) => `${total}`}
          />
        </Col>
       </>
        }
      </Skeleton>
    </Row>
  );
};

export default CommentListItems;
