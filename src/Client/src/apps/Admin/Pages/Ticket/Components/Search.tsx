import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetTicketFilter } from "../ClientSideStates";




const Search = () => {
  const { filter } = useSelector((state: RootState) => state.ticket);
  console.log("filter",filter)

  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetTicketFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
