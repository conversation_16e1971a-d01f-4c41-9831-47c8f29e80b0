import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { hanldleSetTicketFilter } from "../ClientSideStates";
import GeneralSubjectTicket from "@/apps/Common/GeneralSubjectTicket";
import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import dayjs from "dayjs";

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter,pageType } = useSelector((state: RootState) => state.ticket);
  const [form] = Form.useForm();
 
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    let currentFilter = { ...filter };

    for (let key in formValues) {
      if (
        !formValues[key] ||
        (Array.isArray(formValues[key]) && formValues[key].length === 0)
      ) {
        delete formValues[key];
        delete currentFilter[key];
        if(key=="Date")
        {
          delete currentFilter["StartDate"]
          delete currentFilter["EndDate"]
        }
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DD");
      delete formValues["Date"];
    }
    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(
      hanldleSetTicketFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };

  useEffect(() => {
    const startDate = filter?.StartDate;
    const endDate = filter?.EndDate;
    const dateRange = [];
    if (startDate) {
      dateRange.push(dayjs(startDate));
    }
    if (endDate) {
      dateRange.push(dayjs(endDate));
    }

    form.setFieldsValue({
      Date: dateRange,
      Type: filter?.Type,
      SubjectId: filter?.SubjectId,
      CustomerId: filter?.CustomerId,
      Title: filter?.Title,
      Priority: filter?.Priority,
    });
  }, [filter]);

  const priorityOptions = [
    { label: t("ticket.list.low"), value: 1 },
    { label: t("ticket.list.medium"), value: 2 },
    { label: t("ticket.list.high"), value: 3 },
    { label: t("ticket.list.critical"), value: 4 },
  ];
  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          <GeneralSubjectTicket
            label={t("ticket.list.type")}
            placeholder={t("ticket.list.type")}
            xs={24}
            name="SubjectId"
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameSubjectId", obj.label);
            }}
          />
          {
            pageType==="tickets"&&
          <GeneralCustomer
            label={t("ticket.list.customer")}
            placeholder={t("ticket.list.customer")}
            xs={24}
            name="CustomerId"
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameCustomerId", obj.label);
            }}
          />
          }
          <MazakaInput
            label={t("ticket.list.title")}
            placeholder={t("ticket.list.title")}
            xs={24}
            name="Title"
            allowClear
          />
          <MazakaSelect
            label={t("ticket.list.priority")}
            placeholder={t("ticket.list.priority")}
            xs={24}
            name="Priority"
            options={priorityOptions}
            allowClear
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNamePriority", obj.label);
            }}
          />
          <MazakaRangePicker
            name={"Date"}
            label={t("ticket.list.dateRange")}
            xs={24}
            className="!m-0"
          />

          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>
          </Col>
          <Form.Item
            name={"customNamePriority"}
            className="!hidden"
          ></Form.Item>
          {
            pageType==="customerDetails"&&
          <Form.Item
            name={"customNameCustomerId"}
            className="!hidden"
          ></Form.Item>
          }
          <Form.Item
            name={"customNameSubjectId"}
            className="!hidden"
          ></Form.Item>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
