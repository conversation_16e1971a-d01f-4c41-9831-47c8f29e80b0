import { Col, Row, Typography } from "antd";
import AddOrUpdateComment from "./AddOrUpdateComment";
import CommentListItems from "./CommentListItems";
import { useTranslation } from "react-i18next";

const TicketCommentIndex = () => {
  const {t} = useTranslation()
  return (
    <>
      <Row gutter={[0, 20]}>
      <Col xs={24}>
          <AddOrUpdateComment />
        </Col>
        <Col xs={24} className="!max-h-[700px]">
          <CommentListItems />
        </Col>
       
      </Row>
    </>
  );
};

export default TicketCommentIndex;
