import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import { createTicketComment } from "../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import endPoints from "../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import MazakaTextEditor from "@/apps/Common/MazakaTextEditor";
import { MazakaButton } from "@/apps/Common/MazakaButton";

const AddOrUpdateComment = () => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    formValues["TicketId"] = ticketDetails?.Id;
    try {
      await createTicketComment(formValues);

      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));

      queryClient.resetQueries({
        queryKey: endPoints.getTicketComments,
        exact: false,
      });
      form.resetFields();
      // setSelectedRecord(null);
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[0, 20]}>
          <MazakaTextEditor
            className="!m-0"
            name={"Comment"}
            xs={24}
            label={t("ticket.list.explainActionYouPerformed")}
            placeholder={t("ticket.list.explainActionYouPerformed")}
            form={form}
          />
          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
            >
              {t("ticket.list.save")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateComment;
