import { createSlice } from "@reduxjs/toolkit";

const InitialState: {
  filter: any;
  AutoDialerDetails: any | null;
  isShowAddUserDepartmetn: boolean;
  isShowSelectDataDrawer: boolean;
  selectedAutoDialerDataTab: string;
  isShowFindCustoemrDrawer: boolean;
  activeSocketList: any[];
} = {
  AutoDialerDetails: null,
  isShowAddUserDepartmetn: false,
  isShowSelectDataDrawer: false,
  selectedAutoDialerDataTab: "1",
  isShowFindCustoemrDrawer: false,
  activeSocketList: [],
  filter: {
    PageNumber: 1,
    PageSize: 30,
    IsArchive: false,
  },
};

const autoDialerSlice = createSlice({
  name: "autoDialerSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetAutoDialerFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },

    hanldleSetAutoDialerSocketList: (state, action) => {
      let data = action.payload;
      state.activeSocketList = data.socketList;
    },
    hanldleSetAutoDialerIshowSelectDataDrawer: (state, action) => {
      let data = action.payload;
      state.isShowSelectDataDrawer = data.status;
    },
    hanldleSetFindCustomerDrawer: (state, action) => {
      let data = action.payload;
      state.isShowFindCustoemrDrawer = data.status;
    },
    hanldleSetAutoDialerSelectDataTab: (state, action) => {
      let data = action.payload;
      state.selectedAutoDialerDataTab = data.key;
    },

    handleResetAllFieldsUser: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetAutoDialerFilter: (state) => {
      state.filter = {
        PageNumber: 1,
        PageSize: 30,
        IsArchive: false,
      };
    },
  },
});

export const {
  handleResetAllFieldsUser,
  handleResetAutoDialerFilter,
  hanldleSetAutoDialerFilter,
  hanldleSetAutoDialerIshowSelectDataDrawer,
  hanldleSetAutoDialerSelectDataTab,
  hanldleSetFindCustomerDrawer,
  hanldleSetAutoDialerSocketList,
} = autoDialerSlice.actions;
export default autoDialerSlice;
