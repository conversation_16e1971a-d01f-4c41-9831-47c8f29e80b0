import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralFilterTags from "../../Users/<USER>/GeneralFilterTags";
import { hanldleSetAutoDialerFilter } from "../ClientSideStates";



const FilterTags = () => {
    const {filter} = useSelector((state:RootState)=>state.autoDialer)
    return ( <>
    
    <GeneralFilterTags
    showFilterTagLength={3}
    filter={filter}
    actionFunc={hanldleSetAutoDialerFilter}
    actionFunkKey="filter"
    excludedKeys={["PageNumber","PageSize","Status","IsArchive"]}
    />
    </> );
}
 
export default FilterTags;