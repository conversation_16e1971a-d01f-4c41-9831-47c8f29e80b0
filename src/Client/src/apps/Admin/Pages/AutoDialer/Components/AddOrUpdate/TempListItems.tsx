import React from 'react';
import { Table } from 'antd';

interface TempDataListViewItem {
  date: string;
  count: number;
  title: string;
  channel: string;
}

const data: TempDataListViewItem[] = [
  {
    date: '2025-04-16',
    count: 10,
    title: 'Örnek Başlık 1',
    channel: 'YouTube',
  },
  {
    date: '2025-04-15',
    count: 5,
    title: 'Örnek Başlık 2',
    channel: 'Twitter',
  },
];

const columns = [
    {
        title: 'Başlık',
        dataIndex: 'title',
        key: 'title',
      },
      {
        title: 'Kanal',
        dataIndex: 'channel',
        key: 'channel',
      },
  {
    title: '<PERSON><PERSON><PERSON>',
    dataIndex: 'date',
    key: 'date',
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON>',
    dataIndex: 'count',
    key: 'count',
  },
  
];

const TempDataTable: React.FC = () => {
  return <Table dataSource={data} columns={columns} rowKey="title" />;
};

export default TempDataTable;
