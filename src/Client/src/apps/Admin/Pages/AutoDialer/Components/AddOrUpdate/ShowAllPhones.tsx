import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useGetAutoDialerDetails } from "../../ServerSideStates";
import { Col, Table } from "antd";
import { useTranslation } from "react-i18next";

const ShowAllPhones = () => {
  const { autoDialerId } = useParams();
  const{t} = useTranslation()
  const autoDialerDetails = useGetAutoDialerDetails(autoDialerId || "");
  const { customersListSelectedItems } = useSelector(
    (state: RootState) => state.customer
  );
  const selectedItems =
    customersListSelectedItems?.length > 0
      ? customersListSelectedItems
      : autoDialerDetails?.data?.Value?.TargetNumbers?.map((item: any) => {
          return {
            Phone: item,
          };
        });
  return (
    <Col xs={24} className="!mt-6">
      <Table
        columns={[
          {
            title: t("customers.list.phone"),
            dataIndex: "Phone",
            render: (value: string) => {
              return <span className="!text-xs">{value}</span>;
            },
          },
        ]}
        dataSource={selectedItems}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",

          total: selectedItems?.length || 0,

          pageSize: 30,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default ShowAllPhones;
