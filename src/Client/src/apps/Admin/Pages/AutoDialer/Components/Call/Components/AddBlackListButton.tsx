import { MazakaButton } from "@/apps/Common/MazakaButton";
import { Modal } from "antd";
import { addCustomerBlackList } from "../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useSearchParams } from "react-router-dom";

const AddBlackListButton = () => {
    const [searchParams] = useSearchParams()
    const confirm = () => {
        Modal.confirm({
          title: "Uyarı",
          icon: null,
          content: `Bu müşteri siyah listeye alıncak .Onaylıyor musunuz?`,
          okText: "Tamam",
          cancelText: "Vazgeç",
          onOk: async () => {
            try {
              await addCustomerBlackList({CustomerId:searchParams.get("customerId")||""});
              openNotificationWithIcon("success", "islem Başarılı");
             
            } catch (error: any) {
                console.log("some things wrong during add customer to blacklist",error)
             openNotificationWithIcon("error","İşlem Başarsız")
            }
          },
        });
      };
    return ( <>
     <MazakaButton onClick={confirm} htmlType="button"  status="error">Add To Black List</MazakaButton>
    </> );
}
 
export default AddBlackListButton;