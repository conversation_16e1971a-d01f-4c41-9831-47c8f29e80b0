import { handleSetCustomersListSelectedItems } from "@/apps/Admin/Pages/Customers/ClientSideStates";
import AddOrUpdateCustomerIndex from "@/apps/Admin/Pages/Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import DetailsFilter from "@/apps/Admin/Pages/Customers/Components/DetailsFilter";
import ListItems from "@/apps/Admin/Pages/Customers/Components/ListItems";

import { MazakaButton } from "@/apps/Common/MazakaButton";
import { RootState } from "@/store/Reducers";
import {
  BranchesOutlined,
  FilterOutlined,
  PlusOutlined,
  UserAddOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import CustomerIndex from "../../AddOrUpdate/Customer/CustomerIndex";
import { hanldleSetFindCustomerDrawer } from "../../../ClientSideStates";
import customerEndPoints from "@/apps/Admin/Pages/Customers/EndPoints"
import { useQueryClient } from "react-query";

const UnKnownCustomerOptions = () => {
  const queryClient = useQueryClient()
  const { isShowFindCustoemrDrawer } = useSelector(
    (state: RootState) => state.autoDialer
  );
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();

  return (
    <>
      {searchParams.get("customerId") && searchParams.get("unSavedNumber") && (
        <MazakaButton
          onClick={async() => {
            searchParams.delete("customerId");
            await setSearchParams(searchParams);
            queryClient.resetQueries({
              queryKey: customerEndPoints.getCustomerListFilter,
              exact: false,
            });
          }}
          icon={<PlusOutlined />}
        >
          {t("customers.add.addCustomer")}
        </MazakaButton>
      )}
      <MazakaButton
        onClick={() => {
          dispatch(hanldleSetFindCustomerDrawer({ status: true }));
        }}
        icon={<FilterOutlined />}
      >
        {t("callNotification.list.findCustomer")}
      </MazakaButton>

      <Drawer
        title={t("callNotification.filter.filterData")}
        open={isShowFindCustoemrDrawer}
        onClose={() => {
          dispatch(hanldleSetFindCustomerDrawer({ status: false }));
        }}
        width={"80%"}
      >
        <CustomerIndex />
      </Drawer>
    </>
  );
};

export default UnKnownCustomerOptions;
