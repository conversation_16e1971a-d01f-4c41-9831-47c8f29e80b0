import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { handleSetCustomersListSelectedItems } from "../../Customers/ClientSideStates";
import { useQueryClient } from "react-query";
import endPoints from "@/apps/Admin/Pages/AutoDialer/EndPoints";
import { RootState } from "@/store/Reducers";
import { commonRoutePrefix } from "@/routes/Prefix";

const AddOtoDailerButton = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  return (
    <>
      <MazakaButton
        icon={<PlusOutlined />}
        status="save"
        onClick={() => {
          navigate(`${commonRoutePrefix}/add-auto-dialer`);
          dispatch(
            handleSetCustomersListSelectedItems({
              selectedIds: [],
              selectedItems: [],
            })
          );
          queryClient.resetQueries({
            queryKey: endPoints.getAutoDialerDetails,
            exact: false,
          });
        }}
      >
        {t("autoDialer.list.add")}
      </MazakaButton>
    </>
  );
};

export default AddOtoDailerButton;
