
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { Col,  Form, Row, Typography } from "antd";

import useMazakaForm from "@/hooks/useMazakaForm";
import InternalComment from "./InternalComments";
import AutoDialerName from "./AutoDialerName";

const TakeNotes = () => {
  const { Text } = Typography;
  const [form] = Form.useForm();

  return (
    <Row
      className="!h-screen !border-gray-100 !bg-gray-100 "
      style={{ borderRight: "1px solid gray" }}
    >
      <Col xs={24}>
       
          <Row gutter={[0, 10]} className="!px-2 !py-2">
            <Col xs={24}>
              
              <AutoDialerName/>
             
              <Col xs={24} className="">
                  <InternalComment />
                </Col>
              {/* <Col xs={24} className="!px-2">
                <Text className="!text-xs font-bold">Report Call</Text>
              </Col>

              <Row gutter={[10, 10]} className="!px-2">
                <MazakaTextArea
                  xs={24}
                  label="External Note"
                  placeholder="Note"
                />

                <MazakaSelect xs={24} label="Product" placeholder="Product" />
                <MazakaSelect
                  xs={24}
                  label="Rakip Markalar"
                  placeholder="Rakip Markalar"
                />
                <MazakaSelect xs={24} label="Status" placeholder="Status" />
                <Col xs={24} className="!flex gap-2">
                  <MazakaButton
                    htmlType="submit"
                    processType={formActions.submitProcessType}
                    status="save"
                  >
                    Save
                  </MazakaButton>
                
                </Col>
              </Row> */}
            </Col>
          </Row>
       
      </Col>
    </Row>
  );
};

export default TakeNotes;
