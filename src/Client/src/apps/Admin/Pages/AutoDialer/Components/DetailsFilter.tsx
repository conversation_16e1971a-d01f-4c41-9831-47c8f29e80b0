import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetAutoDialerFilter } from "../ClientSideStates";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import dayjs from "dayjs";

const DetailsFilter: FC<{ onFinish: any }> = ({ onFinish }) => {
  const { filter } = useSelector((state: RootState) => state.autoDialer);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    let currentFilter = { ...filter };

    for (let key in formValues) {
      if (
        formValues[key]!==0&&
        !formValues[key] ||
        (Array.isArray(formValues[key]) && formValues[key].length === 0)
      ) {
        delete formValues[key];
        delete currentFilter[key];
        if (key == "Date") {
          delete currentFilter["StartDate"];
          delete currentFilter["EndDate"];
        }
      }
    }
     if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
          formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
            "YYYY-MM-DD"
          );
          formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DD");
          delete formValues["Date"];
        }

    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(
      hanldleSetAutoDialerFilter({
        filter: newFilter,
      })
    );
    mazakaForm.setSuccess(1000, () => {}, t("form.transactionSuccessful"));
    onFinish();
  };
  const statusOptions = [
    { label: t("autoDialer.list.pending"), value: 0 },
    { label: t("autoDialer.list.inProgress"), value: 1 },
    { label: t("autoDialer.list.complete"), value: 2 },
    { label: t("autoDialer.list.cancel"), value: 3 },
  ];

  useEffect(() => {
    const startDate = filter?.StartDate;
    const endDate = filter?.EndDate;
    const dateRange = [];
    if (startDate) {
      dateRange.push(dayjs(startDate));
    }
    if (endDate) {
      dateRange.push(dayjs(endDate));
    }
    form.setFieldsValue({
      Date: dateRange,
      SearchTerm: filter?.SearchTerm,
      Status: filter?.Status,
      Total: filter?.Total,
      Done: filter?.Done,
    });
  }, [filter]);
  return (
    <>
      <MazakaForm
        form={form}
        submitButtonVisible={false}
        onFinish={handleOnFinish}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("autoDialer.list.name")}
            placeholder={t("autoDialer.list.name")}
            xs={24}
            name="SearchTerm"
            allowClear
          />
          <MazakaSelect
            label={t("autoDialer.list.status")}
            placeholder={t("autoDialer.list.status")}
            xs={24}
            name="Status"
            allowClear
            options={statusOptions}
            onChange={(value: string, obj: any) => {
              form.setFieldValue("customNameStatus", obj.label);
            }}
          />
         

          <MazakaRangePicker label={t("autoDialer.list.dateRange")} xs={24} name="Date" />

          <Col xs={24}>
            <MazakaButton
              htmlType="submit"
              processType={formActions.submitProcessType}
              status="save"
            >
              {t("users.filter.filterButton")}
            </MazakaButton>
            <Form.Item
            name={"customNameStatus"}
            className="!hidden"
          ></Form.Item>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
