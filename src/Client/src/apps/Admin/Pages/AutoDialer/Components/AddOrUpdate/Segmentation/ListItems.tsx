import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Modal, Table, Typography } from 'antd';
import { ItemType } from 'antd/es/menu/interface';
import { useState } from 'react';





const ListItems = () => {
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // To track selected rows
  const { Text } = Typography
  const columns = [
    {
      title: 'name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: any, b: any) => {
        return a.name.localeCompare(b.name);
      },
    },
    {
      title: 'Count',
      dataIndex: 'Count',
      key: 'Count',
      sorter: (a: any, b: any) => {
        return a.count.localeCompare(b.count);
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },

    //   {
    //     title: "",
    //     dataIndex: "edit",
    //     key: "edit",
    //     width: "8%",
    //     render: (key: any, record: any) => (
    //       <Col className="text-end pr-2">
    //         <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
    //           <EllipsisOutlined className="text-xl" />
    //         </Dropdown>
    //       </Col>
    //     ),
    //   },
  ];

  const dataSource = [
    {
      key: '1',
      name: "Test1"
    },
    {
      key: '2',
      name: "Test2"
    },
    {
      key: '3',
      name: "Test3"
    },
  ];
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        setIsShowEditDrawer(true)
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: null,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        // try {
        //   await deleteCar(record);
        //   openNotificationWithIcon("success", "islem Başarılı");
        //   queryClient.resetQueries({
        //     queryKey: endpoints.getCarListFilter,
        //     exact: false,
        //   });
        // } catch (error: any) {
        //   showServiceErrorMessage(error,{},"Car",true)
        // }
      },
    });
  };
  return <>
    <Table columns={columns} dataSource={dataSource}
      rowSelection={{
        selectedRowKeys,
        onChange: setSelectedRowKeys,
      }}
      pagination={{
        position: ["bottomRight"],
        className: "!px-0",
        // onChange: handleChangePagination,
        // total: cars.data?.FilteredCount || 0,
        // current: cars.data?.PageIndex,
        // pageSize: cars.data?.PageSize,
        showLessItems: true,
        size: "small",
        showSizeChanger: true,
        locale: { items_per_page: "" },
        showTotal: (e) => `${e}`,
      }}

    />


  </>
};

export default ListItems;
