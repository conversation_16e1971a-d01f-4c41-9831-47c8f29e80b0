import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { Col, Row } from "antd";

const DetailsFilter = () => {
  return (
    <>
      <MazakaForm
      submitButtonVisible={false}
      >
        <Row gutter={[20,20]}>
          <MazakaInput
            label="Name"
            placeholder="Name"
            xs={24}
          />
          <MazakaInput
            label="Sur Name"
            placeholder="Sur Name"
            xs={24}
          />
           <MazakaSelect
            label="Data Source"
            placeholder="Data Source"
            xs={24}
          />
          <MazakaSelect
          label="Type"
          placeholder="Type"
          xs={24}
          />
          <MazakaDatePicker
          label="Date"
          xs={24}
          
          />
          <Col xs={24}>
            <MazakaButton status="save" >Filter</MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
