import React, { useState } from "react";
import type { UploadProps } from "antd";
import { message, Upload } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

const { Dragger } = Upload;

const UploadFile: React.FC = () => {
  const [fileList, setFileList] = useState<File[]>([]);

  const handleChange: UploadProps["onChange"] = (info:any) => {

    setFileList([ info?.file]); // Tek dosya yükleme (multiple: false)
      message.success(` uploaded successfully`);

   
  };

  const handleRemove = (fileName: string) => {
    setFileList((prevList) => prevList.filter((file) => file.name !== fileName));
  };

  const props: UploadProps = {
    name: "file",
    multiple: false,
    accept: ".xlsx",
    showUploadList: false,
    onChange: handleChange,
    beforeUpload: () => false, // Upload'u engelle, manuel <PERSON> i<PERSON>in
  };

  console.log("filelist",fileList)

  return (
    <div>
      <Dragger {...props}>
        <p className="ant-upload-drag-icon !flex justify-center">
          <svg
            fill="#808080"
            width={40}
            height={40}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M8 256C8 119 119 8 256 8s248 111 248 248-111 248-248 248S8 393 8 256zm292 116V256h70.9c10.7 0 16.1-13 8.5-20.5L264.5 121.2c-4.7-4.7-12.2-4.7-16.9 0l-115 114.3c-7.6 7.6-2.2 20.5 8.5 20.5H212v116c0 6.6 5.4 12 12 12h64c6.6 0 12-5.4 12-12z"></path>
          </svg>
        </p>
        <p className="ant-upload-text">Click or drag file to this area to upload</p>
        <p className="ant-upload-hint">Supported xlsx file format</p>
      </Dragger>

      {fileList.length > 0 && (
        <div className="mt-4 space-y-2">
          {fileList.map((file) => (
            <div
              key={file.name}
              className="flex justify-between items-center p-2 border rounded-md"
            >
              <span className="truncate">{file.name}</span>
              <DeleteOutlined
                className="text-red-500 cursor-pointer"
                onClick={() => handleRemove(file.name)}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UploadFile;
