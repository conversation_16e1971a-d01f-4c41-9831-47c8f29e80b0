import React, { useState } from "react";
import {
  Calendar,
  Modal,
  Form,
  Input,
  DatePicker,
  Button,
  theme,
  List,
  Col,
  Row,
} from "antd";
import { Dayjs } from "dayjs";

interface EventItem {
  date: string;
  title: string;
}

const MyCalendar: React.FC = () => {
  const [events, setEvents] = useState<EventItem[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const { token } = theme.useToken();

  const handleSelect = (value: Dayjs) => {
    setSelectedDate(value);
  };

  const handleAddEvent = () => {
    form.validateFields().then((values) => {
      const newEvent: EventItem = {
        date: values.date.format("YYYY-MM-DD"),
        title: values.title,
      };
      setEvents([...events, newEvent]);
      setIsModalOpen(false);
      form.resetFields();
    });
  };

  const selectedDayEvents = events.filter(
    (e) => e.date === selectedDate?.format("YYYY-MM-DD")
  );


  const dateCellRender = (value: Dayjs) => {
    const dateStr = value.format("YYYY-MM-DD");
    const eventsForDate = events.filter((event) => event.date === dateStr);

    if (eventsForDate.length > 1) {
      return (
        <div
          className="multi-event-day"
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "red",
            borderRadius: 4,
            zIndex: -1,
          }}
        />
      );
    }

    return null;
  };




  return (
    <>
      <Calendar
        dateCellRender={dateCellRender}
        fullscreen={false} onSelect={handleSelect} />

      {selectedDate && (
        <div className="!flex flex-col gap-2">
          <h3 className="!font-bold">
            {selectedDate.format("YYYY-MM-DD")} - Events
          </h3>
          <List
            bordered
            dataSource={selectedDayEvents}
            renderItem={(item) => <List.Item>{item.title}</List.Item>}
          />
          <Button
            type="primary"

            onClick={() => setIsModalOpen(true)}
          >
            Add Event
          </Button>
        </div>
      )}

      <Modal
        title="Add Event"
        open={isModalOpen}
        onOk={handleAddEvent}
        onCancel={() => setIsModalOpen(false)}
        okText="Add"
        cancelText="Cancel"
      >
        <Col xs={24}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{ date: selectedDate }}
          >
            <Row gutter={[0, 10]}>
              <Col xs={24}>
                <Form.Item
                  label="Title"
                  name="title"
                  rules={[{ required: true, message: "" }]}
                >
                  <Input className="!w-full" placeholder="Title" />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  label="Date"
                  name="date"
                  rules={[{ required: true, message: "" }]}
                >
                  <DatePicker className="!w-full" disabled />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Col>
      </Modal>
    </>
  );
};

export default MyCalendar;
