import { Table, Typography } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";

interface QueueUserListProps {
  data: any[];
}

const UserQueueList: FC<QueueUserListProps> = ({ data }) => {
  const { Text } = Typography;
  const { t } = useTranslation();
  const columns = [
    {
      title: t("task.list.name"),
      dataIndex: "Name",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Table
        columns={columns}
        dataSource={data?.map((item: string) => {
          return {
            Name: item,
          };
        })}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",

          total: data?.length || 0,

          pageSize: 20,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </>
  );
};

export default UserQueueList;
