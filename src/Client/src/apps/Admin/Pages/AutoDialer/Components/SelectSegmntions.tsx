import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { Col, Row, Button } from "antd";

const SelectSegmentations = () => {
    const segmentationOptions = [
        { label: "Retail", value: "retail" },
        { label: "Enterprise", value: "enterprise" },
        { label: "VIP Customers", value: "vip_customers" },
        { label: "Trial Users", value: "trial_users" },
    ];
    

    return (
        <Col xs={24}>
            <MazakaForm submitButtonVisible={false}>
                <Row gutter={[20, 20]}>
                    <MazakaSelect
                        xs={24}
                        label="Select Segmentation"
                        placeholder="Select a segmentation"
                        size="middle"
                        options={segmentationOptions}
                    />
                    <MazakaInput
                        xs={24}
                        size="middle"
                        label="Number of Records to Assign"
                        placeholder="Enter number of records"
                        type="number"
                    />
                    <Col xs={24}>
                        <MazakaButton status={"save"} type="primary">
                            Apply Segmentation
                        </MazakaButton>
                    </Col>
                </Row>
            </MazakaForm>
        </Col>
    );
};

export default SelectSegmentations;
