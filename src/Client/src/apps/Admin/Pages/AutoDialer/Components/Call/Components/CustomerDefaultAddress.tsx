import { useGetCustomerDefaultAddress,  } from "@/apps/Admin/Pages/Customers/ServerSideStates";
import { Typography } from "antd";
import { useSearchParams } from "react-router-dom";

const CustomerDefaultAddress = () => {
  const { Text } = Typography;
  const [searchParams] = useSearchParams();
  const customerId = searchParams.get("customerId");
  const defaultAddresss = useGetCustomerDefaultAddress(customerId || null);
  const addresData = defaultAddresss?.data?.Value;
  


 
  return (
    <>
      { addresData && (
        <Text className="!text-xs !text-gray-500">{`${addresData?.City||""}-${addresData?.State||""}-${addresData?.Country}`}</Text>
      )}
    </>
  );
};

export default CustomerDefaultAddress;
