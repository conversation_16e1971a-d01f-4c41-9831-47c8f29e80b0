import { useGetCustomerDetails } from "@/apps/Admin/Pages/Customers/ServerSideStates";
import { useGetUserDetails } from "@/apps/Admin/Pages/Users/<USER>";
import { TeamOutlined } from "@ant-design/icons";
import { Typography } from "antd";
import { useSearchParams } from "react-router-dom";

const AdvisorName = () => {
  const { Text } = Typography;
  const [searchParams] = useSearchParams();
  const customerId = searchParams.get("customerId");
  const customerDetails = useGetCustomerDetails(customerId || null);
  const customerData = customerDetails?.data?.Value;
  const userDetails = useGetUserDetails(customerData?.AdvisorId || "");

  return (
    <>
      {userDetails?.data?.Value && (
        <>
          <TeamOutlined className="" />
          <Text className="!text-xs !text-gray-500">{`${
            userDetails?.data?.Value?.Name || ""
          } ${userDetails?.data?.Value?.SurName || ""}`}</Text>
        </>
      )}
    </>
  );
};

export default AdvisorName;
