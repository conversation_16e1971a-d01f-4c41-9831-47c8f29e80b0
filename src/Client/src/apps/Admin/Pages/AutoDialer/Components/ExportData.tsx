import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaCheckbox } from "@/apps/Common/MazakaCheckbox";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { Col, Row, Button, Typography } from "antd";

const ExportData = () => {
  const { Text } = Typography;

  return (
    <Col xs={24}>
      <MazakaForm>
        <Row gutter={[20, 20]}>
          <MazakaSelect
            xs={24}
            label="Select Employee"
            placeholder="Select Employee"
            size="middle"
            options={[]}
          />

          <MazakaCheckbox text="All Selected Items" className="!text-xs" xs={24} />
          <Col xs={24} className="!flex gap-1">
            <MazakaButton status="error" className="!text-xs">
              Assign Data to X Date
            </MazakaButton>

            <MazakaButton status="error" className="!text-xs">
              Select Assignment Rule
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default ExportData;
