import { Col, Row } from "antd";
import ListItems from "./ListItems";

const SegmentationIndex = () => {
    return (<>
        <Col xs={24} >
            <Row gutter={[0, 2]}>
                {/* <Col xs={24} >
                <TopOptions/>
            </Col> */}
                <Col xs={24} >
                    <ListItems />
                </Col>
            </Row>
        </Col>
    </>);
}

export default SegmentationIndex;