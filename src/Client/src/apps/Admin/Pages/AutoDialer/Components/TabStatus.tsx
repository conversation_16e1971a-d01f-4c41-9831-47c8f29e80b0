import { RootState } from "@/store/Reducers";
import { Tabs, TabsProps } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  hanldleSetAutoDialerFilter,
} from "../ClientSideStates";

const TabStatus = () => {
  const { filter } = useSelector((state: RootState) => state.autoDialer);

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const onChange = (key: string) => {
    const currentFilter: any = { ...filter };
    if (key === "1") {
      currentFilter["IsArchive"] = false;
    } else if (key === "2") {
      currentFilter["IsArchive"] = true;
    }
    dispatch(hanldleSetAutoDialerFilter({ filter: currentFilter }));
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: t("autoDialer.list.planed"),
    },
    {
      key: "2",
      label: t("autoDialer.list.archive"),
    },
  ];

  return (
    <>
      <Tabs activeKey={filter?.IsArchive===true?"2":"1"} defaultActiveKey="1" items={items} onChange={onChange}  />
    </>
  );
};

export default TabStatus;
