import GeneralPhoneNumber2 from "@/apps/Common/GeneralPhoneNumber2";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { Col, Row } from "antd";

const DetailsFilter = () => {
  return (
    <>
      <MazakaForm
      submitButtonVisible={false}
      >
        <Row gutter={[20,20]}>
        <MazakaSelect
          label="Customer"
          placeholder="Customer"
          />
          <MazakaSelect
          label="Customer Type"
          placeholder="Customer Type"
          />
           <MazakaSelect
          label="Sector"
          placeholder="Sector"
          />
          <MazakaInput
            label="Email"
            placeholder="Email"
            xs={24}
          />
          <GeneralPhoneNumber2
          label="Phone"
            placeholder="Phone"
          xs={24}
          />
           <MazakaSelect
          label="Priority"
            placeholder="Priority"
          xs={24}
          />
          <MazakaSelect
          label="Customer Group"
            placeholder="Customer Group"
          xs={24}
          />
          
          <MazakaSelect
          label="City"
          placeholder="City"
          />
          <MazakaSelect
          label="Departments"
          placeholder="Departments"
          />
          <Col xs={24}>
            <MazakaButton status="save" >Filter</MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default DetailsFilter;
