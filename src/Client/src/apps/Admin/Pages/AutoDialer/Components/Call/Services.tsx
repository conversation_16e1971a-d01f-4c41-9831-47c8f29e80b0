import { DataResponse } from "@/services/BaseResponseModel";
import { deleteRequest, get, post, put } from "@/services/Client";
import headers  from '@/services/Headers.json';
import endpoints from "./EndPoints"




  export const addCustomerBlackList = async (
    data: any
  ): Promise<DataResponse<any>> => {
    const url = `${endpoints.addCustomerBlackList}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
  };

 