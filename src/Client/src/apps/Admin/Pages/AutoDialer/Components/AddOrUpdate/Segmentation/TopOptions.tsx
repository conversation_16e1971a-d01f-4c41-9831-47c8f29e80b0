import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { FilterOutlined } from "@ant-design/icons";
import { <PERSON>, Divider, Drawer, Row } from "antd";
import { useState } from "react";
// import DetailsFilter from "./DetailsFilter";


const TopOptions = () => {
  const [isShowFilterDrawer, setIsShowFilterDrawer] = useState(false);
  return (
    <>
      <Col xs={24} className="!flex gap-2 !py-1 ">

        <MazakaButton
          onClick={() => {
            setIsShowFilterDrawer(true);
          }}
          className="!text-xs"
          icon={<FilterOutlined />}
          status="save"
        >
          Filter
        </MazakaButton>
      </Col>
      <Col xs={24}>
        <Divider className="!m-0" />
      </Col>
      <Col xs={24} >
        <Row className="!py-2" >
          <MazakaSelect
            xs={24}
            md={12}
            xl={6}
            placeholder="Select Segmentation"
          />

        </Row>
      </Col>
      <Col xs={24} >
        <Divider className="!m-0" />
      </Col>

      <Drawer
        title="Filter Data"
        open={isShowFilterDrawer}
        onClose={() => {
          setIsShowFilterDrawer(false);
        }}
      >
        {/* <DetailsFilter /> */}
      </Drawer>
    </>
  );
};

export default TopOptions;
