import React, { useState } from "react";
import { List, Typography, Row, Col } from "antd";
import { ArrowDownOutlined, ArrowUpOutlined } from "@ant-design/icons";

const { Text } = Typography;

const data = [
  {
    name: "<PERSON><PERSON>",
    datetime: "2025-04-20 14:30",
    description: "Toplantı notları ve yapılacaklar listesi.",
  },
  {
    name: "<PERSON>yş<PERSON> Demir",
    datetime: "2025-04-21 09:00",
    description: "Proje teslim tarihi hakkında bilgi verildi.",
  },
];

const ExternalNoteList: React.FC = () => {
  const [showAll, setShowAll] = useState(false);

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  const displayedData = showAll ? data : [data[0]];

  return (
    <Row>

      <Col xs={24} className="!flex justify-between items-center" >
        <Text className="!font-bold !text-xs"> External Comments </Text>
        {data.length > 1 && (
          showAll ? <ArrowUpOutlined className="!text-xs" /> : <ArrowDownOutlined className="!text-xs" />
        )}
      </Col>
      <Col xs={24}>
        <List
          itemLayout="vertical"
          dataSource={displayedData}
          renderItem={(item) => (
            <List.Item className="!p-0" >
              <Col xs={24}>
                <Row>
                  <Col span={24}>
                    <Text className="!text-xs">{item.description}</Text>
                  </Col>
                  <Col className="!flex gap-1">
                    <Text className="!text-[10px]">{item.name}</Text>
                    <Text className="!text-[10px]" type="secondary">
                      {item.datetime}
                    </Text>
                  </Col>
                </Row>
              </Col>
            </List.Item>
          )}
        />

      </Col>
    </Row>
  );
};

export default ExternalNoteList;
