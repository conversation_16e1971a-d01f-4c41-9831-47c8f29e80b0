import { useGetCustomerCallDetails, useGetCustomerDetails } from "@/apps/Admin/Pages/Customers/ServerSideStates";
import { Col, Typography } from "antd";
import { useSearchParams } from "react-router-dom";
import { useGetAutoDialerDetails } from "../../../ServerSideStates";
import dayjs  from 'dayjs';

const AutoDialerName = () => {
  const { Text } = Typography;
  const [searchParams] = useSearchParams();
  const callId = searchParams.get("callId");
  const callDetails = useGetCustomerCallDetails(callId||"")
  const callData = callDetails?.data?.Value
  const autoDialerDetails = useGetAutoDialerDetails(callData?.AutoDialerId||"")



  return (
    <>
      {autoDialerDetails?.data?.Value && (
        <>
        <Col
                xs={24}
                className="!flex justify-center !border !border-gray-200 !p-2 !rounded-sm !bg-gray-200 !px-1 "
              >

         <Text className="!text-xs ">
            <span className="!text-xs !font-bold" >{autoDialerDetails?.data?.Value?.Name||""}</span>{` ${autoDialerDetails?.data?.Value?.InsertDate?dayjs(autoDialerDetails?.data?.Value?.InsertDate).format("YYYY-DD-MM HH:mm"):""}`}
                
                </Text>
              </Col>
         
        </>
      )}
    </>
  );
};

export default AutoDialerName;
