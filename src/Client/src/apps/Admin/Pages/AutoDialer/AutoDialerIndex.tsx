import { Col,  Row } from "antd";
import ListViewItems from "./Components/ListViewItem";
import Title from "./Components/Title";
import TopOptions from "./Components/TopOptions";
import TabStatus from "./Components/TabStatus";
import endPoints from "./EndPoints"
import { useQueryClient } from "react-query";
import { useEffect } from "react";

const OtoDailerIndex = () => {
  const queryClient = useQueryClient()
  useEffect(()=>{
      queryClient.resetQueries({
        queryKey: endPoints.getAutoDialerListFilter,
        exact: false,
      });
    },[])
  return (
    <>
      <Col xs={24}>
        <Row gutter={[0, 0]}>
          <Col xs={24} className="">
            <Title />
          </Col>
          <Col xs={24} style={{ paddingLeft:"10px"}}>
            <TabStatus />
          </Col>

          <Col xs={24}>
            <TopOptions />
          </Col>
          <Col xs={24} className="!px-2">
            <ListViewItems />
          </Col>
        </Row>
      </Col>
    </>
  );
};

export default OtoDailerIndex;
