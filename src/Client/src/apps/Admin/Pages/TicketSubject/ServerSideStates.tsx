import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getSubjectTicketListFilter } from "./Services";





export const useGetSubjectTickets = (filter?: any) => {
  const query = useQuery(
    [endpoints. getSubjectTicketListFilter,filter],
    () => {
      return getSubjectTicketListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



