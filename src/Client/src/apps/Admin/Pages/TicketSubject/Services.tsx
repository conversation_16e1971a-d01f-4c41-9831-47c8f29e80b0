import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getSubjectTicketListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getSubjectTicketListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createSubjectTicket = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createSubjectTicket}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateSubjectTicketWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateSubjectTicketWithUrl}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteSubjectTicket = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteSubjectTicket}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
