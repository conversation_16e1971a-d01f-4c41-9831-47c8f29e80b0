import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetSubjectTicketFilter } from "../ClientSideStates";



const Search = () => {
  const { filter } = useSelector((state: RootState) => state.sector);

  return (
    <>
      <GeneralSearch
      filter={filter}
      searchFieldName="SearchTerm"
      filterActionFunc={hanldleSetSubjectTicketFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
