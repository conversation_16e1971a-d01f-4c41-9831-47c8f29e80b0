import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import Search from "./Search";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import AddOrUpdateSubjectTicket from "./AddOrUpdateSubjectTicket";
import { useTranslation } from "react-i18next";


const TopOptions = () => {
  const {t} = useTranslation()
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  return (
    <>
      <Row>
        <>
          <Col xs={24}>
            <Search />
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
          <Col xs={24}>
            <Divider className="!m-0 !text-gray-400" />
          </Col>
        </>

        <div className=" !py-1 !px-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              setIsShowAddDrawer(true);
            }}
          >
          {t("subjectTicket.add")}
          </MazakaButton>
        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
      </Row>
      <Drawer
        title= {t("subjectTicket.addSubjectTicket")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateSubjectTicket
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
