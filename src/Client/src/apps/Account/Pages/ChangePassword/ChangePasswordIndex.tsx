import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Divider, Form, Row, Typography } from "antd";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { changePassword } from "./Services";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaPassword } from "@/apps/Common/MazakaPassword";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import PageTitle from "@/apps/Common/PageTitle";

const ChangePasswordIndex = () => {
  const { Text } = Typography;
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();
  const { userInfoes } = useSelector((state: RootState) => state.profile);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    formValues["NewEmail"] = userInfoes?.Email;

    try {
      await changePassword(formValues);
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
    } catch (error: any) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };
  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row>
            <Col xs={24} className=" ">
              <div className="!flex gap-3 items-center menu-height">
                <PageTitle title={t("settings.changePassword")} isSubTitle />
              </div>
            </Col>
            <Col xs={24}>
              <Divider className="!m-0" />
            </Col>
            <Col xs={24} className="!mt-2">
              <Row className="!px-2" gutter={[10, 10]}>
                <Col xs={24} md={12}>
                  <Row gutter={[10, 10]}>
                    <MazakaPassword
                      isShowDescriptionPass={false}
                      xs={24}
                      label={t("settings.oldPassword")}
                      placeholder={t("settings.oldPassword")}
                      name={"OldPassword"}
                    />
                    <MazakaPassword
                      isShowDescriptionPass={true}
                      xs={24}
                      label={t("settings.newPassword")}
                      placeholder={t("settings.newPassword")}
                      name={"NewPassword"}
                    />
                    <Form.Item  className={"ant-col ant-col-24 ant-col-xs-24"}
                      style={{ padding: "5px" }}
                      name="ConfirmPassword"
                      label={t("settings.confirmNewPassword")}
                      dependencies={["NewPassword"]}
                      hasFeedback
                      rules={[
                        {
                          required: true,
                          message: t("settings.passwordRequired")
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (!value || getFieldValue("NewPassword") === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error(t("settings.passwordsDoNotMatch")));
                          }
                        })
                      ]}
                    >
                      <MazakaPassword
                        isShowDescriptionPass={false}
                        xs={24}
                        placeholder={t("settings.confirmNewPassword")}
                        name={"ConfirmPassword"}
                      />
                    </Form.Item>
                    <Col xs={24}>
                      <MazakaButton
                        htmlType="submit"
                        processType={formActions.submitProcessType}
                        status="save"
                      >
                        {t("settings.change")}
                      </MazakaButton>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangePasswordIndex;
