import { DataResponse } from "@/services/BaseResponseModel";
import headers  from '@/services/Headers.json';
import { post } from "@/services/Client";
import endPoints from "./EndPoints"






export const changePassword= async (data: any): Promise<DataResponse<any>> => {
    const url = `${endPoints.changePassword}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
  };

