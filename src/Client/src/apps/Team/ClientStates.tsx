import { createSlice } from "@reduxjs/toolkit";

const InitialState: {filter:any,} = {
 
  filter: {
    PageNumber: 1,
    PageSize: 20,
   

  },
};

const teamSlice = createSlice({
  name: "TeamSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetTeamFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
   
  
    handleResetAllFieldsTeam: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterTeam: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 20,
      }
      },
  },
});

export const { handleResetAllFieldsTeam,handleResetFilterTeam,hanldleSetTeamFilter } = teamSlice.actions;
export default teamSlice;
