
import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getChatMessageDetails, getUserChatListFilter } from "./Services";

export const useGetUserChats= (filter:any) => {
  const query = useQuery(
    [endpoints. getUserChatListFilter,filter],
    () => {
      return getUserChatListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetChatMessageDetails= (chatId:string) => {
    const query = useQuery(
      [endpoints. getChatMessageDetails,chatId],
      () => {
        return getChatMessageDetails(chatId);
      },
      {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      }
    );
  
    return query;
  };
