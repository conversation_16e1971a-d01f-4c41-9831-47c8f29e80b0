import { createSlice } from "@reduxjs/toolkit";

const InitialState: {userChatFilter:any,} = {
 
  userChatFilter: {
    PageNumber: 1,
    PageSize: 20,
   
  },
};

const chatSlice = createSlice({
  name: "ChatSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetUserChatFilter: (state, action) => {
      let data = action.payload;
      state.userChatFilter = data.filter;
    },
   
   
  
    handleResetAllFieldsChat: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetUserChatFilter: (state) => {
       state.userChatFilter = {
        PageNumber: 1,
        PageSize: 20,
      }
      },
  },
});

export const { handleResetAllFieldsChat, handleResetUserChatFilter, hanldleSetUserChatFilter } = chatSlice.actions;
export default chatSlice;
