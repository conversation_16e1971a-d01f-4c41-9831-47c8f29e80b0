import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getLanguageListFilter = async (
filter:any
): Promise<DataResponse<any>> => {
const query = filter?CreateUrlFilter(filter):null

  const url = `${endpoints.getLanguageListFilter}?${query||""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const createLanguage = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createLanguage}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const updateLanguageWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateLanguageWithUrl}/${data.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteLanguage = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteLanguage}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
