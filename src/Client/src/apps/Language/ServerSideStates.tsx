import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getLanguageListFilter } from "./Services";





export const useGetLanguages= (filter:any) => {
  const query = useQuery(
    [endpoints. getLanguageListFilter,filter],
    () => {
      return getLanguageListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};



