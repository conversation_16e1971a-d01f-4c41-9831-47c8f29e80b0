import {
  DeleteOutlined,
  FormOutlined,
} from "@ant-design/icons";
import { Col, Drawer,  Modal, Table, Tooltip, Typography } from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetLanguages } from "../ServerSideStates";
import { deleteLanguage } from "../Services";
import AddOrUpdateLanguage from "./AddOrUpdateLanguage";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { hanldleSetLanguageFilter } from "../ClientSideStates";

const ListItems = () => {
  const { Text } = Typography;
  const dispatch = useDispatch()
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { filter } = useSelector((state: RootState) => state.language);
  const languages = useGetLanguages(filter);
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const columns = [
    {
      title: t("language.code"),
      dataIndex: "Code",
      key: "Code",
      sorter: (a: any, b: any) => a.Code.localeCompare(b.Code),
      render: (value: string, record: any) => {
        return (
          <div className="!flex gap-1 items-center">
            <div
              className={`w-[12px] !h-[12px] ${
                record?.IsActive ? "!bg-[#35b214]" : "!bg-gray-300"
              }`}
            ></div>
            <Text className="!text-xs">{value}</Text>
          </div>
        );
      },
    },
    {
      title: t("language.name"),
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a.Name.localeCompare(b.Name),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
        <Tooltip title={t("language.edit")}>
          <FormOutlined
            className=" !text-[#0096d1] !text-sm"
            onClick={async(e) => {
              e.preventDefault();
              e.stopPropagation();
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
             
            }}
          />
        </Tooltip>

        <Tooltip title={t("language.delete")}>
          <DeleteOutlined
            className=" !text-[#9da3af] !text-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              confirm(record);
            }}
          />
        </Tooltip>
      </Col>
      ),
    },
  ];



  const confirm = (record: any) => {
    Modal.confirm({
      title: t("language.warning"),
      icon: null,
      content: t("language.deleteModalDesc"),
      okText: t("language.delete"),
      cancelText: t("language.cancel"),
      onOk: async () => {
        try {
          await deleteLanguage(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getLanguageListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };
const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetLanguageFilter({ filter: newFilter }));
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={languages?.data?.Value}
        loading={languages.isLoading || languages.isFetching}
        onRow={(record) => {
          return {
            onClick: async(event) => {
              await setSelectedRecord(record);
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: languages.data?.FilteredCount || 0,
          current: languages.data?.PageNumber,
          pageSize: languages.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />
      <Drawer
        title={t("language.editLanguage")}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateLanguage
          selectedRecord={selectedRecord}
          onFinish={() => setIsShowEditDrawer(false)}
        />
      </Drawer>
    </>
  );
};

export default ListItems;
