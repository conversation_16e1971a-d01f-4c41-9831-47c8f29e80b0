import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";

import endPoints from "../EndPoints";
import { createLanguage, updateLanguageWithPut } from "../Services";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { MazakaSwitch } from "@/apps/Common/MazakaSwitch";

const AddOrUpdateLanguage: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    const formValues = form.getFieldsValue();

    try {
      if (selectedRecord) {
        await updateLanguageWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createLanguage(formValues);
      }
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getLanguageListFilter,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{IsActive:true}}
      >
        <Row gutter={[20, 20]}>
          <MazakaInput
            label={t("language.code")}
            placeholder={t("language.code")}
            xs={24}
            name="Code"
            rules={[{ required: true, message: "" }]}
          />
          <MazakaInput
            label={t("language.name")}
            placeholder={t("language.name")}
            xs={24}
            name="Name"
            rules={[{ required: true, message: "" }]}
          />
          <MazakaSwitch
            label={t("users.add.status")}
            xs={24}
            md={12}
            xl={12}
            name={"IsActive"}
            checkedChildren={t("users.add.active")}
            unCheckedChildren={t("users.add.pasive")}
          />

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("language.edit") : t("language.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateLanguage;
