import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { PlusOutlined } from "@ant-design/icons";
import AddOrUpdateLanguage from "./AddOrUpdateLanguage";
import TopFilters from "./TopFilters";
import { useTranslation } from "react-i18next";


const TopOptions = () => {
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false);
  const{t} = useTranslation()
  return (
    <>
      <Row>
        <>
          <Col xs={24} className="!px-2">
          <TopFilters/>
          </Col>
          <Col xs={24} className="">
            <Divider className="!m-0" />
          </Col>
          <Col xs={24}>
            <Divider className="!m-0 !text-gray-400" />
          </Col>
        </>

        <div className=" !py-1 !px-2">
          <MazakaButton
            icon={<PlusOutlined />}
            onClick={() => {
              setIsShowAddDrawer(true);
            }}
          >
           {t("language.addLanguage")}
          </MazakaButton>
        </div>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
      </Row>
      <Drawer
        title={t("language.addLanguage")}
        open={isShowAddDrawer}
        onClose={() => {
          setIsShowAddDrawer(false);
        }}
      >
        <AddOrUpdateLanguage
          onFinish={() => {
            setIsShowAddDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default TopOptions;
