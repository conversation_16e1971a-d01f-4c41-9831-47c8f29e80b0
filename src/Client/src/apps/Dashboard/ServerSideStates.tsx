import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getAllDashboardInfoes } from "./Services";

export const useGetDashboardSummaryInfoes = (filter?: any) => {
  const query = useQuery(
    [endpoints.getAllDashboardInfoes, filter],
    () => {
      return getAllDashboardInfoes(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
