// PriorityPieChart.tsx
import { Card, Empty } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useGetDashboardSummaryInfoes } from "../ServerSideStates";
import { RootState } from "@/store/Reducers";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const data = [
  { name: "Low", value: 10 },
  { name: "Medium", value: 25 },
  { name: "High", value: 35 },
  { name: "Critical", value: 20 },
];

const COLORS = ["#34b115", "#ff9a0b", "#e05b4a", "#0096d1"];

const PriorityPieChart: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate()
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const summaryInfoes = useGetDashboardSummaryInfoes(filter);
  const [priorityData, setPriorityData] = useState<any[]>([]);

  useEffect(() => {
    if (summaryInfoes?.data?.Value) {
      const taskCountByPriority =
        summaryInfoes?.data?.Value?.TaskCountByPriority        ;

      const result = taskCountByPriority
        ? Object.entries(taskCountByPriority).map(([name, value]) => ({
            name,
            value,
          }))
        : [];

      setPriorityData(result);
    }
  }, [summaryInfoes?.data]);
  return (
    <Card
      style={{ width: "100%", height: 400 }}
      bodyStyle={{ padding: "10px" }}
      loading={summaryInfoes?.isLoading||summaryInfoes?.isFetching}
    >
      <div>
        <span className="!font-bold !text-sm cursor-pointer"
         onClick={()=>{
          navigate("/tasks")
        }}
        >
          {t("dashboard.priorityStatusTasks")}{" "}
        </span>
      </div>
      {
        priorityData?.length>0?<>
        
      <div>
        <ResponsiveContainer height={300}>
          <PieChart>
            <Pie
              data={priorityData}
              cx="50%"
              cy="50%"
              label
              outerRadius={100}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
        </>:<>
        <Empty/>
        </>
      }
    </Card>
  );
};

export default PriorityPieChart;
