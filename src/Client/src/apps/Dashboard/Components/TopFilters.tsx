import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import { RootState } from "@/store/Reducers";
import { Row, Form } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetDashboardFilter } from "../ClientStates";
import { useEffect } from "react";
import dayjs from "dayjs";

const TopFilters = () => {
  const [form] = Form.useForm();
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch();
  const handleChangeDate = (date: [dayjs.Dayjs | null, dayjs.Dayjs | null]) => {
    const currentFilter = { ...filter };

    const [start, end] = date || [];

    if (start && start.isValid()) {
      currentFilter["StartDate"] = dayjs(start).format("YYYY-MM-DD");
    } else {
      delete currentFilter["StartDate"];
    }

    if (end && end.isValid()) {
      currentFilter["EndDate"] = dayjs(end).format("YYYY-MM-DD");
    } else {
      delete currentFilter["EndDate"];
    }

    dispatch(hanldleSetDashboardFilter({ filter: currentFilter }));
  };

  useEffect(() => {
    const startDate = filter?.StartDate;
    const endDate = filter?.EndDate;
    const dateRange = [];
    if (startDate) {
      dateRange.push(dayjs(startDate));
    }
    if (endDate) {
      dateRange.push(dayjs(endDate));
    }

    form.setFieldsValue({
      Date: dateRange,
    });
  }, [filter]);
  return (
    <>
      <Form form={form}>
        <Row>
          <MazakaRangePicker
            name="Date"
            xs={24}
            md={12}
            xl={6}
            showTime={false}
            onChange={handleChangeDate}
          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilters;
