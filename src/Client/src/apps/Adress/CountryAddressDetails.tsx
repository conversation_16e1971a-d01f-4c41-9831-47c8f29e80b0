import { FC, useEffect, useState } from "react";
import GeneralProvinces from "./Components/GeneralProvince";
import GeneralDistrict from "./Components/GeneralDistricts";
import { FormInstance } from "antd";
import GeneralCitiesTree from "./Components/GeneralCitiesTree";
import GeneralCountries from "./Components/GeneralCountries";
import { useTranslation } from "react-i18next";

interface CountryAdressDetailsProps {
  size?:"large" |"small"|"middle",
  selectedRecord?: any;
  form: FormInstance;
  viewingFields?: {
    country: boolean;
    stateProvince: boolean;
    district: boolean;
    city: boolean;
  };
  selectInputType?: "inline" | "block";
  fieldsRequiredRules?: {
    country: boolean;
    stateProvince: boolean;
    district: boolean;
    city: boolean;
  };
  addresNamesInfo?: Record<string, any>;
  setAddresNamesInfo?: any;
  allowClear?: boolean;
  multipleFields?:{
    country: string |undefined;
    stateProvince: string |undefined;
    district: any;
    city: any;
  }
  isShowLabel?:boolean
  responsive?:{
    city:{xs?:number,md?:number,lg?:number,xl?:number},
    district:{xs?:number,md?:number,lg?:number,xl?:number},
    neighbourhood:{xs?:number,md?:number,lg?:number,xl?:number},
  }
  filter?:any
  actionFunc?:any,
  pageType?:string
  
}

const CountryAdressDetails: FC<CountryAdressDetailsProps> = (props) => {
  const [viewingFields, setViewingFields] = useState({
    country: true,
    stateProvince: true,
    district: true,
    city: true,
  });

  const [fieldsRequiredRule, setFieldsRequiredRule] = useState({
    country: true,
    stateProvince: true,
    district: true,
    city: true,
  });
const {t} = useTranslation()
  const [selectedCountryId, setSelectedCountryId] = useState("");
  const [selectedStateProvinceId, setSelectedProvinceId] = useState("");
  const [selectedDistrictId, setSelectedDistrictId] = useState<string[]|string>("");

  const setInitialData = async () => {
    if (props.selectedRecord) {
      
      if(props?.multipleFields?.city==="multiple")
      {
        const { CountryId,StateProvinceId,DistrictIds, } = props.selectedRecord;
       
        

        if(CountryId)
        {
          setSelectedCountryId(CountryId)
        }
        if (StateProvinceId) {
    
          setSelectedProvinceId(StateProvinceId);
        }
        if (DistrictIds) {
          setSelectedDistrictId(DistrictIds);
        }
  
        props.form.setFieldsValue(props.selectedRecord);
      }
      else{
        const { CountryId, StateProvinceId, DistrictId } = props.selectedRecord;

        console.log(props.selectedRecord)
       
        if (CountryId) {
          setSelectedCountryId(CountryId);
        }
        if (StateProvinceId) {
          setSelectedProvinceId(StateProvinceId);
        }
        if (DistrictId) {
          setSelectedDistrictId(DistrictId);
        }
        
  
        props.form.setFieldsValue(props.selectedRecord);
      }
    }
  };

  useEffect(() => {
    setInitialData();
  }, [props.selectedRecord]);

  useEffect(() => {
    if (props.viewingFields) {
      setViewingFields(props.viewingFields);
    }
  }, [props.viewingFields]);

  useEffect(() => {
    if (props.fieldsRequiredRules) {
      setFieldsRequiredRule({ ...props.fieldsRequiredRules });
    }
  }, [props.fieldsRequiredRules]);

  const handleCountryChange = (countryId: string, obj: any) => {
    if (props.addresNamesInfo) {
      const newInfo = { ...props.addresNamesInfo, countryName: obj?.label };
      props.setAddresNamesInfo?.(newInfo);
    }

    props.form.resetFields(["StateProvinceId", "DistrictId", "CityId"]);
    setSelectedCountryId(obj?.key);
    setSelectedProvinceId("");
    setSelectedDistrictId("");
  };

  const handleProvinceChange = (provinceId: string, obj: any) => {
    if (props.addresNamesInfo) {
      const newInfo = { ...props.addresNamesInfo, stateProvinceName: obj?.label,CityId:provinceId };
      props.setAddresNamesInfo?.(newInfo);
    }
    

    props.form.resetFields(["districtId", "neighbourhoodId"]);
    setSelectedProvinceId(obj.key);
    setSelectedDistrictId("");
  };

  const handleDistrictChange = (districtId: string, obj: any) => {
  
    if(props?.multipleFields?.city==="multiple")
    {
      if (props.addresNamesInfo){
        
        const newInfo:any = { ...props.addresNamesInfo };
        newInfo["DistrictIds"]=districtId
        newInfo["districtNames"] = obj
       
        if(newInfo?.DistrictIds?.length<=0)
        {
          delete newInfo["districtNames"]
          delete newInfo["DistrictIds"]

        }
      
        props.setAddresNamesInfo?.(newInfo);
      }
    }
    else{

      if (props.addresNamesInfo) {
        const newInfo = { ...props.addresNamesInfo, districtName: obj?.label,DistrictiId:districtId };
        
        props.setAddresNamesInfo?.(newInfo);
      }
  
    }
    props.form.resetFields(["neighbourhoodId"]);
    setSelectedDistrictId(obj.key);
  };

  const handleCityChange = (value: string,obj:any ) => {

   
    if(props?.multipleFields?.city==="multiple")
    {
      if (props.addresNamesInfo){
        const newInfo:any = { ...props.addresNamesInfo, neighbourhoodNames:obj,NeighbourhoodIds:value };
        if(newInfo?.NeighbourhoodIds?.length<=0)
          {
            delete newInfo["neighbourhoodNames"]
            delete newInfo["NeighbourhoodIds"]
  
          }
        props.setAddresNamesInfo(newInfo);
      }
    }
    else{
      if (props.addresNamesInfo) {
        
         const newInfo = { ...props.addresNamesInfo, cityName:obj[0],neighbourhoodId:value };
         props.setAddresNamesInfo(newInfo);
         
       }

    }
  };


 

  return (
    <>
      {viewingFields.country && (
        <GeneralCountries
          label={t("customers.add.country")}
          placeholder={t("customers.add.country")}
          name="Country"
          rules={[{ required: fieldsRequiredRule.country, message: "" }]}
          onChange={handleCountryChange}
          allowClear={props.allowClear}
        />
      )}
      {viewingFields.stateProvince && (
        <GeneralProvinces
        pageType={props.pageType}
        xs={props?.responsive?.city?.xs||24}
        md={props?.responsive?.city?.md}
        lg={props?.responsive?.city?.lg}
        xl={props?.responsive?.city?.xl}
          label={props.isShowLabel===false?"":t("customers.add.state")}
          placeholder={t("customers.add.state")}
          name="State"
          externalValueId={selectedCountryId}
          rules={[{ required: fieldsRequiredRule.stateProvince, message: "" }]}
          onChange={handleProvinceChange}
          allowClear={props.allowClear}
          size={props.size}
        />
      )}
      {viewingFields.district && (
        <GeneralDistrict
        pageType={props.pageType}
        label={props.isShowLabel===false?"":t("customers.add.city")}
        xs={props?.responsive?.district?.xs||24}
        md={props?.responsive?.district?.md}
        lg={props?.responsive?.district?.lg}
        xl={props?.responsive?.district?.xl}
          placeholder={t("customers.add.city")}
          name={props?.multipleFields?.district==="multiple"?"districtIds":"city"}
          externalValueId={selectedStateProvinceId}
          rules={[{ required: fieldsRequiredRule.district, message: "" }]}
          onChange={handleDistrictChange}
          allowClear={props.allowClear}
         size={props.size}
       mode={props?.multipleFields?.district||undefined}
        
        />
      )}
      {viewingFields.city && (
        <GeneralCitiesTree
        pageType={props.pageType}
        label={props.isShowLabel===false?"":t("customers.add.neighborhood")}
        xs={props?.responsive?.neighbourhood?.xs||24}
        md={props?.responsive?.neighbourhood?.md}
        lg={props?.responsive?.neighbourhood?.lg}
        xl={props?.responsive?.neighbourhood?.xl}
          placeholder={t("customers.add.neighborhood")}
          name={props?.multipleFields?.district==="multiple"?"neighbourhoodIds":"neighbourhood"}
          externalValueId={selectedDistrictId}
          rules={[{ required: fieldsRequiredRule.city, message: "" }]}
          onChange={handleCityChange}
          allowClear={props.allowClear}
          mode={props?.multipleFields?.city||undefined}

          size={props.size}
        />
      )}
    </>
  );
};

export default CountryAdressDetails;
