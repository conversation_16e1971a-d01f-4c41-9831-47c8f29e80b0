import { useQuery } from "react-query";
import endpoints from "./EndPoints"
import {
  getAllAddressInfoes,
  getCityList,
  getCountryList,
  getDistrictList,
  getStateProvinceList,
} from "./Services";

export const useGetAllAddressInfoes = () => {
  return useQuery([endpoints.getAllAddressInfoes], () => getAllAddressInfoes(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useCountries = () => {
  return useQuery([endpoints.getCountries], () => getCountryList(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};
export const useStateProavinces = (countryId?: string) => {
    let endpoint = countryId
      ? [endpoints.getStateProvinces, countryId]
      : [endpoints.getStateProvinces];
    return useQuery(endpoint, () => getStateProvinceList(countryId), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    });
  };
export const useDistricts = (stateProvinceId?: string|string[]) => {
  let endpoint = stateProvinceId
    ? [endpoints.getDistricts, stateProvinceId]
    : [endpoints.getDistricts];
  return useQuery(endpoint, () => getDistrictList(stateProvinceId), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useCities = (districtId?: string|string[],isMultiple?:boolean) => {
  let endpoint = districtId
    ? [endpoints, districtId]
    : [endpoints.getCities];
  return useQuery(endpoint, () => getCityList(districtId,isMultiple), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};
