import { get, post } from "@/services/Client";
import endpoints from "./EndPoints"
import headers from "@/services/Headers.json";

export const getAllAddressInfoes = async () => {
  const url = `${endpoints.getAllAddressInfoes}`;
  const config = headers.content_type.application_json;
  return get(url, config);
};

export const getCountryList = async () => {
  const url = `${endpoints.getCountries}`;
  const config = headers.content_type.application_json;
  return get(url, config);
};

export const getStateProvinceList = async (countryId?:string) => {
  if (countryId) {
    const url = `${endpoints.getStateProvinces + "/" + countryId}/states`;
    const config = headers.content_type.application_json;
    return get(url, config);
  } else {
    return new Promise((resovle, reject) => {
      resovle([]);
    });
  }
};

export const getDistrictList = async (stateProvinceId?: string|string[]) => {
  if (stateProvinceId) {
    const url = `${endpoints.getDistricts+ "/" + stateProvinceId}/cities`;
    const config = headers.content_type.application_json;
    return get(url, config);
  } else {
    return new Promise((resovle, reject) => {
      resovle([]);
    });
  }
};
export const getCityList = async (districtId?: string|string[],isMultiple?:boolean) => {
  if (districtId) {
    if(isMultiple)
    {
      const url = `${endpoints.getNeighbourhoodsByDistrictIds}`;
      const config = headers.content_type.application_json;
      return post(url,districtId||[], config);
    }
    else{

      const url = `${endpoints.getCities + "/" + districtId}`;
      const config = headers.content_type.application_json;
      return get(url, config);
    }
  } else {
    return new Promise((resovle, reject) => {
      resovle([]);
    });
  }
};