import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { useGetAllAddressInfoes } from "../ServerSideStates";

import { FC, useState } from "react";
import { normalizeString } from "../helpers/TRNormalizedName";
import { useTranslation } from "react-i18next";

interface GeneralAllAddressInfoesProps {
  isShowLabel: boolean;
  inputShape: "inline" | "block";
}

const GeneralAllAddressInfoes: FC<GeneralAllAddressInfoesProps> = (props) => {
  const { data } = useGetAllAddressInfoes();
  const addressInfo = data?.Value || {};

  const [selectedCountry, setSelectedCountry] = useState<string>();
  const [selectedState, setSelectedState] = useState<string>();
  const [selectedCity, setSelectedCity] = useState<string>();
  const { t } = useTranslation();

  const mapToOptions = (arr: string[] = []) => {
    return arr.map((item) => ({ label: item, value: item }));
  };

  return (
    <>
      <MazakaSelect
        xs={24}
        xl={props.inputShape === "block" ? 24 : 8}
        showSearch
        name="Country"
        allowClear
        label={t("customers.add.country")}
        placeholder={t("customers.add.country")}
        options={mapToOptions(addressInfo.Countries)}
        onChange={setSelectedCountry}
        value={selectedCountry}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(
            option.label.toLowerCase().trim()
          );
          const status = (normalizedLabel ?? "").includes(
            normalizedInput.toLowerCase()
          );
          return status;
        }}
      />
      <MazakaSelect
        xs={24}
        xl={props.inputShape === "block" ? 24 : 8}
        name="State"
        showSearch
        allowClear
        label={props.isShowLabel === false ? "" : t("customers.add.state")}
        placeholder={t("customers.add.state")}
        options={mapToOptions(addressInfo.States)}
        onChange={setSelectedState}
        value={selectedState}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(
            option.label.toLowerCase().trim()
          );
          const status = (normalizedLabel ?? "").includes(
            normalizedInput.toLowerCase()
          );
          return status;
        }}
      />
      <MazakaSelect
        xs={24}
        xl={props.inputShape === "block" ? 24 : 8}
        name="City"
        showSearch
        allowClear
        label={props.isShowLabel === false ? "" : t("customers.add.city")}
        placeholder={props.isShowLabel === false ? "" : t("customers.add.city")}
        options={mapToOptions(addressInfo.Cities)}
        onChange={setSelectedCity}
        value={selectedCity}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(
            option.label.toLowerCase().trim()
          );
          const status = (normalizedLabel ?? "").includes(
            normalizedInput.toLowerCase()
          );
          return status;
        }}
      />
    </>
  );
};

export default GeneralAllAddressInfoes;
