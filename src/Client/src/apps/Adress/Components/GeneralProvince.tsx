import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { FC } from "react";
import useCapitalize from "../helpers/useCapitalize";
import { useStateProavinces } from "../ServerSideStates";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";


const GeneralProvinces: FC<GeneralSelectInputs> = (props) => {

  const stateProvinces = useStateProavinces(props.externalValueId);

  let { capitalize } = useCapitalize();


  return (
    <>
      <MazakaSelect
      
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        size={props.size}
        loading={stateProvinces.isLoading || stateProvinces.isFetching}
        disabled={
          stateProvinces.isLoading ||
          stateProvinces.isFetching ||
          props.disabled
        }
        className={props.className+" "+props.pageType==="advertList"?"advert-list-address":""}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={
          stateProvinces?.data?.Value
            ? stateProvinces.data.Value.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Name,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralProvinces;
