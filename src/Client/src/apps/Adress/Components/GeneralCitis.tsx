


import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { FC,} from "react";
import { useCities } from "../ServerSideStates";
import useCapitalize from "../helpers/useCapitalize";
import { MazakaSelect } from "apps/Common/MazakaSelect";

const GeneralCities: FC<GeneralSelectInputs> = (props) => {

  const cities = useCities(props.externalValueId);
  let { capitalize } = useCapitalize();


  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={cities.isLoading || cities.isFetching}
        disabled={cities.isLoading || cities.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={
          cities.data?.data
            ? cities.data?.data.map((item: any) => {
                return {
                  key: item.id,
                  value: item.id,
                  label: capitalize(item.name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCities;
