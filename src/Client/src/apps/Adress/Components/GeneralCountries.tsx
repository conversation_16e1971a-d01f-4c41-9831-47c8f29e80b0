

import { FC, } from "react";
import { useCountries } from "../ServerSideStates";
import useCapitalize from "../helpers/useCapitalize";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";


const GeneralCountries: FC<GeneralSelectInputs> = (props) => {

  const countries = useCountries();
  let { capitalize } = useCapitalize();

  


  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={countries.isLoading || countries.isFetching}
        disabled={countries.isLoading || countries.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={
          countries?.data?.Value
            ? countries.data.Value.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Name,
                  label: capitalize(item.Name || " "),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCountries;
