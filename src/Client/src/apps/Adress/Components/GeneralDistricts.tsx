"use client";


import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { FC,} from "react";
import { useDistricts } from "../ServerSideStates";
import useCapitalize from "../helpers/useCapitalize";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";

const GeneralDistrict: FC<GeneralSelectInputs> = (props) => {
 
  const districts = useDistricts(props.externalValueId);
  let { capitalize } = useCapitalize();
 

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        
        loading={districts.isLoading || districts.isFetching}
        disabled={districts.isLoading || districts.isFetching || props.disabled}
        className={props.className+" "+props.pageType==="advertList"?"advert-list-address":""}
        showSearch={true}
        size={props.size}
        allowClear={props.allowClear || false}
        filterOption={(input, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={
          districts.data?.Value
            ? districts.data?.Value.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Name,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
        
      />
    </>
  );
};

export default GeneralDistrict;
