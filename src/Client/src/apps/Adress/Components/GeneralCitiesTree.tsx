import { FC, useEffect, useState } from "react";
import { Col, Form, TreeSelect } from "antd";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { normalizeString } from "../helpers/TRNormalizedName";
import { useCities } from "../ServerSideStates";
import useCapitalize from "../helpers/useCapitalize";

const GeneralCitiesTree: FC<GeneralSelectInputs> = (props) => {
  const cities = useCities(props.externalValueId, props.mode === "multiple" || props.mode === "tags");
  const { capitalize } = useCapitalize();

  const [treeData, setTreeData] = useState<any[]>([]);
  const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (cities.data) {
      const transactionDataList = cities.data.data || [];

      const formatTreeData = (city: any) => {
        const children = [];

        // neighbourhood varsa ekle
        if (city.neighbourhood && city.neighbourhood.length > 0) {
          children.push(
            ...city.neighbourhood.map((neighbourhood: any) => ({
              title: capitalize(neighbourhood.name),
              value: neighbourhood.id,
              children: neighbourhood.subNeighbourhood
                ? neighbourhood.subNeighbourhood.map((sub: any) => ({
                    title: capitalize(sub.name),
                    value: sub.id,
                  }))
                : [],
            }))
          );
        }

        // subNeighbourhood varsa ekle (type1 için)
        if (city.subNeighbourhood && city.subNeighbourhood.length > 0) {
          children.push(
            ...city.subNeighbourhood.map((sub: any) => ({
              title: capitalize(sub.name),
              value: sub.id,
            }))
          );
        }

        return {
          title: capitalize(city.name),
          value: city.id,
          children: children.length > 0 ? children : undefined,
        };
      };

      const formattedTreeData = transactionDataList.map(formatTreeData);
      setTreeData(formattedTreeData);
    }
  }, [cities.data]);

  const filterTreeNode = (inputValue: string, treeNode: any) => {
    const normalizedInput = normalizeString(inputValue.toLowerCase().trim());
    const normalizedTitle = normalizeString(treeNode.title.toLowerCase().trim());
    return normalizedTitle.includes(normalizedInput);
  };

  return (
    <Col
      span={props.span ?? 24}
      md={props.md}
      sm={props.sm}
      lg={props.lg}
      xl={props.xl}
      xs={props.xs}
      
    >
      <Form.Item
        className={props.className}
        name={props.name}
        rules={props.rules}
        label={props.label}
        tooltip={props.tooltip}
      >
        <TreeSelect
         className={props.className}
          showSearch

          style={{ width: "100%" }}
          value={selectedValue}
          dropdownStyle={{ maxHeight: 400, overflow: "auto",width:props.pageType==="advertList"?264:"90%", }}
          placeholder={props.placeholder}
          allowClear
      
          
          treeData={treeData}
          treeDefaultExpandAll
          onChange={props.onChange}
          filterTreeNode={filterTreeNode}
          multiple={props.mode === "multiple"}
        />
      </Form.Item>
    </Col>
  );
};

export default GeneralCitiesTree;
