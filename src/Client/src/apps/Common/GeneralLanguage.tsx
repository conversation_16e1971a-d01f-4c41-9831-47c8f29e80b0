import { FC } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetLanguages } from "../Language/ServerSideStates";

const GeneralLanguage: FC<GeneralSelectInputs> = (props) => {
  const languages: any = useGetLanguages({activeOnly:true});

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={languages.isLoading || languages.isFetching}
        disabled={languages.isLoading || languages.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(
            option.label.toLowerCase().trim()
          );
          const status = (normalizedLabel ?? "").includes(
            normalizedInput.toLowerCase()
          );
          return status;
        }}
        label={props.label}
        options={
          languages.data?.Value
            ? languages.data?.Value?.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Name,
                  label: item.Name,
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralLanguage;
