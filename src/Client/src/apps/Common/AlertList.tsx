import { <PERSON><PERSON>, <PERSON>, <PERSON>, Typo<PERSON> } from "antd";
import { FC, Fragment } from "react";
import { useTranslation } from "react-i18next";
interface error {
  Description: string;
  faild: number;
  sucess: number;
}
interface AlertListProps {
  data: error[];
}
const AlertList: FC<AlertListProps> = ({ data }) => {

  const { t } = useTranslation();
  return (
    <>
      <Col xs={24} className="!mt-10 !max-h-[500px] !overflow-scroll">
        <Row gutter={[0, 10]}>
         {
          data[0]?.faild&&
          <Col xs={24} className="!flex gap-1 items-center">
            <span className=" !text-xs !font-bold">
              {t("autoDialer.list.numberitemGivingErrors")}
            </span>
            <span className="!text-red-500 !text-xs">{data[0]?.faild}</span>
          </Col>
         }
         {
          data[0]?.sucess&&
          <Col xs={24} className="!flex gap-1 items-center">
            <span className=" !text-xs !font-bold">
              {t("autoDialer.list.numberSuccessfulUploads")}
            </span>
            <span className="!text-green-500 !text-xs">{data[0]?.sucess}</span>
          </Col>
         }

          {data?.map((item, index) => {
            return (
              <Col xs={24} key={index}>
                <Alert
                  type="error"
                  description={item?.Description || ""}
                  closable
                />
              </Col>
            );
          })}
        </Row>
      </Col>
    </>
  );
};

export default AlertList;
