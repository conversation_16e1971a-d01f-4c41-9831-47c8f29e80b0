import { RootState } from "@/store/Reducers";
import { Form, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import { hanldleSetCallFilter } from "../ClientSideStates";
import { MazakaRangePicker } from "@/apps/Common/MazakaRangePicker";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import { determineCallStatus } from "@/helpers/Call";

const TopFilters = () => {
  const { t } = useTranslation();
  const { filter } = useSelector((state: RootState) => state.call);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const directionStatusOptions = [
    { label: t("customers.add.inbound"), value: 0 },
    { label: t("customers.add.outbound"), value: 1 },
  ];
  const handleRangeDateTime = (value: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    const currentFilters = { ...filter };

    if (value && value[0] && value[1]) {
      currentFilters["StartTime"] = value[0].format("YYYY-MM-DDTHH:mm");
      currentFilters["EndTime"] = value[1].format("YYYY-MM-DDTHH:mm");
    } else {
      delete currentFilters["StartTime"];
      delete currentFilters["EndTime"];
    }

    dispatch(hanldleSetCallFilter({ filter: currentFilters }));
  };

  const handleChangeDirection = (value: number) => {
    const currentFilters = { ...filter };
    if (value === 0 || value) {
      currentFilters["Direction"] = value;
    } else {
      delete currentFilters["Direction"];
    }
    dispatch(hanldleSetCallFilter({ filter: currentFilters }));
  };
  const handleUserChange = (value: string,obj:any) => {
    const currentFilters = { ...filter };
    if (value) {
      currentFilters["UserId"] =obj?.key;
    } else {
      delete currentFilters["UserId"];
    }
    dispatch(hanldleSetCallFilter({ filter: currentFilters }));
  };
  const handleChangeStatus = (value: number) => {
    const currentFilters = { ...filter };
    if (value === 0 || value) {
      currentFilters["Status"] = value;
    } else {
      delete currentFilters["Status"];
    }
    dispatch(hanldleSetCallFilter({ filter: currentFilters }));
  };
  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <MazakaRangePicker
            xs={24}
            xl={6}
            showTime={true}
            onChange={handleRangeDateTime}
            name="Date"
          />

          <MazakaSelect
            placeholder={t("customers.add.direction")}
            xs={24}
            xl={4}
            onChange={handleChangeDirection}
            allowClear
            name="Direction"
            options={directionStatusOptions}
          />
          <MazakaSelect
            placeholder={t("customers.add.status")}
            xs={24}
            xl={4}
            onChange={handleChangeStatus}
            allowClear
            name="Status"
            options={determineCallStatus("select", 0, t)}
          />
          <GeneralUsers
            placeholder={t("users.users")}
            name="UserId"
            xs={24}
            extensionStatus="add"
            xl={4}
            onChange={handleUserChange}
            allowClear
          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilters;
