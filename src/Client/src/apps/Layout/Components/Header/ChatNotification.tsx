import { useEffect, useState } from "react";
import {
    HubConnectionBuilder,
    LogLevel,
    HttpTransportType,
} from "@microsoft/signalr";
import { notification } from "antd";
import {
    MessageTwoTone
} from "@ant-design/icons";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";

function ChatNotification() {
    const [connection, setConnection] = useState(null);
    useEffect(() => {
        const newConnection = new HubConnectionBuilder()
            .withUrl(`${setBackEndUrl()}/hubs/chathub`, {
                accessTokenFactory: () => {
                    return localStorage.getItem("access_token") || "";
                },
                skipNegotiation: true,
                transport: HttpTransportType.WebSockets,
                withCredentials: true
            })
            .configureLogging(LogLevel.Information)
            .withAutomaticReconnect()
            .build();
        newConnection
            .start()
            .then(() => {
                console.log("SignalR bağlantısı kuruldu");
                setConnection(newConnection);
            })
            .catch((err) => console.error("SignalR bağlantı hatası:", err));
        return () => {
            if (connection) {
                connection.stop();
            }
        };
    }, []);

    useEffect(() => {
        if (connection) {
            connection.on("NewMessage", async (data) => {
                console.log("NewMessage data", data);
                notification.destroy();
                notification.open({
                    message: "NewMessage Event",
                    description: `Data: ${data}`,
                    className: "!bg-blue-500 !text-white !z-[99999]",
                    duration: 0,
                });
            });
        }
        return () => {
            if (connection) {
                connection.off("NewMessage");
            }
        };
    }, [connection]);

    useEffect(() => {
        if (connection) {
            connection.on("MessageStatusChanged", async (data) => {
                console.log("MessageStatusChanged data", data);
                notification.destroy();
                notification.open({
                    message: "MessageStatusChanged Event",
                    description: `Data: ${data}`,
                    className: "!bg-blue-500 !text-white !z-[99999]",
                    duration: 0,
                });
            });
        }
        return () => {
            if (connection) {
                connection.off("MessageStatusChanged");
            }
        };
    }, [connection]);

    useEffect(() => {
        if (connection) {
            connection.on("ChatAssigned", async (data) => {
                console.log("ChatAssigned data", data);
                notification.destroy();
                notification.open({
                    message: "ChatAssigned Event",
                    description: `Data: ${data}`,
                    className: "!bg-blue-500 !text-white !z-[99999]",
                    duration: 0,
                });
            });
        }
        return () => {
            if (connection) {
                connection.off("ChatAssigned");
            }
        };
    }, [connection]);

    useEffect(() => {
        if (connection) {
            connection.on("ChatAssignedToYou", async (data) => {
                console.log("ChatAssignedToYou data", data);
                notification.destroy();
                notification.open({
                    message: "ChatAssignedToYou Event",
                    description: `Data: ${data}`,
                    className: "!bg-blue-500 !text-white !z-[99999]",
                    duration: 0,
                });
            });
        }
        return () => {
            if (connection) {
                connection.off("ChatAssigned");
            }
        };
    }, [connection]);

    //connection.on("UserTyping", async (data) => true false gelecek

    //await connection.invoke("JoinChat", chatId);
    //await connection.invoke("LeaveChat", chatId);
    //await connection.invoke("TypingStarted", chatId);
    //await connection.invoke("TypingStopped", chatId);

    return connection ? (
        <MessageTwoTone twoToneColor="#00FF00" />
    ) : (
        <MessageTwoTone twoToneColor="#FF0000" />
    );
}

export default ChatNotification;
