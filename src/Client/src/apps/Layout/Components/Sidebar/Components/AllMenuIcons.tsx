
import { commonRoutePrefix } from "@/routes/Prefix";
import {
  <PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  ClusterOutlined,
  DiffOutlined,
  FlagFilled,
  ImportOutlined,
  KeyOutlined,
  PauseCircleFilled,
  PauseCircleOutlined,
  PhoneOutlined,
  ProductOutlined,
  ProfileOutlined,
  <PERSON><PERSON>Outlined,
  TagsOutlined,
  TeamOutlined,
  UsergroupAddOutlined,
  UserOutlined,
  UserSwitchOutlined,
} from "@ant-design/icons";

export const allMenuIcons = [
    {
      icon: (
        <div app-chart-bar-regular-icon="">
          <svg
            width={17}
            height={17}
            fill={"white"}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M24 32c13.3 0 24 10.7 24 24V408c0 13.3 10.7 24 24 24H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H72c-39.8 0-72-32.2-72-72V56C0 42.7 10.7 32 24 32zM128 136c0-13.3 10.7-24 24-24l208 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-208 0c-13.3 0-24-10.7-24-24zm24 72H296c13.3 0 24 10.7 24 24s-10.7 24-24 24H152c-13.3 0-24-10.7-24-24s10.7-24 24-24zm0 96H424c13.3 0 24 10.7 24 24s-10.7 24-24 24H152c-13.3 0-24-10.7-24-24s10.7-24 24-24z" />
          </svg>
        </div>
      ),
      url: `/dashboard`,
    },
    {
      icon: (
        <div app-comments-alt-solid-icon="">
          <svg
            width={17}
            height={17}
            fill={"white"}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 576 512"
          >
            <path d="M416 224V64c0-35.3-28.7-64-64-64H64C28.7 0 0 28.7 0 64v160c0 35.3 28.7 64 64 64v54.2c0 8 9.1 12.6 15.5 7.8l82.8-62.1H352c35.3.1 64-28.6 64-63.9zm96-64h-64v64c0 52.9-43.1 96-96 96H192v64c0 35.3 28.7 64 64 64h125.7l82.8 62.1c6.4 4.8 15.5.2 15.5-7.8V448h32c35.3 0 64-28.7 64-64V224c0-35.3-28.7-64-64-64z" />
          </svg>
        </div>
      ),
      url: "/chat",
    },
    {
      icon: <PauseCircleFilled className={`!text-lg !text-white `} />,
      url: "/simpleUser/pauses",
    },
    {
      icon: <div app-wrench-solid-icon="">
      <svg
        width={17}
        height={17}
        fill={"white"}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 512 512"
      >
        <path d="M352 320c88.4 0 160-71.6 160-160c0-15.3-2.2-30.1-6.2-44.2c-3.1-10.8-16.4-13.2-24.3-5.3l-76.8 76.8c-3 3-7.1 4.7-11.3 4.7H336c-8.8 0-16-7.2-16-16V118.6c0-4.2 1.7-8.3 4.7-11.3l76.8-76.8c7.9-7.9 5.4-21.2-5.3-24.3C382.1 2.2 367.3 0 352 0C263.6 0 192 71.6 192 160c0 19.1 3.4 37.5 9.5 54.5L19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L297.5 310.5c17 6.2 35.4 9.5 54.5 9.5zM80 408a24 24 0 1 1 0 48 24 24 0 1 1 0-48z" />
      </svg>
    </div>,
      url: "/settings/general",
    },
    {
     
      url: "/customers",
      icon: <UsergroupAddOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/import-data",
      icon: <ImportOutlined className="!text-base !text-white" />,
    },
    {
      
      url: "/auto-dailer",
      icon: <PhoneOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/pauses",
      icon: <PauseCircleOutlined className="!text-base !text-white" />,
    },
    {
      
      url: "/tickets",
      icon: <TagsOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/tasks",
      icon: <ProfileOutlined className="!text-base !text-white" />,
    },
    {
      
      url: "/call-reports",
      icon: <BarChartOutlined className="!text-base !text-white" />,
    },
    {
    
      url: "/recordings",
      icon: <AimOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/subject-ticket",
      icon: <DiffOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/users",
      icon: <UserOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/user-department",
      icon: <UsergroupAddOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/professions",
      icon: <SolutionOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/sectors",
      icon: <ProductOutlined className="!text-base !text-white" />,
    },
    {
      
      url: "/classifications",
      icon: <TeamOutlined className="!text-base !text-white" />,
    },
    {
    
      url: "/authority",
      icon: <UserSwitchOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/roles",
      icon: <KeyOutlined className="!text-base !text-white" />,
    },
    {
      
      url: "/languages",
      icon: <FlagFilled className="!text-base !text-white" />,
    },
    {
     
      url: "/notification-ways",
      icon: <BellOutlined className="!text-base !text-white" />,
    },
    {
     
      url: "/logs",
      icon: <ClusterOutlined className="!text-base !text-[#909090]" />,
    },
    {
    
     
      url: "/pause-types",
      icon: <PauseCircleOutlined className="!text-base !text-[#909090]" />,
    },
  ];