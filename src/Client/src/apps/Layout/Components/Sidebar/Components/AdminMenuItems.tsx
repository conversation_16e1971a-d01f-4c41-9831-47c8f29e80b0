import { commonRoutePrefix } from "@/routes/Prefix";
import { PauseCircleFilled } from "@ant-design/icons";

export const adminMenuItems = (t: any) => {
  return [
    {
      title: t("dashboard.dashboard"),

      icon: (
        <div app-chart-bar-regular-icon="">
          <svg
            width={17}
            height={17}
            fill={"white"}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M24 32c13.3 0 24 10.7 24 24V408c0 13.3 10.7 24 24 24H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H72c-39.8 0-72-32.2-72-72V56C0 42.7 10.7 32 24 32zM128 136c0-13.3 10.7-24 24-24l208 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-208 0c-13.3 0-24-10.7-24-24zm24 72H296c13.3 0 24 10.7 24 24s-10.7 24-24 24H152c-13.3 0-24-10.7-24-24s10.7-24 24-24zm0 96H424c13.3 0 24 10.7 24 24s-10.7 24-24 24H152c-13.3 0-24-10.7-24-24s10.7-24 24-24z" />
          </svg>
        </div>
      ),

      url: commonRoutePrefix + "/dashboard",
    },
    {
      title: t("pauses.pauses"),
      icon: <PauseCircleFilled className={`!text-lg !text-white `} />,
      url: commonRoutePrefix + `/simpleUser/pauses`,
    },
    {
      title: t("settings.settings"),
      icon: (
        <div app-wrench-solid-icon="">
          <svg
            width={17}
            height={17}
            fill={"white"}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path d="M352 320c88.4 0 160-71.6 160-160c0-15.3-2.2-30.1-6.2-44.2c-3.1-10.8-16.4-13.2-24.3-5.3l-76.8 76.8c-3 3-7.1 4.7-11.3 4.7H336c-8.8 0-16-7.2-16-16V118.6c0-4.2 1.7-8.3 4.7-11.3l76.8-76.8c7.9-7.9 5.4-21.2-5.3-24.3C382.1 2.2 367.3 0 352 0C263.6 0 192 71.6 192 160c0 19.1 3.4 37.5 9.5 54.5L19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L297.5 310.5c17 6.2 35.4 9.5 54.5 9.5zM80 408a24 24 0 1 1 0 48 24 24 0 1 1 0-48z" />
          </svg>
        </div>
      ),
      url: "/settings/general",
    },
    // {
    //   title: t("team.team"),
    //   icon: (active: boolean) => (
    //     <i app-user-friends-solid-icon="">
    //       <svg
    //         width={17}
    //         height={17}
    //         fill={active ? "white" : "#b5b5b5"}
    //         xmlns="http://www.w3.org/2000/svg"
    //         viewBox="0 0 640 512"
    //       >
    //         <path d="M192 256c61.9 0 112-50.1 112-112S253.9 32 192 32 80 82.1 80 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C51.6 288 0 339.6 0 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zM480 256c53 0 96-43 96-96s-43-96-96-96-96 43-96 96 43 96 96 96zm48 32h-3.8c-13.9 4.8-28.6 8-44.2 8s-30.3-3.2-44.2-8H432c-20.4 0-39.2 5.9-55.7 15.4 24.4 26.3 39.7 61.2 39.7 99.8v38.4c0 2.2-.5 4.3-.6 6.4H592c26.5 0 48-21.5 48-48 0-61.9-50.1-112-112-112z" />
    //       </svg>
    //     </i>
    //   ),
    //   url: "/team",
    // },
    {
      title: t("chat.chat"),
      icon: (
        <div app-comments-alt-solid-icon="">
          <svg
            width={17}
            height={17}
            fill={"white"}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 576 512"
          >
            <path d="M416 224V64c0-35.3-28.7-64-64-64H64C28.7 0 0 28.7 0 64v160c0 35.3 28.7 64 64 64v54.2c0 8 9.1 12.6 15.5 7.8l82.8-62.1H352c35.3.1 64-28.6 64-63.9zm96-64h-64v64c0 52.9-43.1 96-96 96H192v64c0 35.3 28.7 64 64 64h125.7l82.8 62.1c6.4 4.8 15.5.2 15.5-7.8V448h32c35.3 0 64-28.7 64-64V224c0-35.3-28.7-64-64-64z" />
          </svg>
        </div>
      ),
      url: commonRoutePrefix + "/chat",
    },
    // {
    //   title: t("calls.calls"),
    //   icon: (active: boolean) => (
    //     <div app-history-regular-icon="">
    //       <svg
    //         width={17}
    //         height={17}
    //         fill={active ? "white" : "#b5b5b5"}
    //         xmlns="http://www.w3.org/2000/svg"
    //         viewBox="0 0 512 512"
    //       >
    //         <path d="M504 255.532c.252 136.64-111.182 248.372-247.822 248.468-64.014.045-122.373-24.163-166.394-63.942-5.097-4.606-5.3-12.543-.443-17.4l16.96-16.96c4.529-4.529 11.776-4.659 16.555-.395C158.208 436.843 204.848 456 256 456c110.549 0 200-89.468 200-200 0-110.549-89.468-200-200-200-55.52 0-105.708 22.574-141.923 59.043l49.091 48.413c7.641 7.535 2.305 20.544-8.426 20.544H26.412c-6.627 0-12-5.373-12-12V45.443c0-10.651 12.843-16.023 20.426-8.544l45.097 44.474C124.866 36.067 187.15 8 256 8c136.811 0 247.747 110.781 248 247.532zm-167.058 90.173 14.116-19.409c3.898-5.36 2.713-12.865-2.647-16.763L280 259.778V116c0-6.627-5.373-12-12-12h-24c-6.627 0-12 5.373-12 12v168.222l88.179 64.13c5.36 3.897 12.865 2.712 16.763-2.647z" />
    //       </svg>
    //     </div>
    //   ),
    //   url: "/calls",
    // },
  ];
};
