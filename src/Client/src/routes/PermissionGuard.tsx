

import { useGetAdminMenus } from "@/apps/Layout/Components/Sidebar/ServerSideStates";
import { Navigate, Outlet, useLocation } from "react-router-dom";

const ValidateUserPermission = () => {
  const userPermissions = useGetAdminMenus();
  const location = useLocation();
  const currentPath = location.pathname.toLowerCase();

  const userUrls: string[] = userPermissions?.data?.Value?.map(
    (permission: any) => permission?.Url?.toLowerCase()
  ) || [];

  const routeGroups: Record<string, string[]> = {
    "/panel/users": ["/panel/add-user", "/panel/edit-user"],
    "/panel/customers": ["/panel/add-customer", "/panel/edit-customer", "/panel/edit-temp-customer"],
    "/panel/import-data": ["/panel/add-import-data"],
    "/panel/auto-dailer": ["/panel/add-auto-dialer", "/panel/edit-auto-dialer"],
  };



  if (userUrls.includes(currentPath)) {
    return <Outlet />;
  }

  for (const [mainUrl, subUrls] of Object.entries(routeGroups)) {
    const hasParentPermission = userUrls.includes(mainUrl);
    const matchesSubPath = subUrls.some((sub) =>
      currentPath.startsWith(sub)
    );

    if (hasParentPermission && matchesSubPath) {
      return <Outlet />;
    }
  }

  
  return <Navigate to="/forbidden" />;
};

export default ValidateUserPermission;
