### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### Test müşterisi oluştur
# @name createCustomer
POST {{baseUrl}}/api/v1/customers/customers
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FirstName": "Test",
    "LastName": "Customer",
    "Email": "<EMAIL>",
    "Phone": "**********",
    "PhonePrefix": "+90"
}

###

@customerId = {{createCustomer.response.body}}

### Adres Listesi - Müşterinin tüm adresleri
GET {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}

### Yeni Ad<PERSON> Oluşturma - Tam bilgilerle
# @name createAddress
POST {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "Ev Adresi",
    "Phone": "**********",
    "PhonePrefix": "+90",
    "Email": "<EMAIL>",
    "Country": "Türkiye",
    "State": "İstanbul",
    "City": "Kadıköy",
    "Province": "Moda",
    "PostCode": "34710",
    "Detail": "Apartman No: 15, Daire: 3, Kat: 2, Kapı kodu: 1234",
    "IsDefault": true
}

###

@addressId = {{createAddress.response.body}}

### Yeni Adres Oluşturma - Minimal bilgilerle
# @name createMinimalAddress
POST {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "İş Adresi",
    "Country": "Türkiye",
    "City": "Ankara",
    "Detail": "Ofis binası 5. kat",
    "IsDefault": false
}

###

@minimalAddressId = {{createMinimalAddress.response.body}}

### Adres Güncelleme - Detail alanı ile
PUT {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/{{addressId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "Güncellenmiş Ev Adresi",
    "Phone": "5559876543",
    "PhonePrefix": "+90",
    "Email": "<EMAIL>",
    "Country": "Türkiye",
    "State": "İstanbul",
    "City": "Beşiktaş",
    "Province": "Levent",
    "PostCode": "34394",
    "Detail": "Güncellenmiş detay: Plaza No: 42, Kat: 8, Ofis: 801, Güvenlik kodu: 5678",
    "IsDefault": true
}

### Adres Güncelleme - Sadece Detail alanı
PUT {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/{{minimalAddressId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "İş Adresi",
    "Country": "Türkiye",
    "City": "Ankara",
    "Detail": "Yeni ofis binası, 10. kat, 1005 numaralı oda, asansör kartı gerekli",
    "IsDefault": false
}

### Güncellenmiş Adres Listesi - Detail alanlarını kontrol et
GET {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}

### Adres Silme
DELETE {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/{{minimalAddressId}}
Authorization: Bearer {{token}}

### Adres Silme - Ana adres
DELETE {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/{{addressId}}
Authorization: Bearer {{token}}

### Son Adres Listesi - Silme işlemlerini kontrol et
GET {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}

### Hata Durumu Testleri

### Olmayan müşteri için adres oluşturma
POST {{baseUrl}}/api/v1/customers/customers/00000000-0000-0000-0000-000000000000/addresses
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "Test Adresi",
    "Country": "Türkiye",
    "City": "İstanbul",
    "Detail": "Test detayı",
    "IsDefault": false
}

### Olmayan adres güncelleme
PUT {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "Olmayan Adres",
    "Country": "Türkiye",
    "City": "İstanbul",
    "Detail": "Bu adres mevcut değil",
    "IsDefault": false
}

### Olmayan adres silme
DELETE {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Geçersiz veri ile adres oluşturma (boş title)
POST {{baseUrl}}/api/v1/customers/customers/{{customerId}}/addresses
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Title": "",
    "Country": "Türkiye",
    "City": "İstanbul",
    "Detail": "Boş title ile test",
    "IsDefault": false
}

### Test müşterisini temizle
DELETE {{baseUrl}}/api/v1/customers/customers/{{customerId}}
Authorization: Bearer {{token}}
