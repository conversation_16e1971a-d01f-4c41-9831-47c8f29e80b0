# RFC-006: Rol Yönetim Sistemi

## Durum
Tamamlandı

## Özet
Bu RFC, MasterCRM sisteminde rol tabanlı yetkilendirme için gerekli olan rol yönetim sistemini tanımlar. <PERSON><PERSON><PERSON>, rollerin olu<PERSON>ul<PERSON>ı, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, silin<PERSON>i ve kullanıcılara atanması için gerekli API endpoint'lerini içerir. ASP.NET Core Identity'nin RoleManager sınıfı kullanılarak rol işlemleri gerçekleştirilecektir.

## Motivasyon
MasterCRM'de kullanıcıların sistem içindeki yetkilerini etkin bir şekilde yönetmek için rol tabanlı bir yetkilendirme sistemi gereklidir. Bu sistem sayesinde:

- Kullanıcılara belirli roller atanabilecek
- Sistem genelinde tutarlı bir yetkilendirme mekanizması sağlanacak
- <PERSON><PERSON> de<PERSON>iklikleri merkezi olarak yönetilebilecek
- Kullanıcı grupları oluşturularak toplu yetkilendirme yapılabilecek

## Teknik Tasarım

### Klasör Yapısı

```
src/
└── Modules/
    └── Users/
        ├── Application/
        │   └── Roles/
        │       ├── CreateRole/
        │       │   ├── CreateRoleCommand.cs
        │       │   ├── CreateRoleCommandHandler.cs
        │       │   ├── CreateRoleCommandValidator.cs
        │       │   └── CreateRoleEndpoint.cs
        │       ├── UpdateRole/
        │       │   ├── UpdateRoleCommand.cs
        │       │   ├── UpdateRoleCommandHandler.cs
        │       │   ├── UpdateRoleCommandValidator.cs
        │       │   └── UpdateRoleEndpoint.cs
        │       ├── DeleteRole/
        │       │   ├── DeleteRoleCommand.cs
        │       │   ├── DeleteRoleCommandHandler.cs
        │       │   └── DeleteRoleEndpoint.cs
        │       ├── GetRoleById/
        │       │   ├── GetRoleByIdQuery.cs
        │       │   ├── GetRoleByIdQueryHandler.cs
        │       │   └── GetRoleByIdEndpoint.cs
        │       └── GetRolesList/
        │           ├── GetRolesListQuery.cs
        │           ├── GetRolesListQueryHandler.cs
        │           └── GetRolesListEndpoint.cs
```

### API Endpoints Listesi

```
GET    /api/v1/users/roles                 - Tüm rolleri listeler
POST   /api/v1/users/roles                 - Yeni rol oluşturur
GET    /api/v1/users/roles/{id}            - Belirli bir rolün detaylarını getirir
PUT    /api/v1/users/roles/{id}            - Belirli bir rolü günceller
DELETE /api/v1/users/roles/{id}            - Belirli bir rolü siler
```

### Domain Model

Mevcut `Role.cs` sınıfı kullanılacaktır:

```csharp
using Microsoft.AspNetCore.Identity;
using Shared.Domain;

namespace Users.Domain.Account;

public class Role : IdentityRole<Guid>, IEntity
{
    public List<UserRole>? UserRole { get; set; }
    public static Guid ADMIN => new("cdb7ce66-3bfa-4f57-916c-199b4d1e60aa");
    public static Guid USER => new("7fcc72f4-b1c9-4f3a-9225-c9e6f1705be5");
    private readonly List<IDomainEvent> _domainEvents = [];

    public List<IDomainEvent> DomainEvents => [.. _domainEvents];

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    public void Raise(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
```

### CQRS Implementation

#### Commands

##### CreateRole

```csharp
// CreateRoleCommand.cs
public class CreateRoleCommand : IRequest<Result<Guid>>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

// CreateRoleCommandHandler.cs
public class CreateRoleCommandHandler : IRequestHandler<CreateRoleCommand, Result<Guid>>
{
    private readonly RoleManager<Role> _roleManager;

    public CreateRoleCommandHandler(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;
    }

    public async Task<Result<Guid>> Handle(CreateRoleCommand request, CancellationToken cancellationToken)
    {
        var role = new Role
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            NormalizedName = request.Name.ToUpper()
        };

        var result = await _roleManager.CreateAsync(role);

        if (!result.Succeeded)
        {
            return Result.Failure<Guid>(new ValidationError(
                result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
        }

        // Domain event oluştur
        role.Raise(new RoleCreatedDomainEvent(role.Id));

        return Result.Success(role.Id);
    }
}

// CreateRoleCommandValidator.cs
public class CreateRoleCommandValidator : AbstractValidator<CreateRoleCommand>
{
    private readonly RoleManager<Role> _roleManager;

    public CreateRoleCommandValidator(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Rol adı boş olamaz.")
            .MaximumLength(100).WithMessage("Rol adı en fazla 100 karakter olabilir.")
            .MustAsync(BeUniqueName).WithMessage("Bu rol adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueName(string name, CancellationToken cancellationToken)
    {
        return await _roleManager.FindByNameAsync(name) == null;
    }
}
```

##### UpdateRole

```csharp
// UpdateRoleCommand.cs
public class UpdateRoleCommand : IRequest<Result>
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
}

// UpdateRoleCommandHandler.cs
public class UpdateRoleCommandHandler : IRequestHandler<UpdateRoleCommand, Result>
{
    private readonly RoleManager<Role> _roleManager;

    public UpdateRoleCommandHandler(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;
    }

    public async Task<Result> Handle(UpdateRoleCommand request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());

        if (role == null)
        {
            return Result.Failure("Rol bulunamadı.");
        }

        // Admin ve User rollerini güncellemeye izin verme
        if (role.Id == Role.ADMIN || role.Id == Role.USER)
        {
            return Result.Failure("Sistem rolleri güncellenemez.");
        }

        role.Name = request.Name;
        role.NormalizedName = request.Name.ToUpper();

        var result = await _roleManager.UpdateAsync(role);

        if (!result.Succeeded)
        {
            return Result.Failure(new ValidationError(
                result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
        }

        // Domain event oluştur
        role.Raise(new RoleUpdatedDomainEvent(role.Id));

        return Result.Success();
    }
}

// UpdateRoleCommandValidator.cs
public class UpdateRoleCommandValidator : AbstractValidator<UpdateRoleCommand>
{
    private readonly RoleManager<Role> _roleManager;

    public UpdateRoleCommandValidator(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Rol ID'si boş olamaz.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Rol adı boş olamaz.")
            .MaximumLength(100).WithMessage("Rol adı en fazla 100 karakter olabilir.")
            .MustAsync(BeUniqueNameForOtherRoles).WithMessage("Bu rol adı zaten kullanılıyor.");
    }

    private async Task<bool> BeUniqueNameForOtherRoles(UpdateRoleCommand command, string name, CancellationToken cancellationToken)
    {
        var existingRole = await _roleManager.FindByNameAsync(name);
        return existingRole == null || existingRole.Id.ToString() == command.Id.ToString();
    }
}
```

##### DeleteRole

```csharp
// DeleteRoleCommand.cs
public class DeleteRoleCommand : IRequest<Result>
{
    public Guid Id { get; set; }
}

// DeleteRoleCommandHandler.cs
public class DeleteRoleCommandHandler : IRequestHandler<DeleteRoleCommand, Result>
{
    private readonly RoleManager<Role> _roleManager;

    public DeleteRoleCommandHandler(RoleManager<Role> roleManager)
    {
        _roleManager = roleManager;
    }

    public async Task<Result> Handle(DeleteRoleCommand request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());

        if (role == null)
        {
            return Result.Failure("Rol bulunamadı.");
        }

        // Admin ve User rollerini silmeye izin verme
        if (role.Id == Role.ADMIN || role.Id == Role.USER)
        {
            return Result.Failure("Sistem rolleri silinemez.");
        }

        var result = await _roleManager.DeleteAsync(role);

        if (!result.Succeeded)
        {
            return Result.Failure(new ValidationError(
                result.Errors.Select(e => Error.Problem(e.Code, e.Description)).ToArray()));
        }

        return Result.Success();
    }
}
```

#### Queries

##### GetRoleById

```csharp
// GetRoleByIdQuery.cs
public class GetRoleByIdQuery : IRequest<Result<RoleDto>>
{
    public Guid Id { get; set; }
}

// RoleDto.cs
public class RoleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int UserCount { get; set; }
}

// GetRoleByIdQueryHandler.cs
public class GetRoleByIdQueryHandler : IRequestHandler<GetRoleByIdQuery, Result<RoleDto>>
{
    private readonly RoleManager<Role> _roleManager;
    private readonly IUserDbContext _dbContext;

    public GetRoleByIdQueryHandler(RoleManager<Role> roleManager, IUserDbContext dbContext)
    {
        _roleManager = roleManager;
        _dbContext = dbContext;
    }

    public async Task<Result<RoleDto>> Handle(GetRoleByIdQuery request, CancellationToken cancellationToken)
    {
        var role = await _roleManager.FindByIdAsync(request.Id.ToString());

        if (role == null)
        {
            return Result.Failure<RoleDto>("Rol bulunamadı.");
        }

        // Kullanıcı sayısını hesapla
        var userCount = await _dbContext.UserRoles
            .CountAsync(ur => ur.RoleId == role.Id, cancellationToken);

        var roleDto = new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            UserCount = userCount
        };

        return Result.Success(roleDto);
    }
}
```

##### GetRolesList

```csharp
// GetRolesListQuery.cs
public class GetRolesListQuery : IRequest<Result<List<RoleListDto>>>
{
}

// RoleListDto.cs
public class RoleListDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public bool IsSystemRole { get; set; }
}

// GetRolesListQueryHandler.cs
public class GetRolesListQueryHandler : IRequestHandler<GetRolesListQuery, Result<List<RoleListDto>>>
{
    private readonly RoleManager<Role> _roleManager;
    private readonly IUserDbContext _dbContext;

    public GetRolesListQueryHandler(RoleManager<Role> roleManager, IUserDbContext dbContext)
    {
        _roleManager = roleManager;
        _dbContext = dbContext;
    }

    public async Task<Result<List<RoleListDto>>> Handle(GetRolesListQuery request, CancellationToken cancellationToken)
    {
        var roles = await _roleManager.Roles.ToListAsync(cancellationToken);

        // Kullanıcı sayılarını hesapla
        var userCounts = await _dbContext.UserRoles
            .GroupBy(ur => ur.RoleId)
            .Select(g => new { RoleId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.RoleId, x => x.Count, cancellationToken);

        var roleDtos = roles.Select(r => new RoleListDto
        {
            Id = r.Id,
            Name = r.Name,
            UserCount = userCounts.ContainsKey(r.Id) ? userCounts[r.Id] : 0,
            IsSystemRole = r.Id == Role.ADMIN || r.Id == Role.USER
        }).ToList();

        return Result.Success(roleDtos);
    }
}
```

### Endpoints

```csharp
// CreateRoleEndpoint.cs
internal sealed class CreateRoleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/users/roles", async (
            CreateRoleCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/users/roles/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Users.Roles")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}

// GetRoleByIdEndpoint.cs
internal sealed class GetRoleByIdEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/users/roles/{id}", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetRoleByIdQuery { Id = id };
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Users.Roles")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}

// GetRolesListEndpoint.cs
internal sealed class GetRolesListEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/users/roles", async (
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetRolesListQuery();
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Users.Roles")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}

// UpdateRoleEndpoint.cs
internal sealed class UpdateRoleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/users/roles/{id}", async (
            Guid id,
            UpdateRoleCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            if (id != command.Id)
            {
                return Results.BadRequest("ID uyuşmazlığı");
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                _ => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("Users.Roles")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}

// DeleteRoleEndpoint.cs
internal sealed class DeleteRoleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapDelete("/api/v1/users/roles/{id}", async (
            Guid id,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new DeleteRoleCommand { Id = id };
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                _ => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags("Users.Roles")
        .WithGroupName("apiv1")
        .RequireAuthorization("Users.Management");
    }
}
```

### Domain Events

```csharp
// RoleCreatedDomainEvent.cs
public record RoleCreatedDomainEvent(Guid RoleId) : IDomainEvent;

// RoleUpdatedDomainEvent.cs
public record RoleUpdatedDomainEvent(Guid RoleId) : IDomainEvent;

// RoleDeletedDomainEvent.cs
public record RoleDeletedDomainEvent(Guid RoleId) : IDomainEvent;
```

## Uygulama Stratejisi

1. Rol yönetim sistemi aşağıdaki sırayla uygulanacaktır:
   - Domain Events tanımlanması
   - Command ve Query sınıflarının oluşturulması
   - Endpoint'lerin uygulanması
   - Validasyon kurallarının eklenmesi
   - Birim testlerin yazılması

2. Güvenlik Değerlendirmesi:
   - Tüm rol yönetim endpoint'leri "ManageUsers" yetkisine sahip kullanıcılar tarafından erişilebilir olacaktır
   - Sistem rolleri (Admin, User) özel koruma altında olacak ve değiştirilemeyecek veya silinemeyecektir

## Alternatif Değerlendirmeler

1. Özel Rol Veritabanı Tablosu vs ASP.NET Identity
   - ASP.NET Identity'nin RoleManager sınıfı seçildi çünkü:
     - Hazır CRUD işlemleri sağlıyor
     - Mevcut kimlik sistemi ile entegre çalışıyor
     - Kod tekrarını önlüyor

2. Rol-İzin İlişkisi
   - Şu aşamada rol-izin ilişkisi uygulanmayacak, roller sadece gruplandırma amaçlı kullanılacak
   - İleride gerekirse, rol-izin ilişkisi için ayrı bir RFC hazırlanacak