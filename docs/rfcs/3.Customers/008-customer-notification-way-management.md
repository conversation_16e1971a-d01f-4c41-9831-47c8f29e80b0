# RFC 008: Müşteri Bildirim Kanalı Yönetimi

## Durum
- **Durum**: Taslak

## Özet
Bu RFC, MasterCRM sisteminde müşteri bildirim kanallarının (NotificationWay) temel CRUD işlemlerini yönetmeye yönelik basit bir altyapının tasarımını tanımlar. NotificationWay entity'si sadece Id ve Name alanlarını içerir ve temel oluşturma, okuma, güncelleme ve silme işlemlerini destekler.

## Motivasyon
- Sistemde farklı bildirim kanallarının tanımlanması ihtiyacı (E-posta, SMS, WhatsApp vb.)
- Bildirim kanallarının merkezi yönetimi
- Basit ve anlaşılır veri yapısı ile hızlı geliştirme

## Detaylı Tasarım

### Veritabanı Tasarımı

```sql
-- Customers.NotificationWay
CREATE TABLE Customers.NotificationWay (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    CONSTRAINT UQ_NotificationWay_Name UNIQUE (Name)
);
```

### Klasör Yapısı

```
src/
├── Modules/
│   ├── Customers/
│   │   ├── Application/
│   │   │   └── NotificationWays/
│   │   │       ├── CreateNotificationWay/
│   │   │       │   ├── CreateNotificationWayCommand.cs
│   │   │       │   ├── CreateNotificationWayCommandHandler.cs
│   │   │       │   ├── CreateNotificationWayCommandValidator.cs
│   │   │       │   └── CreateNotificationWayEndpoint.cs
│   │   │       ├── UpdateNotificationWay/
│   │   │       │   ├── UpdateNotificationWayCommand.cs
│   │   │       │   ├── UpdateNotificationWayCommandHandler.cs
│   │   │       │   ├── UpdateNotificationWayCommandValidator.cs
│   │   │       │   └── UpdateNotificationWayEndpoint.cs
│   │   │       ├── GetNotificationWay/
│   │   │       │   ├── GetNotificationWayQuery.cs
│   │   │       │   ├── GetNotificationWayQueryHandler.cs
│   │   │       │   └── GetNotificationWayEndpoint.cs
│   │   │       ├── ListNotificationWays/
│   │   │       │   ├── ListNotificationWaysQuery.cs
│   │   │       │   ├── ListNotificationWaysQueryHandler.cs
│   │   │       │   └── ListNotificationWaysEndpoint.cs
│   │   │       └── DeleteNotificationWay/
│   │   │           ├── DeleteNotificationWayCommand.cs
│   │   │           ├── DeleteNotificationWayCommandHandler.cs
│   │   │           └── DeleteNotificationWayEndpoint.cs
│   │   └── Infrastructure/
│   │       └── Data/
│   │           ├── CustomersDbContext.cs
│   │           └── Configurations/
│   │               └── NotificationWayConfiguration.cs
```

### Entity Modeli

```csharp
// Modules/Customers/Domain/NotificationWay.cs
namespace MasterCRM.Modules.Customers.Domain;

public class NotificationWay
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
}
```

### Entity Configuration

```csharp
// Modules/Customers/Infrastructure/Data/Configurations/NotificationWayConfiguration.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MasterCRM.Modules.Customers.Domain;

namespace MasterCRM.Modules.Customers.Infrastructure.Data.Configurations;

internal sealed class NotificationWayConfiguration : IEntityTypeConfiguration<NotificationWay>
{
    public void Configure(EntityTypeBuilder<NotificationWay> builder)
    {
        builder.ToTable("NotificationWay", "Customers");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasDefaultValueSql("NEWID()");

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.Name)
            .IsUnique()
            .HasDatabaseName("UQ_NotificationWay_Name");
    }
}
```

### CRUD İşlemleri

#### Create İşlemi

```csharp
// Modules/Customers/Application/NotificationWays/CreateNotificationWay/CreateNotificationWayCommand.cs
using MediatR;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.CreateNotificationWay;

public record CreateNotificationWayCommand(string Name) : IRequest<Result<Guid>>;

// CreateNotificationWayCommandHandler.cs
using MediatR;
using Microsoft.EntityFrameworkCore;
using MasterCRM.Modules.Customers.Infrastructure.Data;
using MasterCRM.Modules.Customers.Domain;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.CreateNotificationWay;

internal sealed class CreateNotificationWayCommandHandler : IRequestHandler<CreateNotificationWayCommand, Result<Guid>>
{
    private readonly CustomersDbContext _context;

    public CreateNotificationWayCommandHandler(CustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result<Guid>> Handle(CreateNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var existingNotificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Name == request.Name, cancellationToken);

        if (existingNotificationWay != null)
            return Result<Guid>.Failure("Bu isimde bir bildirim kanalı zaten mevcut");

        var notificationWay = new NotificationWay
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            InsertDate = DateTime.Now
        };

        _context.NotificationWays.Add(notificationWay);
        await _context.SaveChangesAsync(cancellationToken);

        return Result<Guid>.Success(notificationWay.Id);
    }
}

// CreateNotificationWayCommandValidator.cs
using FluentValidation;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.CreateNotificationWay;

internal sealed class CreateNotificationWayCommandValidator : AbstractValidator<CreateNotificationWayCommand>
{
    public CreateNotificationWayCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Bildirim kanalı adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Bildirim kanalı adı 100 karakteri geçemez");
    }
}

// CreateNotificationWayEndpoint.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MasterCRM.Modules.Customers.Application.NotificationWays.CreateNotificationWay;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.CreateNotificationWay;

[ApiController]
[Route("api/v1/customers/notification-ways")]
public class CreateNotificationWayEndpoint : ControllerBase
{
    private readonly IMediator _mediator;

    public CreateNotificationWayEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateNotificationWayCommand command)
    {
        var result = await _mediator.Send(command);

        if (result.IsSuccess)
            return CreatedAtAction(nameof(GetNotificationWayEndpoint.Get), new { id = result.Value }, result.Value);

        return BadRequest(result.Error);
    }
}
```

#### Read İşlemleri

```csharp
// Modules/Customers/Application/NotificationWays/GetNotificationWay/GetNotificationWayQuery.cs
using MediatR;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.GetNotificationWay;

public record GetNotificationWayQuery(Guid Id) : IRequest<Result<NotificationWayResponse>>;

public record NotificationWayResponse(Guid Id, string Name);

// GetNotificationWayQueryHandler.cs
using MediatR;
using Microsoft.EntityFrameworkCore;
using MasterCRM.Modules.Customers.Infrastructure.Data;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.GetNotificationWay;

internal sealed class GetNotificationWayQueryHandler : IRequestHandler<GetNotificationWayQuery, Result<NotificationWayResponse>>
{
    private readonly CustomersDbContext _context;

    public GetNotificationWayQueryHandler(CustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result<NotificationWayResponse>> Handle(GetNotificationWayQuery request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .Where(nw => nw.Id == request.Id)
            .Select(nw => new NotificationWayResponse(nw.Id, nw.Name))
            .FirstOrDefaultAsync(cancellationToken);

        if (notificationWay == null)
            return Result<NotificationWayResponse>.Failure("Bildirim kanalı bulunamadı");

        return Result<NotificationWayResponse>.Success(notificationWay);
    }
}

// GetNotificationWayEndpoint.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MasterCRM.Modules.Customers.Application.NotificationWays.GetNotificationWay;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.GetNotificationWay;

[ApiController]
[Route("api/v1/customers/notification-ways")]
public class GetNotificationWayEndpoint : ControllerBase
{
    private readonly IMediator _mediator;

    public GetNotificationWayEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> Get(Guid id)
    {
        var result = await _mediator.Send(new GetNotificationWayQuery(id));

        if (result.IsSuccess)
            return Ok(result.Value);

        return NotFound(result.Error);
    }
}
```

#### Update İşlemi

```csharp
// Modules/Customers/Application/NotificationWays/UpdateNotificationWay/UpdateNotificationWayCommand.cs
using MediatR;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.UpdateNotificationWay;

public record UpdateNotificationWayCommand(Guid Id, string Name) : IRequest<Result>;

// UpdateNotificationWayCommandHandler.cs
using MediatR;
using Microsoft.EntityFrameworkCore;
using MasterCRM.Modules.Customers.Infrastructure.Data;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.UpdateNotificationWay;

internal sealed class UpdateNotificationWayCommandHandler : IRequestHandler<UpdateNotificationWayCommand, Result>
{
    private readonly CustomersDbContext _context;

    public UpdateNotificationWayCommandHandler(CustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(UpdateNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Id == request.Id, cancellationToken);

        if (notificationWay == null)
            return Result.Failure("Bildirim kanalı bulunamadı");

        var existingWithSameName = await _context.NotificationWays
            .AnyAsync(nw => nw.Name == request.Name && nw.Id != request.Id, cancellationToken);

        if (existingWithSameName)
            return Result.Failure("Bu isimde başka bir bildirim kanalı zaten mevcut");

        notificationWay.Name = request.Name;
        notificationWay.UpdateDate = DateTime.Now;

        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

// UpdateNotificationWayCommandValidator.cs
using FluentValidation;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.UpdateNotificationWay;

internal sealed class UpdateNotificationWayCommandValidator : AbstractValidator<UpdateNotificationWayCommand>
{
    public UpdateNotificationWayCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Bildirim kanalı adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Bildirim kanalı adı 100 karakteri geçemez");
    }
}

// UpdateNotificationWayEndpoint.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MasterCRM.Modules.Customers.Application.NotificationWays.UpdateNotificationWay;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.UpdateNotificationWay;

[ApiController]
[Route("api/v1/customers/notification-ways")]
public class UpdateNotificationWayEndpoint : ControllerBase
{
    private readonly IMediator _mediator;

    public UpdateNotificationWayEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateNotificationWayRequest request)
    {
        var command = new UpdateNotificationWayCommand(id, request.Name);
        var result = await _mediator.Send(command);

        if (result.IsSuccess)
            return NoContent();

        return BadRequest(result.Error);
    }
}

public record UpdateNotificationWayRequest(string Name);
```

#### Delete İşlemi

```csharp
// Modules/Customers/Application/NotificationWays/DeleteNotificationWay/DeleteNotificationWayCommand.cs
using MediatR;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.DeleteNotificationWay;

public record DeleteNotificationWayCommand(Guid Id) : IRequest<Result>;

// DeleteNotificationWayCommandHandler.cs
using MediatR;
using Microsoft.EntityFrameworkCore;
using MasterCRM.Modules.Customers.Infrastructure.Data;
using MasterCRM.Shared.Application.Results;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.DeleteNotificationWay;

internal sealed class DeleteNotificationWayCommandHandler : IRequestHandler<DeleteNotificationWayCommand, Result>
{
    private readonly CustomersDbContext _context;

    public DeleteNotificationWayCommandHandler(CustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(DeleteNotificationWayCommand request, CancellationToken cancellationToken)
    {
        var notificationWay = await _context.NotificationWays
            .FirstOrDefaultAsync(nw => nw.Id == request.Id, cancellationToken);

        if (notificationWay == null)
            return Result.Failure("Bildirim kanalı bulunamadı");

        _context.NotificationWays.Remove(notificationWay);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

// DeleteNotificationWayEndpoint.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MasterCRM.Modules.Customers.Application.NotificationWays.DeleteNotificationWay;

namespace MasterCRM.Modules.Customers.Application.NotificationWays.DeleteNotificationWay;

[ApiController]
[Route("api/v1/customers/notification-ways")]
public class DeleteNotificationWayEndpoint : ControllerBase
{
    private readonly IMediator _mediator;

    public DeleteNotificationWayEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        var result = await _mediator.Send(new DeleteNotificationWayCommand(id));

        if (result.IsSuccess)
            return NoContent();

        return BadRequest(result.Error);
    }
}
```

### API Endpoints

```
GET    /api/v1/customers/notification-ways        - Tüm bildirim kanallarını listele
POST   /api/v1/customers/notification-ways        - Yeni bildirim kanalı oluştur
GET    /api/v1/customers/notification-ways/{id}   - Belirli bildirim kanalını getir
PUT    /api/v1/customers/notification-ways/{id}   - Bildirim kanalını güncelle
DELETE /api/v1/customers/notification-ways/{id}   - Bildirim kanalını sil
```

## Uygulama Stratejisi

1. **Faz 1 (1 hafta):**
   - Veritabanı tablosunun oluşturulması
   - Entity ve Configuration sınıflarının yazılması
   - DbContext'e eklenmesi

2. **Faz 2 (1 hafta):**
   - CRUD işlemlerinin geliştirilmesi
   - Command/Query handler'larının yazılması
   - Validation kurallarının eklenmesi

3. **Faz 3 (0.5 hafta):**
   - API endpoint'lerinin oluşturulması
   - Test edilmesi ve dokümantasyonun hazırlanması

## Başarı Metrikleri
- Tüm CRUD işlemleri sorunsuz çalışmalı
- API response time < 100ms
- Unique constraint ihlalleri uygun şekilde handle edilmeli
- Validation kuralları doğru çalışmalı

## Riskler ve Önlemler
1. **Veri Tutarlılığı:**
   - Unique constraint ile aynı isimde birden fazla kayıt önlenmesi

2. **Performans:**
   - Name alanı için index oluşturulması

## Bağımlılıklar
- CustomersDbContext
- MediatR
- FluentValidation
- Shared.Application.Results