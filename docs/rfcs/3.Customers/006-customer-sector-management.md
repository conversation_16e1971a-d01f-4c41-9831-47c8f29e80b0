# RFC-006: Customer Sector Management

## Status
Tammalandı

## Özet

Bu RFC, MasterCRM sisteminde müşteri sektör yönetimi modülünün tasarımını ve implementasyonunu tanımlar. Müşterilerin sektör bazında kategorize edilmesi, filtrelenmesi ve raporlanması için gerekli veri modeli ve API endpoint'lerini içerir.

## Motivasyon

Müşterilerin sektörlere göre sınıflandırılması, işletmelerin daha etkili müşteri segmentasyonu yapmasını, sektöre özel kampanyalar planlamasını ve sektörel analitik raporlar üretmesini sağlar. Bu modül sayesinde:

- Müşteriler sektörlere göre gruplandırılabilecek
- Sektör bazlı müşteri filtreleme yapılabilecek
- Sektörel performans analizleri gerçekleştirilebilecek
- Sektöre özel pazarlama stratejileri geliştirilebilecek

## Tasarım

### Klasör Yapısı

```
MasterCRM/
├── src/
│   ├── Modules/
│   │   ├── Customers/
│   │   │   ├── Application/
│   │   │   │   ├── Sectors/
│   │   │   │   │   ├── CreateSector/
│   │   │   │   │   │   ├── CreateSectorCommand.cs
│   │   │   │   │   │   ├── CreateSectorCommandHandler.cs
│   │   │   │   │   │   ├── CreateSectorCommandValidator.cs
│   │   │   │   │   │   └── CreateSectorEndpoint.cs
│   │   │   │   │   ├── DeleteSector/
│   │   │   │   │   │   ├── DeleteSectorCommand.cs
│   │   │   │   │   │   ├── DeleteSectorCommandHandler.cs
│   │   │   │   │   │   └── DeleteSectorEndpoint.cs
│   │   │   │   │   ├── GetSector/
│   │   │   │   │   │   ├── GetSectorQuery.cs
│   │   │   │   │   │   ├── GetSectorQueryHandler.cs
│   │   │   │   │   │   └── GetSectorEndpoint.cs
│   │   │   │   │   ├── GetSectorsList/
│   │   │   │   │   │   ├── GetSectorsListQuery.cs
│   │   │   │   │   │   ├── GetSectorsListQueryHandler.cs
│   │   │   │   │   │   └── GetSectorsListEndpoint.cs
│   │   │   │   │   └── UpdateSector/
│   │   │   │   │       ├── UpdateSectorCommand.cs
│   │   │   │   │       ├── UpdateSectorCommandHandler.cs
│   │   │   │   │       ├── UpdateSectorCommandValidator.cs
│   │   │   │   │       └── UpdateSectorEndpoint.cs
│   │   │   │   └── Common/
│   │   │   │       └── DTOs/
│   │   │   │           └── SectorDto.cs
│   │   │   ├── Domain/
│   │   │   │   └── Entities/
│   │   │   │       └── Sector.cs
│   │   │   └── Infrastructure/
│   │   │       └── Data/
│   │   │           └── CustomerDbContext.cs
```

### Veri Modeli

```sql
-- Sectors Table
CREATE TABLE Customers.Sector (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    Name NVARCHAR(100) NOT NULL
);

-- Customer-Sector Relationship
ALTER TABLE Customers.Customer
ADD SectorId UNIQUEIDENTIFIER NULL;

ALTER TABLE Customers.Customer
ADD CONSTRAINT FK_Customer_Sector FOREIGN KEY (SectorId)
REFERENCES Customers.Sector (Id);
```

### Domain Models

```csharp
namespace MasterCRM.Modules.Customers.Domain.Entities
{
    public class Sector : BaseAuditableEntity
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;

        // Navigation property
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }
}

// Customer entity'sine eklenecek property
namespace MasterCRM.Modules.Customers.Domain.Entities
{
    public partial class Customer
    {
        public Guid? SectorId { get; set; }
        public virtual Sector? Sector { get; set; }
    }
}
```

### CQRS Implementation

#### Commands

##### CreateSector

```csharp
// CreateSectorCommand.cs
public class CreateSectorCommand : IRequest<Result<Guid>>
{
    public string Name { get; set; } = string.Empty;
}

// CreateSectorCommandHandler.cs
public class CreateSectorCommandHandler : IRequestHandler<CreateSectorCommand, Result<Guid>>
{
    private readonly ICustomersDbContext _context;

    public CreateSectorCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result<Guid>> Handle(CreateSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = new Sector
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            InsertDate = DateTime.Now
        };

        _context.Sectors.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success(entity.Id);
    }
}

// CreateSectorCommandValidator.cs
public class CreateSectorCommandValidator : AbstractValidator<CreateSectorCommand>
{
    public CreateSectorCommandValidator()
    {
        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Sektör adı boş olamaz.")
            .MaximumLength(100).WithMessage("Sektör adı 100 karakterden uzun olamaz.");
    }
}
```

##### UpdateSector

```csharp
// UpdateSectorCommand.cs
public class UpdateSectorCommand : IRequest<Result>
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

// UpdateSectorCommandHandler.cs
public class UpdateSectorCommandHandler : IRequestHandler<UpdateSectorCommand, Result>
{
    private readonly ICustomersDbContext _context;

    public UpdateSectorCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(UpdateSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = await _context.Sectors.FindAsync(new object[] { request.Id }, cancellationToken);

        if (entity == null)
        {
            return Result.Failure($"Sektör bulunamadı (ID: {request.Id})");
        }

        entity.Name = request.Name;
        entity.Description = request.Description;
        entity.IsActive = request.IsActive;
        entity.UpdateDate = DateTime.Now;

        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

// UpdateSectorCommandValidator.cs
public class UpdateSectorCommandValidator : AbstractValidator<UpdateSectorCommand>
{
    public UpdateSectorCommandValidator()
    {
        RuleFor(v => v.Id)
            .NotEmpty().WithMessage("ID boş olamaz.");

        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Sektör adı boş olamaz.")
            .MaximumLength(100).WithMessage("Sektör adı 100 karakterden uzun olamaz.");

        RuleFor(v => v.Description)
            .MaximumLength(500).WithMessage("Açıklama 500 karakterden uzun olamaz.");
    }
}
```

##### DeleteSector

```csharp
// DeleteSectorCommand.cs
public class DeleteSectorCommand : IRequest<Result>
{
    public Guid Id { get; set; }
}

// DeleteSectorCommandHandler.cs
public class DeleteSectorCommandHandler : IRequestHandler<DeleteSectorCommand, Result>
{
    private readonly ICustomersDbContext _context;

    public DeleteSectorCommandHandler(ICustomersDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(DeleteSectorCommand request, CancellationToken cancellationToken)
    {
        var entity = await _context.Sectors.FindAsync(new object[] { request.Id }, cancellationToken);

        if (entity == null)
        {
            return Result.Failure($"Sektör bulunamadı (ID: {request.Id})");
        }

        // Check if any customers are using this sector
        var hasCustomers = await _context.Customers
            .AnyAsync(c => c.SectorId == request.Id, cancellationToken);

        if (hasCustomers)
        {
            return Result.Failure("Bu sektöre atanmış müşteriler olduğu için silinemez.");
        }

        _context.Sectors.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
```

#### Queries

##### GetSectorById

```csharp
// GetSectorByIdQuery.cs
public class GetSectorByIdQuery : IRequest<Result<SectorDto>>
{
    public Guid Id { get; set; }
}

// GetSectorByIdQueryHandler.cs
public class GetSectorByIdQueryHandler : IRequestHandler<GetSectorByIdQuery, Result<SectorDto>>
{
    private readonly ICustomersDbContext _context;
    private readonly IMapper _mapper;

    public GetSectorByIdQueryHandler(ICustomersDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<Result<SectorDto>> Handle(GetSectorByIdQuery request, CancellationToken cancellationToken)
    {
        var entity = await _context.Sectors
            .FindAsync(new object[] { request.Id }, cancellationToken);

        if (entity == null)
        {
            return Result.Failure<SectorDto>($"Sektör bulunamadı (ID: {request.Id})");
        }

        return Result.Success(_mapper.Map<SectorDto>(entity));
    }
}
```

##### GetSectorsList

```csharp
// GetSectorsListQuery.cs
public class GetSectorsListQuery : IRequest<Result<List<SectorDto>>>
{
}

// GetSectorsListQueryHandler.cs
public class GetSectorsListQueryHandler : IRequestHandler<GetSectorsListQuery, Result<List<SectorDto>>>
{
    private readonly ICustomersDbContext _context;
    private readonly IMapper _mapper;

    public GetSectorsListQueryHandler(ICustomersDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<Result<List<SectorDto>>> Handle(GetSectorsListQuery request, CancellationToken cancellationToken)
    {
        var sectors = await _context.Sectors
            .OrderBy(s => s.Name)
            .ToListAsync(cancellationToken);

        return Result.Success(_mapper.Map<List<SectorDto>>(sectors));
    }
}
```

### DTOs

```csharp
// SectorDto.cs
public class SectorDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
}

// CustomerDto eklemeleri
public partial class CustomerDto
{
    public Guid? SectorId { get; set; }
    public string? SectorName { get; set; }
}
```

### API Endpoints

```csharp
// CreateSectorEndpoint.cs
[Route("api/v1/customers/sectors")]
public class CreateSectorEndpoint : EndpointBaseAsync
    .WithRequest<CreateSectorCommand>
    .WithActionResult<Guid>
{
    private readonly ISender _mediator;

    public CreateSectorEndpoint(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [Authorize(Policy = "CustomerManagement")]
    public override async Task<ActionResult<Guid>> HandleAsync(
        CreateSectorCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _mediator.Send(request, cancellationToken);
        return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Error);
    }
}

// GetSectorEndpoint.cs
[Route("api/v1/customers/sectors/{id}")]
public class GetSectorEndpoint : EndpointBaseAsync
    .WithRequest<Guid>
    .WithActionResult<SectorDto>
{
    private readonly ISender _mediator;

    public GetSectorEndpoint(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Authorize(Policy = "CustomerManagement")]
    public override async Task<ActionResult<SectorDto>> HandleAsync(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        var result = await _mediator.Send(new GetSectorByIdQuery { Id = id }, cancellationToken);
        return result.IsSuccess ? Ok(result.Value) : NotFound(result.Error);
    }
}

// GetSectorsListEndpoint.cs
[Route("api/v1/customers/sectors")]
public class GetSectorsListEndpoint : EndpointBaseAsync
    .WithoutRequest
    .WithActionResult<List<SectorDto>>
{
    private readonly ISender _mediator;

    public GetSectorsListEndpoint(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Authorize(Policy = "CustomerManagement")]
    public override async Task<ActionResult<List<SectorDto>>> HandleAsync(
        CancellationToken cancellationToken = default)
    {
        var result = await _mediator.Send(new GetSectorsListQuery(), cancellationToken);

        return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Error);
    }
}

// UpdateSectorEndpoint.cs
[Route("api/v1/customers/sectors/{id}")]
public class UpdateSectorEndpoint : EndpointBaseAsync
    .WithRequest<UpdateSectorCommand>
    .WithActionResult
{
    private readonly ISender _mediator;

    public UpdateSectorEndpoint(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpPut]
    [Authorize(Policy = "CustomerManagement")]
    public override async Task<ActionResult> HandleAsync(
        UpdateSectorCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _mediator.Send(request, cancellationToken);
        return result.IsSuccess ? NoContent() : BadRequest(result.Error);
    }
}

// DeleteSectorEndpoint.cs
[Route("api/v1/customers/sectors/{id}")]
public class DeleteSectorEndpoint : EndpointBaseAsync
    .WithRequest<Guid>
    .WithActionResult
{
    private readonly ISender _mediator;

    public DeleteSectorEndpoint(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpDelete]
    [Authorize(Policy = "CustomerManagement")]
    public override async Task<ActionResult> HandleAsync(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        var result = await _mediator.Send(new DeleteSectorCommand { Id = id }, cancellationToken);
        return result.IsSuccess ? NoContent() : BadRequest(result.Error);
    }
}
```



### Uygulama Aşamaları

1. Veritabanı değişikliklerinin yapılması
   - Sector tablosunun oluşturulması
   - Customer tablosuna SectorId foreign key eklenmesi

2. Domain entity ve repository katmanının oluşturulması
   - Sector entity sınıfının eklenmesi
   - Customer entity'sine SectorId property'sinin eklenmesi
   - ICustomersDbContext'e DbSet<Sector> eklenmesi

3. CQRS pattern ile komut ve sorguların oluşturulması
   - Create, Update, Delete komutları
   - GetById ve GetList sorguları

4. API endpoint'lerinin oluşturulması
   - Tüm CRUD operasyonları için endpoint'ler

5. Frontend bileşenlerinin geliştirilmesi
   - Sektör yönetim sayfası
   - Müşteri formlarına sektör seçeneği eklenmesi

6. Testlerin yazılması
   - Unit testler
   - Integration testler

### Test Stratejisi

#### Unit Tests

```csharp
// CreateSectorCommandHandlerTests.cs
public class CreateSectorCommandHandlerTests
{
    [Fact]
    public async Task Handle_ValidSector_ShouldCreateSectorAndReturnId()
    {
        // Arrange
        var context = CreateDbContext();
        var handler = new CreateSectorCommandHandler(context);
        var command = new CreateSectorCommand
        {
            Name = "Test Sector",
            Description = "Test Description",
            IsActive = true
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeEmpty();

        var createdSector = await context.Sectors.FindAsync(result.Value);
        createdSector.Should().NotBeNull();
        createdSector!.Name.Should().Be(command.Name);
        createdSector.Description.Should().Be(command.Description);
        createdSector.IsActive.Should().Be(command.IsActive);
    }
}

// GetSectorsListQueryHandlerTests.cs
public class GetSectorsListQueryHandlerTests
{
    [Fact]
    public async Task Handle_WithSectors_ShouldReturnAllSectors()
    {
        // Arrange
        var context = CreateDbContextWithSectors();
        var mapper = CreateMapper();
        var handler = new GetSectorsListQueryHandler(context, mapper);
        var query = new GetSectorsListQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.Should().HaveCount(3); // All sectors
    }
}

// DeleteSectorCommandHandlerTests.cs
public class DeleteSectorCommandHandlerTests
{
    [Fact]
    public async Task Handle_SectorWithCustomers_ShouldReturnFailure()
    {
        // Arrange
        var context = CreateDbContextWithSectorAndCustomers();
        var handler = new DeleteSectorCommandHandler(context);
        var command = new DeleteSectorCommand { Id = GetSectorIdWithCustomers() };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Error.Should().Contain("atanmış müşteriler");
    }

    [Fact]
    public async Task Handle_SectorWithoutCustomers_ShouldDeleteAndReturnSuccess()
    {
        // Arrange
        var context = CreateDbContextWithSectors();
        var handler = new DeleteSectorCommandHandler(context);
        var sectorId = GetSectorIdWithoutCustomers();
        var command = new DeleteSectorCommand { Id = sectorId };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        var deletedSector = await context.Sectors.FindAsync(sectorId);
        deletedSector.Should().BeNull();
    }
}
```

#### Integration Tests

```csharp
// SectorEndpointsTests.cs
public class SectorEndpointsTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public SectorEndpointsTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetSectors_ShouldReturnSectors()
    {
        // Arrange
        // Setup test database with sectors

        // Act
        var response = await _client.GetAsync("/api/v1/customers/sectors");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        var sectors = JsonSerializer.Deserialize<List<SectorDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        sectors.Should().NotBeNull();
        sectors!.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CreateSector_WithValidData_ShouldCreateSector()
    {
        // Arrange
        var sector = new CreateSectorCommand
        {
            Name = "New Test Sector",
            Description = "Description for test sector",
            IsActive = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/customers/sectors", sector);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        var sectorId = JsonSerializer.Deserialize<Guid>(content);

        sectorId.Should().NotBeEmpty();
    }
}
```

## Güvenlik Değerlendirmesi

Sektör yönetimi modülü için önemli güvenlik değerlendirmeleri:

1. Yetkilendirme
   - Sektör yönetimi fonksiyonlarına erişim "CustomerManagement" politikası ile sınırlandırılacak
   - Kullanıcı rolleri bazında erişim kısıtlamaları uygulanacak

2. Veri Doğrulama
   - Tüm input verileri FluentValidation ile doğrulanacak
   - XSS ve SQL injection saldırılarına karşı koruma sağlanacak

3. Audit Logging
   - Tüm sektör ekleme, düzenleme ve silme işlemleri log'lanacak
   - History alanı ile değişiklikler kaydedilecek

## Performans Değerlendirmesi

1. Indexleme
   - Sector tablosunda Name alanı için index oluşturulacak
   - Customer tablosunda SectorId için index oluşturulacak

2. Caching
   - Sektör listesi için caching mekanizması kullanılacak
   - Cache invalidation stratejisi uygulanacak

```sql
-- Name alanı için index
CREATE INDEX IX_Sector_Name ON Customers.Sector(Name);

-- Customer tablosunda SectorId için index
CREATE INDEX IX_Customer_SectorId ON Customers.Customer(SectorId);
```

## İlgili İş Akışları

Bu RFC kapsamında geliştirilen Sector yönetimi, aşağıdaki mevcut ve planlanan iş akışlarını etkileyecektir:

1. Müşteri Kayıt Süreci
   - Müşteri kaydı sırasında sektör seçimi eklenecek

2. Müşteri Filtreleme
   - Sektör bazlı müşteri filtreleme özelliği eklenecek

3. Raporlama ve Analitik
   - Sektör bazlı müşteri dağılımı raporları oluşturulacak
   - Sektör performans analizleri yapılabilecek

## Alternatif Yaklaşımlar

1. Hiyerarşik Sektör Yapısı
   - Sektörlerin alt-sektörler içerebileceği hiyerarşik bir yapı
   - ParentSectorId gibi self-referencing bir ilişki
   - Karar: İlk aşamada basit düz yapı tercih edildi, gerekirse ileride genişletilebilir

2. Çoklu Sektör Atama
   - Bir müşterinin birden fazla sektörde yer alabilmesi
   - Many-to-many ilişki ile CustomerSector ara tablosu
   - Karar: Şu aşamada tek sektör ataması yeterli görüldü

## Uygulama Zaman Çizelgesi

- 1. Gün: Veri modeli ve domain entity implementasyonu
- 2. Gün: CQRS commands ve queries implementasyonu
- 3. Gün: API endpoints implementasyonu
- 4. Gün: Frontend components implementasyonu
- 5. Gün: Unit ve integration testleri
- 6. Gün: Code review ve bug fixing
- 7. Gün: Dokümantasyon ve deployment

Toplam Süre: 1 hafta (7 gün)

## Sonuç

Bu RFC, MasterCRM sistemine Müşteri Sektör Yönetimi özelliğini ekleyerek müşterilerin sektörel olarak kategorize edilmesini sağlayacaktır. Bu sayede işletmeler müşterilerini daha etkili bir şekilde segmente edebilecek, sektöre özgü stratejiler geliştirebilecek ve sektörel analitik raporlar üretebilecektir.

Önerilen implementasyon, mevcut sistem mimarisi ve best practice'ler ile uyumlu olarak tasarlanmış olup, minimum teknik borç ile sürdürülebilir bir çözüm sunmaktadır.