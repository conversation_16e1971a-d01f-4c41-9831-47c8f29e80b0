# RFC 007: Customers Profession Management

## Status
Tammalandı

## Özet

Bu RFC, MasterCRM sisteminde mü<PERSON> (Customer) veri modeline meslek (Profession) yönetimi eklenmesini önermektedir. Öner<PERSON><PERSON>, müşterilerin mesleklerinin kategorize edilmesini ve müşteri verilerine dayalı analitik raporların geliştirilmesini sağlayacaktır.

## Motivasyon

MasterCRM sisteminde müşteriler hakkında kapsamlı bilgi tutulması, müşteri segmentasyonu ve hedefleme için kritik öneme sahiptir. Meslek bilgisi:

- Müşteri profili analizlerinde değerli bir veri noktasıdır
- Pazarlama kampanyalarının hedeflenmesini iyileştirir
- Müşteri segmentasyonu için önemli bir kriterdir
- İş akışlarında mesleğe özel süreçlerin tanımlanmasına olanak sağ<PERSON>

## Teknik Detaylar

### Veri Modeli Değişiklikleri

1. Yeni bir `Profession` varlığı eklenecektir:

```csharp
public class Profession
{
    public Guid Id { get; set; }
    public string Name { get; set; }

    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public Guid? UpdateUserId { get; set; }
    public string History { get; set; }
}
```

2. `Customer` varlığına `ProfessionId` alanı eklenecektir:

```csharp
public class Customer
{
    // Mevcut alanlar...

    public Guid? ProfessionId { get; set; }
    public virtual Profession Profession { get; set; }
}
```

### Veritabanı Değişiklikleri

```sql
-- Yeni tablo oluşturma
CREATE TABLE Customers.Profession (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX)
);

-- Customer tablosuna foreign key ekleme
ALTER TABLE Customers.Customer
ADD ProfessionId UNIQUEIDENTIFIER NULL;

ALTER TABLE Customers.Customer
ADD CONSTRAINT FK_Customer_Profession FOREIGN KEY (ProfessionId)
REFERENCES Customers.Profession (Id);

-- Index ekleme (performans için)
CREATE INDEX IX_Customer_ProfessionId ON Customers.Customer(ProfessionId);
```

### Klasör Yapısı

Profession yönetimi için aşağıdaki klasör yapısı oluşturulacaktır:

```
MasterCRM/
├── src/
│   ├── Modules/
│   │   ├── Customers/
│   │   │   ├── Application/
│   │   │   │   ├── Professions/
│   │   │   │   │   ├── CreateProfession/
│   │   │   │   │   │   ├── CreateProfessionCommand.cs
│   │   │   │   │   │   ├── CreateProfessionCommandHandler.cs
│   │   │   │   │   │   ├── CreateProfessionCommandValidator.cs
│   │   │   │   │   │   └── CreateProfessionEndpoint.cs
│   │   │   │   │   ├── UpdateProfession/
│   │   │   │   │   │   ├── UpdateProfessionCommand.cs
│   │   │   │   │   │   ├── UpdateProfessionCommandHandler.cs
│   │   │   │   │   │   ├── UpdateProfessionCommandValidator.cs
│   │   │   │   │   │   └── UpdateProfessionEndpoint.cs
│   │   │   │   │   ├── DeleteProfession/
│   │   │   │   │   │   ├── DeleteProfessionCommand.cs
│   │   │   │   │   │   ├── DeleteProfessionCommandHandler.cs
│   │   │   │   │   │   └── DeleteProfessionEndpoint.cs
│   │   │   │   │   ├── GetProfession/
│   │   │   │   │   │   ├── GetProfessionQuery.cs
│   │   │   │   │   │   ├── GetProfessionQueryHandler.cs
│   │   │   │   │   │   └── GetProfessionEndpoint.cs
│   │   │   │   │   └── ListProfessions/
│   │   │   │   │       ├── ListProfessionsQuery.cs
│   │   │   │   │       ├── ListProfessionsQueryHandler.cs
│   │   │   │   │       └── ListProfessionsEndpoint.cs
│   │   │   │   └── Customers/
│   │   │   │       ├── UpdateCustomer/
│   │   │   │       │   ├── UpdateCustomerCommand.cs (güncellenecek)
│   │   │   │       │   ├── UpdateCustomerCommandHandler.cs (güncellenecek)
│   │   │   │       │   └── UpdateCustomerCommandValidator.cs (güncellenecek)
│   │   │   │       └── CreateCustomer/
│   │   │   │           ├── CreateCustomerCommand.cs (güncellenecek)
│   │   │   │           ├── CreateCustomerCommandHandler.cs (güncellenecek)
│   │   │   │           └── CreateCustomerCommandValidator.cs (güncellenecek)
│   │   │   └── Infrastructure/
│   │   │       ├── Data/
│   │   │       │   ├── ProfessionConfiguration.cs
│   │   │       │   └── CustomerConfiguration.cs (güncellenecek)
```

### API Endpoints

Meslek yönetimi için aşağıdaki API endpoint'leri eklenecektir:

```
GET /api/v1/customers/professions - Tüm meslekleri listeler
GET /api/v1/customers/professions/{id} - Belirli bir mesleği getirir
POST /api/v1/customers/professions - Yeni meslek oluşturur
PUT /api/v1/customers/professions/{id} - Mevcut bir mesleği günceller
DELETE /api/v1/customers/professions/{id} - Bir mesleği siler
```

### Kod Örnekleri

#### Domain Model

```csharp
// Modules/Customers/Application/Professions/Profession.cs
namespace MasterCRM.Modules.Customers.Application.Professions
{
    public class Profession
    {
        public Guid Id { get; set; }
        public string Name { get; set; }

        public DateTime InsertDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public Guid? InsertUserId { get; set; }
        public Guid? UpdateUserId { get; set; }
        public string History { get; set; }
    }
}
```

#### CreateProfessionCommand

```csharp
// Modules/Customers/Application/Professions/CreateProfession/CreateProfessionCommand.cs
namespace MasterCRM.Modules.Customers.Application.Professions.CreateProfession
{
    public class CreateProfessionCommand : IRequest<Result<Guid>>
    {
        public string Name { get; set; }
    }
}
```

#### CreateProfessionCommandHandler

```csharp
// Modules/Customers/Application/Professions/CreateProfession/CreateProfessionCommandHandler.cs
namespace MasterCRM.Modules.Customers.Application.Professions.CreateProfession
{
    public class CreateProfessionCommandHandler : IRequestHandler<CreateProfessionCommand, Result<Guid>>
    {
        private readonly ICustomersDbContext _dbContext;
        private readonly ICurrentUserService _currentUserService;

        public CreateProfessionCommandHandler(
            ICustomersDbContext dbContext,
            ICurrentUserService currentUserService)
        {
            _dbContext = dbContext;
            _currentUserService = currentUserService;
        }

        public async Task<Result<Guid>> Handle(CreateProfessionCommand request, CancellationToken cancellationToken)
        {
            var profession = new Profession
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                InsertDate = DateTime.Now,
                InsertUserId = _currentUserService.UserId
            };

            _dbContext.Professions.Add(profession);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result<Guid>.Success(profession.Id);
        }
    }
}
```

#### CreateProfessionEndpoint

```csharp
// Modules/Customers/Application/Professions/CreateProfession/CreateProfessionEndpoint.cs
namespace MasterCRM.Modules.Customers.Application.Professions.CreateProfession
{
    public class CreateProfessionEndpoint : EndpointBaseAsync
        .WithRequest<CreateProfessionCommand>
        .WithActionResult<Guid>
    {
        private readonly IMediator _mediator;

        public CreateProfessionEndpoint(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("/api/v1/customers/professions")]
        public override async Task<ActionResult<Guid>> HandleAsync(
            CreateProfessionCommand command,
            CancellationToken cancellationToken = default)
        {
            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
                return Ok(result.Value);

            return BadRequest(result.Error);
        }
    }
}
```

#### Entity Configuration

```csharp
// Modules/Customers/Infrastructure/Data/ProfessionConfiguration.cs
namespace MasterCRM.Modules.Customers.Infrastructure.Data
{
    public class ProfessionConfiguration : IEntityTypeConfiguration<Profession>
    {
        public void Configure(EntityTypeBuilder<Profession> builder)
        {
            builder.ToTable("Profession", "Customers");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.InsertDate)
                .IsRequired();
        }
    }
}
```

```csharp
// Modules/Customers/Infrastructure/Data/CustomerConfiguration.cs (güncellenecek)
namespace MasterCRM.Modules.Customers.Infrastructure.Data
{
    public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
    {
        public void Configure(EntityTypeBuilder<Customer> builder)
        {
            // Mevcut yapılandırmalar...

            // Profession ile ilişki
            builder.HasOne(c => c.Profession)
                .WithMany()
                .HasForeignKey(c => c.ProfessionId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
```

## Geçiş Planı

1. Profession tablosunun ve modelinin oluşturulması
2. Customer modelinin güncellenmesi ve migration'ların hazırlanması
3. API endpoint'lerinin ve iş mantığının uygulanması
4. Unit ve entegrasyon testlerinin yazılması
5. Değişikliklerin CI/CD pipeline'dan geçirilmesi

## Alternatif Yaklaşımların Değerlendirilmesi

1. **Sabit Enumeration Kullanımı**: Meslek bilgisi için sabit bir enumeration kullanılabilirdi, ancak bu yaklaşım yeni mesleklerin dinamik olarak eklenmesine olanak tanımaz.

2. **Ayrı Bir Microservice Olarak Tasarım**: Meslek yönetimi ayrı bir microservice olarak tasarlanabilirdi, ancak mevcut modüler monolitik mimari ile daha uyumlu olması ve karmaşıklığı azaltması için entegre edilmiş bir yaklaşım tercih edilmiştir.

## Güvenlik Değerlendirmesi

Bu değişiklik, mevcut güvenlik modeline önemli bir değişiklik getirmemektedir. Meslek verileri, diğer sistem verileri gibi yetkilendirme kontrollerine tabi olacaktır.

## Test Planı

1. **Unit Testler**: Komut ve sorgu işleyicileri için unit testler yazılacaktır.
2. **Entegrasyon Testleri**: API endpoint'leri için entegrasyon testleri yapılacaktır.
3. **Veritabanı Testleri**: Veritabanı işlemleri ve ilişkileri için test senaryoları hazırlanacaktır.

## Sonuç

Bu RFC, MasterCRM sistemine müşteri meslek yönetimi ekleyerek, müşteri veri modelini genişletmeyi ve müşteri segmentasyonu yeteneklerini geliştirmeyi amaçlamaktadır. Önerilen değişiklikler, sistemin mevcut mimarisine uygun ve kolayca entegre edilebilir olacak şekilde tasarlanmıştır.