# RFC 004: AutoDialer Management

## Abstract

<PERSON><PERSON>, MasterCRM sistemi içerisinde AutoDialer özelliğini tanımlar. AutoDialer, iş süreçlerini otomatize etmek ve müşteri iletişimini iyileştirmek amacıyla otomatik arama planları oluşturulmasını ve yönetilmesini sağlayan bir modüldür. 3CX telefon sistemi entegrasyonu kullanılarak, tanımlanan müşteri listelerine otomatik aramalar gerçekleştirilecektir.

## Motivasyon

MasterCRM platformunun hedeflerinden biri, müşteri ilişkileri yönetiminde verimliliği artırmaktır. AutoDialer modülü:

- Satış ve müşteri hizmetleri ekiplerinin zamanını optimize eder
- Toplu müşteri aramalarını otomatize eder
- <PERSON><PERSON> kampanyalarının yönetimini ve takibini kolaylaştırır
- Müşteri iletişimini sistematik hale getirir
- 3CX entegrasyonu ile telefon görüşmelerini merkezi bir sistemde yönetir

## Teknik Tasarım

### DbContext Konfigürasyonu

```csharp
// Conversations modülü içerisindeki AutoDialer entity'si için DbContext konfigürasyonu
public class AutoDialerConfiguration : IEntityTypeConfiguration<AutoDialer>
{
    public void Configure(EntityTypeBuilder<AutoDialer> builder)
    {
        builder.ToTable("AutoDialer", "Conversations");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.QueueNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.StartDate)
            .IsRequired();

        builder.Property(e => e.Active)
            .IsRequired();

        builder.Property(e => e.Status)
            .IsRequired();

        // JSON kolon yapılandırması
        builder.Property(e => e.TargetNumbers)
            .HasColumnType("json");
    }
}
```

### Entity Modeli

```csharp
public class AutoDialer : BaseEntity
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public bool Active { get; set; }
    public string QueueNumber { get; set; }
    public AutoDialerStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public List<string> TargetNumbers { get; set; }
}

public enum AutoDialerStatus
{
    Pending,
    InProgress,
    Completed,
    Cancelled
}
```

### Database Şeması

```sql
CREATE TABLE Conversations.AutoDialer (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    Name NVARCHAR(100) NOT NULL,
    Active BIT NOT NULL DEFAULT 0,
    QueueNumber NVARCHAR(20) NOT NULL,
    Status INT NOT NULL DEFAULT 0,
    StartDate DATETIME2 NOT NULL,
    TargetNumbers JSON NOT NULL -- JSON column type
);
```

### API Endpoints

| Method | Endpoint | İşlev |
|--------|----------|-------|
| GET | /api/v1/conversations/autodialers | Tüm AutoDialer kayıtlarını listeler |
| GET | /api/v1/conversations/autodialers/{id} | Belirli bir AutoDialer kaydını getirir |
| POST | /api/v1/conversations/autodialers | Yeni bir AutoDialer kaydı oluşturur |
| PUT | /api/v1/conversations/autodialers/{id} | Mevcut bir AutoDialer kaydını günceller |
| DELETE | /api/v1/conversations/autodialers/{id} | Bir AutoDialer kaydını siler |

### CQRS İmplementasyonu

Projenin mevcut yapısıyla uyumlu olarak, AutoDialer yönetimi için CQRS pattern kullanılacaktır. Vertical Slice Architecture'a uygun olarak her feature kendi klasörü içerisinde komut, sorgu, handler, validator ve endpoint sınıflarını barındıracaktır.

### Vertical Slice Architecture İmplementasyonu

Her bir özellik (feature) için Command/Query ve endpoint'ler aynı klasör altında organize edilecektir:

```
Conversations/
├── Application/
│   ├── AutoDialer/
│   │   ├── CreateAutoDialer/
│   │   │   ├── CreateAutoDialerCommand.cs
│   │   │   ├── CreateAutoDialerCommandHandler.cs
│   │   │   ├── CreateAutoDialerCommandValidator.cs
│   │   │   └── CreateAutoDialerEndpoint.cs
│   │   ├── UpdateAutoDialer/
│   │   │   ├── UpdateAutoDialerCommand.cs
│   │   │   ├── UpdateAutoDialerCommandHandler.cs
│   │   │   ├── UpdateAutoDialerCommandValidator.cs
│   │   │   └── UpdateAutoDialerEndpoint.cs
│   │   ├── DeleteAutoDialer/
│   │   │   ├── DeleteAutoDialerCommand.cs
│   │   │   ├── DeleteAutoDialerCommandHandler.cs
│   │   │   └── DeleteAutoDialerEndpoint.cs
│   │   ├── GetAutoDialer/
│   │   │   ├── GetAutoDialerQuery.cs
│   │   │   ├── GetAutoDialerQueryHandler.cs
│   │   │   └── GetAutoDialerEndpoint.cs
│   │   └── ListAutoDialers/
│   │       ├── ListAutoDialersQuery.cs
│   │       ├── ListAutoDialersQueryHandler.cs
│   │       └── ListAutoDialersEndpoint.cs
```

### Kodlama Örnekleri

#### CreateAutoDialerCommand.cs
```csharp
public class CreateAutoDialerCommand : IRequest<Result<Guid>>
{
    public string Name { get; set; }
    public bool Active { get; set; }
    public string QueueNumber { get; set; }
    public DateTime StartDate { get; set; }
    public List<string> TargetNumbers { get; set; }
}
```

#### CreateAutoDialerCommandHandler.cs
```csharp
public class CreateAutoDialerCommandHandler : IRequestHandler<CreateAutoDialerCommand, Result<Guid>>
{
    private readonly IApplicationDbContext _context;

    public CreateAutoDialerCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<Guid>> Handle(CreateAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = new AutoDialer
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            Active = request.Active,
            Status = AutoDialerStatus.Pending,
            QueueNumber = request.QueueNumber,
            StartDate = request.StartDate,
            TargetNumbers = request.TargetNumbers,
            InsertDate = DateTime.Now
        };

        _context.AutoDialers.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return Result<Guid>.Success(entity.Id);
    }
}
```

#### CreateAutoDialerCommandValidator.cs
```csharp
public class CreateAutoDialerCommandValidator : AbstractValidator<CreateAutoDialerCommand>
{
    public CreateAutoDialerCommandValidator()
    {
        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name must not exceed 100 characters.");

        RuleFor(v => v.QueueNumber)
            .NotEmpty().WithMessage("Queue Number is required.")
            .MaximumLength(20).WithMessage("Queue Number must not exceed 20 characters.");

        RuleFor(v => v.StartDate)
            .NotEmpty().WithMessage("Start Date is required.")
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Start Date must be today or in the future.");

        RuleFor(v => v.TargetNumbers)
            .NotEmpty().WithMessage("Target Numbers is required.")
            .Must(x => x != null && x.Count > 0).WithMessage("At least one target number is required.");
    }
}
```

#### CreateAutoDialerEndpoint.cs
```csharp
// Vertical Slice Architecture uygun şekilde
// Command ile aynı klasör içerisinde bulunacak
public class CreateAutoDialerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/autodialers", async (
            CreateAutoDialerCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/conversations/autodialers/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Conversations.Autodialers")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.Management");
    }
}
```

#### GetAutoDialerQuery.cs
```csharp
public class GetAutoDialerQuery : IRequest<Result<AutoDialerDto>>
{
    public Guid Id { get; set; }
}
```

#### GetAutoDialerQueryHandler.cs
```csharp
public class GetAutoDialerQueryHandler : IRequestHandler<GetAutoDialerQuery, Result<AutoDialerDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAutoDialerQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<AutoDialerDto>> Handle(GetAutoDialerQuery request, CancellationToken cancellationToken)
    {
        var entity = await _context.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (entity == null)
            return Result<AutoDialerDto>.Failure("AutoDialer not found.");

        var dto = new AutoDialerDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Active = entity.Active,
            Status = entity.Status,
            QueueNumber = entity.QueueNumber,
            StartDate = entity.StartDate,
            TargetNumbers = entity.TargetNumbers
        };

        return Result<AutoDialerDto>.Success(dto);
    }
}
```

### 3CX Entegrasyonu

AutoDialer modülü, MasterCRM'in 3CX telefon sistemi entegrasyonunu kullanarak aşağıdaki işlevleri gerçekleştirecektir:

1. 3CX API üzerinden QueueNumber'a otomatik arama talebi gönderilmesi
2. Arama durumlarının izlenmesi ve kaydedilmesi
3. Arama sonuçlarının raporlanması

Entegrasyon için aşağıdaki servisler oluşturulacaktır:

```csharp
public interface IThreeCXService
{
    Task<bool> InitiateAutoDialCall(string queueNumber, List<string> targetNumbers);
    Task<CallStatus> CheckCallStatus(string callId);
}
```

## Frontend Gereksinimleri

AutoDialer yönetimi için React kullanılarak aşağıdaki bileşenler geliştirilecektir:

1. AutoDialer Listeleme Sayfası
   - Tüm AutoDialer kayıtlarını gösterme
   - Filtreleme ve sıralama özellikleri
   - Active/Inactive durum gösterimi

2. AutoDialer Oluşturma/Düzenleme Formu
   - Temel bilgiler (Name, QueueNumber, StartDate, Active)
   - Hedef numara listesi yönetimi (ekleme, çıkarma, düzenleme)
   - JSON formatında hedef numaraların önizlemesi

3. AutoDialer Detay Sayfası
   - Genel bilgiler
   - Arama durumları ve istatistikler
   - Entegre raporlama

## Uygulama Planı

1. **Hafta 1: Backend Geliştirme**
   - Entity ve DB yapısının oluşturulması
   - Command ve Query sınıflarının geliştirilmesi
   - API endpoint'lerinin oluşturulması
   - Temel unit testlerin yazılması

2. **Hafta 2: 3CX Entegrasyonu**
   - 3CX API ile entegrasyon kodlarının yazılması
   - Arama işlemlerinin test edilmesi
   - Error handling mekanizmalarının geliştirilmesi

3. **Hafta 3: Frontend Geliştirme**
   - React bileşenlerinin oluşturulması
   - API entegrasyonunun sağlanması
   - Kullanıcı arayüzü testleri

## Başarı Kriterleri

1. AutoDialer kayıtlarının başarıyla oluşturulabilmesi, düzenlenebilmesi ve silinebilmesi
2. 3CX entegrasyonunun sorunsuz çalışması
3. Hedef numara listesinin JSON formatında doğru şekilde saklanması ve işlenmesi
4. UI/UX tasarımının kullanıcı dostu olması
5. API yanıt sürelerinin 200ms altında olması

## Güvenlik Değerlendirmesi

1. Telefon numaralarının güvenliği için:
   - Veritabanında şifreleme
   - API isteklerinde rate limiting
   - Kullanıcı yetkilendirme kontrolleri

2. AutoDialer işlemlerinin kötüye kullanılmaması için:
   - Her işlem için audit logging
   - Kullanıcı bazlı yetkilendirme
   - İşlem limitleri

## Riskler ve Azaltma Stratejileri

1. **3CX API değişiklikleri:**
   - Adapter pattern kullanarak entegrasyon kodunu izole etme
   - Düzenli 3CX API güncellemelerini takip etme

2. **Performans sorunları:**
   - Büyük hedef numara listeleri için batch processing
   - Caching stratejileri uygulama
   - Asenkron işleme mekanizmaları

3. **Güvenlik riskleri:**
   - Düzenli güvenlik testleri
   - Input validation kontrollerinin sıkılaştırılması
   - GDPR uyumluluğu için telefon numaralarının uygun şekilde işlenmesi

## Sonuç

AutoDialer modülü, MasterCRM sisteminin müşteri iletişim yönetimi yeteneklerini önemli ölçüde geliştirecektir. Modüler monolith mimarisi içerisinde, Conversations modülünün bir parçası olarak geliştirilerek, sistemin diğer bileşenleriyle uyumlu şekilde çalışacaktır.