# RFC-018: <PERSON><PERSON>önetim Si<PERSON>

## Status
Tamamlandı

## Özet
Bu RFC, MasterCRM sisteminde çoklu dil desteği sağlayacak temel dil yönetim sistemini tanımlar. Sistem, kullanıcı arayüzünde farklı dillerin kullanılabilmesini sağlayacak basit CRUD işlemlerini içerir.

## Motivasyon
- Uluslararası müşterilere hizmet veren işletmelerin farklı dillerde CRM kullanabilmesi
- Sistem genelinde tutarlı bir çoklu dil desteği sağlanması
- Yeni dillerin kolayca eklenebilmesi ve yönetilebilmesi

## Teknik Tasarım

### Veri Modeli

```csharp
public class Language : BaseEntity
{
    public string Code { get; private set; }  // ISO 639-1 kodu (tr, en, de, fr, vb.)
    public string Name { get; private set; }  // <PERSON>lin adı (Türkçe, English, Deutsch, vb.)
    public bool IsDefault { get; private set; }
    public bool IsActive { get; private set; }
    public string FlagIcon { get; private set; }  // Dil bayrağı için ikon
}
```

### Veritabanı Şeması

```sql
CREATE TABLE General.Language (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Code NVARCHAR(10) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    IsDefault BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    FlagIcon NVARCHAR(100),
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX)
);
```

### API Endpoints

```
# Dil Yönetimi CRUD İşlemleri
GET    /api/v1/general/languages - Tüm dilleri listeler
GET    /api/v1/general/languages/{id} - Belirli bir dili getirir
POST   /api/v1/general/languages - Yeni dil ekler
PUT    /api/v1/general/languages/{id} - Dil bilgilerini günceller
DELETE /api/v1/general/languages/{id} - Dili siler
PUT    /api/v1/general/languages/{id}/set-default - Varsayılan dili belirler
```

### CQRS Implementation

#### Commands

```csharp
// CreateLanguageCommand.cs
public class CreateLanguageCommand : IRequest<Result<Guid>>
{
    public string Code { get; set; }
    public string Name { get; set; }
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public string FlagIcon { get; set; }
}

// UpdateLanguageCommand.cs
public class UpdateLanguageCommand : IRequest<Result>
{
    public Guid Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public bool IsActive { get; set; }
    public string FlagIcon { get; set; }
}

// DeleteLanguageCommand.cs
public class DeleteLanguageCommand : IRequest<Result>
{
    public Guid Id { get; set; }
}

// SetDefaultLanguageCommand.cs
public class SetDefaultLanguageCommand : IRequest<Result>
{
    public Guid Id { get; set; }
}
```

#### Queries

```csharp
// GetLanguagesQuery.cs
public class GetLanguagesQuery : IRequest<Result<List<LanguageDto>>>
{
    public bool ActiveOnly { get; set; } = false;
}

// GetLanguageByIdQuery.cs
public class GetLanguageByIdQuery : IRequest<Result<LanguageDto>>
{
    public Guid Id { get; set; }
}
```

### Frontend Bileşenleri

1. Dil listesi tablosu
2. Dil ekleme/düzenleme formu
3. Dil silme onay modalı
4. Varsayılan dil belirleme butonu

## Uygulama Stratejisi

Dil yönetim sistemi aşağıdaki aşamalarda uygulanacaktır:

1. **Backend Geliştirme (3 gün)**
   - Veri modellerinin oluşturulması
   - Veritabanı şemasının oluşturulması
   - CQRS komut ve sorguların implementasyonu
   - API endpoint'lerinin oluşturulması

2. **Frontend Geliştirme (2 gün)**
   - Dil yönetim sayfasının oluşturulması
   - CRUD işlemleri için gerekli bileşenlerin geliştirilmesi
   - API entegrasyonu

## Örnek Kullanım Senaryoları

1. **Yeni Dil Ekleme**
   - Admin kullanıcı, dil yönetim sayfasına gider
   - "Yeni Dil Ekle" butonuna tıklar
   - Dil kodu, adı, aktif durumu ve bayrak ikonu bilgilerini girer
   - Kaydet butonuna tıklar ve yeni dil sisteme eklenir

2. **Dil Düzenleme**
   - Admin kullanıcı, dil listesinden düzenlemek istediği dili seçer
   - Düzenle butonuna tıklar
   - Dil bilgilerini günceller ve kaydet butonuna tıklar

3. **Dil Silme**
   - Admin kullanıcı, dil listesinden silmek istediği dili seçer
   - Sil butonuna tıklar
   - Onay modalında silme işlemini onaylar

4. **Varsayılan Dil Belirleme**
   - Admin kullanıcı, dil listesinden varsayılan yapmak istediği dili seçer
   - "Varsayılan Yap" butonuna tıklar
   - Sistem, seçilen dili varsayılan olarak işaretler ve diğer dillerin varsayılan durumunu kaldırır

## Sonuç

Bu RFC, MasterCRM sistemine temel çoklu dil desteği eklemek için gerekli CRUD işlemlerini tanımlamaktadır. Önerilen implementasyon, mevcut sistem mimarisi ve best practice'ler ile uyumlu olarak tasarlanmış olup, dil yönetimi için gerekli minimum fonksiyonaliteyi sağlamaktadır.