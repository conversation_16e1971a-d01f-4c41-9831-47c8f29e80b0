# RFC-019: <PERSON><PERSON><PERSON>, Eyalet ve Şehir Yönetim Sistemi

## Status
Tamamlandı

## Özet
Bu RFC, MasterCRM sisteminde ülke, eyalet ve şehir verilerinin hiyerarşik olarak yönetilmesini sağlayacak altyapıyı tanımlar. Sistem, adres bilgilerinin standartlaştırılması, coğrafi bazlı raporlama ve filtreleme işlemleri için temel oluşturacaktır.

## Motivasyon
- Müşteri adres bilgilerinin standartlaştırılması
- Coğrafi bazlı müşteri segmentasyonu yapılabilmesi
- Bölgesel raporlama ve analiz imkanı sağlanması
- Satış ve pazarlama stratejilerinin bölgesel olarak planlanabilmesi
- Adres doğrulama süreçlerinin iyileştirilmesi

## Teknik Tasarım

### Veri Modeli

```csharp
public class Country : BaseAuditableEntity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Code { get; private set; }  // ISO 3166-1 alpha-2 kodu (TR, US, DE vb.)
    public string PhoneCode { get; private set; }  // Ülke telefon kodu (+90, +1 vb.)
    public bool IsActive { get; private set; }

    // Navigation property
    public virtual ICollection<State> States { get; private set; } = new List<State>();
}

public class State : BaseAuditableEntity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Code { get; private set; }  // Eyalet/Bölge kodu
    public Guid CountryId { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation properties
    public virtual Country Country { get; private set; }
    public virtual ICollection<City> Cities { get; private set; } = new List<City>();
}

public class City : BaseAuditableEntity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public Guid StateId { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation property
    public virtual State State { get; private set; }
}
```

### Veritabanı Şeması

```sql
CREATE TABLE General.Country (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Code NVARCHAR(10) NOT NULL,
    PhoneCode NVARCHAR(10),
    IsActive BIT NOT NULL DEFAULT 1,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX)
);

CREATE TABLE General.State (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Code NVARCHAR(10),
    CountryId UNIQUEIDENTIFIER NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    CONSTRAINT FK_State_Country FOREIGN KEY (CountryId) REFERENCES General.Country(Id)
);

CREATE TABLE General.City (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    StateId UNIQUEIDENTIFIER NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    CONSTRAINT FK_City_State FOREIGN KEY (StateId) REFERENCES General.State(Id)
);

-- İndeksler
CREATE INDEX IX_Country_Code ON General.Country(Code);
CREATE INDEX IX_State_CountryId ON General.State(CountryId);
CREATE INDEX IX_State_Code ON General.State(Code);
CREATE INDEX IX_City_StateId ON General.City(StateId);
```

### API Endpoints

```
# Ülke Yönetimi
GET    /api/v1/general/countries - Tüm ülkeleri listeler
GET    /api/v1/general/countries/{id} - Belirli bir ülkeyi getirir
POST   /api/v1/general/countries - Yeni ülke ekler
PUT    /api/v1/general/countries/{id} - Ülke bilgilerini günceller
DELETE /api/v1/general/countries/{id} - Ülkeyi siler

# Eyalet/Bölge Yönetimi
GET    /api/v1/general/countries/states - Tüm eyaletleri listeler
GET    /api/v1/general/countries/{countryId}/states - Ülkeye ait eyaletleri listeler
GET    /api/v1/general/states/{id} - Belirli bir eyaleti getirir
POST   /api/v1/general/countries/{countryId}/states - Yeni eyalet ekler
PUT    /api/v1/general/states/{id} - Eyalet bilgilerini günceller
DELETE /api/v1/general/states/{id} - Eyaleti siler

# Şehir Yönetimi
GET    /api/v1/general/states/cities - Tüm şehirleri listeler
GET    /api/v1/general/states/{stateId}/cities - Eyalete ait şehirleri listeler
GET    /api/v1/general/cities/{id} - Belirli bir şehri getirir
POST   /api/v1/general/states/{stateId}/cities - Yeni şehir ekler
PUT    /api/v1/general/cities/{id} - Şehir bilgilerini günceller
DELETE /api/v1/general/cities/{id} - Şehri siler
```

### CQRS Implementation

#### Commands
```csharp
// src/Modules/General/Application/CountryManagement/Commands/CreateCountry/CreateCountryCommand.cs
public class CreateCountryCommand : IRequest<Result<Guid>>
{
    public string Name { get; set; }
    public string Code { get; set; }
    public string PhoneCode { get; set; }
    public bool IsActive { get; set; } = true;
}

// src/Modules/General/Application/CountryManagement/Commands/CreateCountry/CreateCountryCommandHandler.cs
public class CreateCountryCommandHandler : IRequestHandler<CreateCountryCommand, Result<Guid>>
{
    private readonly IGeneralDbContext _context;

    public CreateCountryCommandHandler(IGeneralDbContext context)
    {
        _context = context;
    }

    public async Task<Result<Guid>> Handle(CreateCountryCommand request, CancellationToken cancellationToken)
    {
        var entity = new Country
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            Code = request.Code,
            PhoneCode = request.PhoneCode,
            IsActive = request.IsActive
        };

        _context.Countries.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return Result<Guid>.Success(entity.Id);
    }
}
```

#### Queries
```csharp
// src/Modules/General/Application/CountryManagement/Queries/GetCountries/GetCountriesQuery.cs
public class GetCountriesQuery : IRequest<Result<List<CountryDto>>>
{
    public bool? IsActive { get; set; }
}

// src/Modules/General/Application/CountryManagement/Queries/GetCountries/GetCountriesQueryHandler.cs
public class GetCountriesQueryHandler : IRequestHandler<GetCountriesQuery, Result<List<CountryDto>>>
{
    private readonly IGeneralDbContext _context;
    private readonly IMapper _mapper;

    public GetCountriesQueryHandler(IGeneralDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<Result<List<CountryDto>>> Handle(GetCountriesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Countries.AsQueryable();

        if (request.IsActive.HasValue)
        {
            query = query.Where(x => x.IsActive == request.IsActive.Value);
        }

        var countries = await query
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);

        return Result<List<CountryDto>>.Success(_mapper.Map<List<CountryDto>>(countries));
    }
}
```

## Uygulama Stratejisi

Ülke, eyalet ve şehir yönetim sistemi aşağıdaki aşamalarda uygulanacaktır:

1. **Veritabanı Yapısının Oluşturulması (1 gün)**
   - Tabloların oluşturulması
   - İndekslerin eklenmesi
   - Temel veri setinin hazırlanması (popüler ülkeler ve eyaletler)

2. **Backend Geliştirme (3 gün)**
   - Domain modellerinin oluşturulması
   - CQRS komut ve sorguların implementasyonu
   - API endpoint'lerinin oluşturulması
   - Validasyon kurallarının tanımlanması

3. **Entegrasyon ve Test (2 gün)**
   - Müşteri adres yönetimi ile entegrasyon
   - Unit ve integration testlerin yazılması
   - Performans testleri

4. **Dokümantasyon ve Deployment (1 gün)**
   - API dokümantasyonu
   - Kullanım kılavuzu
   - Deployment ve release

Toplam süre: 7 gün

## Alternatif Değerlendirmeleri

### Hazır Veri Servisi Kullanımı
- Üçüncü parti bir coğrafi veri servisi kullanılabilir (Google Places API, vb.)
- Avantaj: Güncel ve doğru veri
- Dezavantaj: Dış bağımlılık ve maliyet
- Karar: İlk aşamada kendi veritabanımızı kullanacağız, ileride entegrasyon düşünülebilir

### NoSQL Yaklaşımı
- Hiyerarşik veri yapısı için NoSQL veritabanı kullanılabilir
- Avantaj: Esnek şema, hiyerarşik veri için daha uygun
- Dezavantaj: Mevcut SQL Server altyapısından farklı bir teknoloji
- Karar: Mevcut SQL Server altyapısı ile devam edilecek

## Performans Değerlendirmesi
- Ülke, eyalet ve şehir verileri için caching mekanizması kullanılacak
- Sık kullanılan sorgular için özel indeksler oluşturulacak
- Büyük veri setleri için sayfalama (paging) implementasyonu yapılacak

## Güvenlik Değerlendirmesi
- Tüm API endpoint'leri için yetkilendirme kontrolleri uygulanacak
- Veri manipülasyonu işlemleri (ekleme, güncelleme, silme) için audit logging yapılacak
- Input validation ile injection saldırılarına karşı koruma sağlanacak

## Sonuç
Bu RFC, MasterCRM sisteminde ülke, eyalet ve şehir verilerinin hiyerarşik olarak yönetilmesini sağlayacak altyapıyı tanımlamaktadır. Bu yapı, adres bilgilerinin standartlaştırılması, coğrafi bazlı raporlama ve filtreleme işlemleri için temel oluşturacaktır. Modüler monolith mimarisi ve CQRS pattern'i kullanılarak, coğrafi verilerin etkin bir şekilde yönetilmesini sağlayacak API'ler oluşturulacaktır.