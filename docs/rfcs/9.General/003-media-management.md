# RFC 003: <PERSON><PERSON><PERSON>önetim Modülü

## Özet

Bu RFC, MasterCRM için dosya ve klasör işlemlerini etkinleştirecek bir Medya Yönetim modülünün uygulanmasını önermektedir. <PERSON><PERSON><PERSON><PERSON>, "Uploads" dizin yapısı içinde dosya yükleme, indirme, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, listeleme, silme ve klasör organizasyonu işlemlerini yönetecektir.

## Motivasyon

MasterCRM, modüller arasında (müşteriler, ticket'lar, görevler vb.) çeşitli varlıklarla ilişkilendirilebilecek medya varlıklarını yönetmek için merkezi bir sisteme ihtiyaç duymaktadır. Önerilen Medya Yönetim modülü:

- Güvenli dosya depolama ve erişim sağlayacak
- Daha iyi organizasyon için hiyerarşik klasör yapısı sunacak
- Ortak bir arayüz aracılığıyla diğer modüllerle sorunsuz bir şekilde entegre olacak
- Dosya yönetimi için temel işlemleri (yükleme, indirme, listeleme, silme) sunacak
- Klasör yönetimi için temel işlemleri (oluşturma, güncelleme, silme, listeleme) sunacak

## Teknik Tasarım

### Modül Yapısı

Medya Yönetim modülü, MasterCRM'nin modüler monolitik mimarisini takip edecek ve aşağıdaki yapıyla organize edilecektir:

```
MasterCRM/
├── src/
│   ├── Modules/
│   │   ├── General/
│   │   │   ├── Application/
│   │   │   │   ├── Files/
│   │   │   │   │   ├── UploadFile/
│   │   │   │   │   │   ├── UploadFileCommand.cs
│   │   │   │   │   │   ├── UploadFileCommandHandler.cs
│   │   │   │   │   │   ├── UploadFileCommandValidator.cs
│   │   │   │   │   │   └── UploadFileEndpoint.cs
│   │   │   │   │   ├── DeleteFile/
│   │   │   │   │   │   ├── DeleteFileCommand.cs
│   │   │   │   │   │   ├── DeleteFileCommandHandler.cs
│   │   │   │   │   │   └── DeleteFileEndpoint.cs
│   │   │   │   │   ├── GetFile/
│   │   │   │   │   │   ├── GetFileQuery.cs
│   │   │   │   │   │   ├── GetFileQueryHandler.cs
│   │   │   │   │   │   └── GetFileEndpoint.cs
│   │   │   │   │   └── ListFiles/
│   │   │   │   │       ├── ListFilesQuery.cs
│   │   │   │   │       ├── ListFilesQueryHandler.cs
│   │   │   │   │       └── ListFilesEndpoint.cs
│   │   │   │   ├── Folders/
│   │   │   │   │   ├── CreateFolder/
│   │   │   │   │   │   ├── CreateFolderCommand.cs
│   │   │   │   │   │   ├── CreateFolderCommandHandler.cs
│   │   │   │   │   │   ├── CreateFolderCommandValidator.cs
│   │   │   │   │   │   └── CreateFolderEndpoint.cs
│   │   │   │   │   ├── DeleteFolder/
│   │   │   │   │   │   ├── DeleteFolderCommand.cs
│   │   │   │   │   │   ├── DeleteFolderCommandHandler.cs
│   │   │   │   │   │   └── DeleteFolderEndpoint.cs
│   │   │   │   │   ├── UpdateFolder/
│   │   │   │   │   │   ├── UpdateFolderCommand.cs
│   │   │   │   │   │   ├── UpdateFolderCommandHandler.cs
│   │   │   │   │   │   ├── UpdateFolderCommandValidator.cs
│   │   │   │   │   │   └── UpdateFolderEndpoint.cs
│   │   │   │   │   └── ListFolders/
│   │   │   │   │       ├── ListFoldersQuery.cs
│   │   │   │   │       ├── ListFoldersQueryHandler.cs
│   │   │   │   │       └── ListFoldersEndpoint.cs
│   │   │   │   └── Shared/
│   │   │   │       ├── DTOs/
│   │   │   │       │   ├── FileDto.cs
│   │   │   │       │   └── FolderDto.cs
│   │   │   │       └── Events/
│   │   │   │           ├── FileUploadedEvent.cs
│   │   │   │           └── FolderCreatedEvent.cs
│   │   │   ├── Domain/
│   │   │   │   ├── File.cs
│   │   │   │   └── Folder.cs
│   │   │   └── Infrastructure/
│   │   │       ├── Data/
│   │   │       │   ├── Configurations/
│   │   │       │   │   ├── FileConfiguration.cs
│   │   │       │   │   └── FolderConfiguration.cs
│   │   │       │   ├── Repositories/
│   │   │       │   │   ├── FileRepository.cs
│   │   │       │   │   └── FolderRepository.cs
│   │   │       │   └── GeneralDbContext.cs
│   │   │       └── Services/
│   │   │           └── StorageService.cs
└── ... (diğer modüller ve klasörler)
```

### Veritabanı Şeması

```sql
-- Folders Table
CREATE TABLE General.Folder (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    Name NVARCHAR(255) NOT NULL,
    ParentFolderId UNIQUEIDENTIFIER NULL,
    Path NVARCHAR(1000) NOT NULL,
    AttributeData NVARCHAR(MAX),
    CONSTRAINT FK_Folder_ParentFolder FOREIGN KEY (ParentFolderId)
    REFERENCES General.Folder(Id)
);

-- Files Table
CREATE TABLE General.File (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FileExtension NVARCHAR(20) NOT NULL,
    MimeType NVARCHAR(100) NOT NULL,
    FileSizeInBytes BIGINT NOT NULL,
    FolderId UNIQUEIDENTIFIER NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    AttributeData NVARCHAR(MAX),
    CONSTRAINT FK_File_Folder FOREIGN KEY (FolderId)
    REFERENCES General.Folder(Id)
);
```

### API Endpoints

```
-- Dosya İşlemleri
POST /api/v1/general/files                  // Dosya yükleme
GET /api/v1/general/files/{id}              // Dosya indirme/görüntüleme
GET /api/v1/general/files                   // Dosya listeleme (filtreleme ile)
DELETE /api/v1/general/files/{id}           // Dosya silme
PUT /api/v1/general/files/{id}/move         // Dosyayı taşıma
POST /api/v1/general/files/batch            // Toplu dosya yükleme

-- Klasör İşlemleri
POST /api/v1/general/folders                // Klasör oluşturma
GET /api/v1/general/folders/{id}            // Klasör detayları
GET /api/v1/general/folders                 // Klasör listeleme (ağaç yapısı)
PUT /api/v1/general/folders/{id}            // Klasör güncelleme
DELETE /api/v1/general/folders/{id}         // Klasör silme
GET /api/v1/general/folders/{id}/content    // Klasör içeriğini listeleme (alt klasörler ve dosyalar)
```

### Domain Modeli

#### Dosya Varlığı (File Entity)

```csharp
public class File : BaseEntity
{
    public string FileName { get; private set; }
    public string OriginalFileName { get; private set; }
    public string FileExtension { get; private set; }
    public string MimeType { get; private set; }
    public long FileSizeInBytes { get; private set; }
    public Guid FolderId { get; private set; }
    public virtual Folder Folder { get; private set; }
    public string StoragePath { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Entity metodları...
}
```

#### Klasör Varlığı (Folder Entity)

```csharp
public class Folder : BaseEntity
{
    public string Name { get; private set; }
    public Guid? ParentFolderId { get; private set; }
    public virtual Folder ParentFolder { get; private set; }
    public string Path { get; private set; }
    public Dictionary<string, object> Attributes { get; private set; }
    public virtual ICollection<Folder> SubFolders { get; private set; }
    public virtual ICollection<File> Files { get; private set; }

    // Entity metodları...
}
```

## Fonksiyonel Gereksinimler

### Dosya İşlemleri

1. **Dosya Yükleme**
   - Tek veya çoklu dosya yükleme desteği
   - Seçilen klasöre orijinal dosya adıyla kaydetme
   - Aynı isimli dosya varsa otomatik yeniden adlandırma (örn. dosya(1).ext)
   - Temel dosya metadatası çıkarma (boyut, mime type)

2. **Dosya Görüntüleme/İndirme**
   - Güvenli URL oluşturma
   - Yetkilendirilmiş erişim

3. **Dosya Listeleme**
   - Sayfalama, sıralama ve filtreleme desteği
   - Klasör hiyerarşisine göre listeleme
   - İsim bazlı arama

4. **Dosya Silme**
   - Veritabanı kaydını silme
   - Fiziksel dosyayı silme

### Klasör İşlemleri

1. **Klasör Oluşturma**
   - Hiyerarşik yapı desteği
   - İç içe klasör yapısı
   - Otomatik path oluşturma

2. **Klasör Güncelleme**
   - İsim değiştirme
   - Taşıma/yeniden organize etme

3. **Klasör Listeleme**
   - Ağaç yapısı görünümü
   - Alt klasörler ve dosyaları listeleme
   - Recursive listeleme seçeneği

4. **Klasör Silme**
   - Recursive silme seçeneği
   - İçindeki dosyaları silme

## Dosya Depolama Stratejisi

Tüm dosyalar "Uploads" klasörü içerisinde, kullanıcının seçtiği klasör hiyerarşisinde saklanacaktır. Dosyalar seçilen klasöre doğrudan orijinal dosya adıyla kaydedilecektir. Tüm dosyalar private olacak ve sadece yetkili kullanıcılar tarafından erişilebilecektir.

## Uygulama Stratejisi

Medya Yönetim modülü aşağıdaki fazlarda gerçekleştirilecektir:

### Faz 1: Temel Altyapı (1 hafta)
- Veritabanı şeması oluşturma
- Domain modellerini tanımlama
- Repository katmanı implementasyonu
- Storage service implementasyonu

### Faz 2: Dosya İşlemleri (1.5 hafta)
- Dosya yükleme/indirme işlevselliği
- Dosya listeleme ve arama
- Dosya silme

### Faz 3: Klasör İşlemleri (1.5 hafta)
- Klasör CRUD işlemleri
- Klasör hiyerarşisi yönetimi
- Path optimizasyonu

### Faz 4: Entegrasyonlar (1 hafta)
- Diğer modüllerle entegrasyon
- Event-driven iletişim implementasyonu

## Başarı Kriterleri

- Dosya yükleme/indirme performansı < 1 saniye (5MB'a kadar dosyalar için)
- Klasör listeleme performansı < 200ms (100 alt öğeye kadar)
- İş süreçlerindeki manuel dosya yönetimi ihtiyacını ortadan kaldırma

## Riskler ve Önlemler

1. **Depolama Alanı Riskleri**:
   - Risk: Disk alanının hızlı dolması
   - Önlem: Düzenli temizlik işlemleri, kullanılmayan dosyaların silinmesi

2. **Performans Riskleri**:
   - Risk: Büyük klasörlerde listeleme performans sorunları
   - Önlem: Sayfalama, lazy loading, index optimizasyonu

## Bağımlılıklar

- Kullanıcı ve Yetkilendirme modülü
- Entity yapısı için Shared Domain modelleri

## Gelecek Geliştirmeler

- Dosya önizleme özelliği
- Dosya arama ve filtreleme iyileştirmeleri
- Sürükle-bırak dosya taşıma desteği
- Dosya paylaşım özellikleri